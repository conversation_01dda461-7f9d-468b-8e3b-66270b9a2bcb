version: '3.8'

services:
  gpt-engine:
    build:
      context: .
      dockerfile: src/Mysoft.GPTEngine.WebApplication/Dockerfile
    container_name: mysoft-gpt-engine
    ports:
      - "5000:80"  # 映射宿主机5000端口到容器80端口
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://0.0.0.0:80
      # 根据需要添加其他环境变量
      - GPT_BUILDER_PATH=http://modelingplatform:9300/
      - ASPNETCORE_HOSTINGSTARTUPASSEMBLIES=FastTracker.Agent.AspNetCore;Mysoft.GPTEngine.Diagnostics.Extend
      - FastTracker_Enable=True
      - FastTracker_ServiceName=gpt-engine
      - FastTracker_Logging_Level=INFO
      - FastTracker_Logging_FilePath=logs/fast-tracker/
      - LOG_LEVEL=Debug
      - PYTHONNET_PYDLL=PythonLibrary/python310.dll
    volumes:
      # 如果需要持久化日志，可以挂载日志目录
      - ./logs:/app/logs
    networks:
      - gpt-engine-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  gpt-engine-network:
    driver: bridge

volumes:
  gpt-engine-logs:
    driver: local

using System;
using System.Collections.Generic;

namespace MarkdownParsingTest
{
    class Program
    {
        static void Main(string[] args)
        {
            // 测试原始 markdown 格式
            string testMarkdown = @"## 下拉筛选-1

| 公司 | 公司名称 |
|---|---|
| 深圳公司 | 深圳公司 |
## 业绩

| 来访数 | 复访数 | 认筹数 | 认筹金额 | 认购套数 | 认购金额 | 筹转认套数 | 筹转认金额 | 净认购套数 | 净认购金额 | 签约套数 | 签约金额 |
|---|---|---|---|---|---|---|---|---|---|---|---|
| 1.046万 | 0.0522万 | 0.0622万 | 134473万 | 0.0654万 | 156446万 | 0.0589万 | 128976万 | 0.0065万 | 27471万 | 0.0793万 | 199233万 |";

            Console.WriteLine("原始 Markdown 内容:");
            Console.WriteLine(testMarkdown);
            Console.WriteLine();

            // 创建一个测试实例来调用解析方法
            var testInstance = new TestDataKnowledgeImporter();
            var result = testInstance.TestParseMarkdownToDataItems(testMarkdown);

            Console.WriteLine("解析结果:");
            for (int i = 0; i < result.Count; i++)
            {
                Console.WriteLine($"数据项 {i + 1}:");
                Console.WriteLine($"  标题: {result[i].Title}");
                Console.WriteLine($"  内容: {result[i].Content}");
                Console.WriteLine();
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }

    // 数据项模型
    public class DataItem
    {
        public string Title { get; set; }
        public string Content { get; set; }
    }

    // 测试类，用于访问私有方法
    public class TestDataKnowledgeImporter
    {
        public List<DataItem> TestParseMarkdownToDataItems(string markdownContent)
        {
            var dataItems = new List<DataItem>();

            if (string.IsNullOrWhiteSpace(markdownContent))
            {
                return dataItems;
            }

            try
            {
                // 按标题分割 markdown 内容
                var sections = markdownContent.Split(new string[] { "\n## " }, StringSplitOptions.RemoveEmptyEntries);

                for (int i = 0; i < sections.Length; i++)
                {
                    var section = sections[i];

                    // 处理第一个部分（可能不以 ## 开头）
                    if (i == 0 && !section.StartsWith("## "))
                    {
                        if (section.StartsWith("# "))
                        {
                            // 移除 # 前缀
                            section = section.Substring(2);
                        }
                        else if (!section.Contains("\n"))
                        {
                            // 如果第一部分没有换行符，可能是标题
                            continue;
                        }
                    }
                    else if (i > 0)
                    {
                        // 为非第一个部分添加 ## 前缀
                        section = "## " + section;
                    }

                    // 查找第一个换行符，分离标题和内容
                    var firstNewlineIndex = section.IndexOf('\n');
                    if (firstNewlineIndex > 0)
                    {
                        var title = section.Substring(0, firstNewlineIndex).Trim();
                        var content = section.Substring(firstNewlineIndex + 1).Trim();

                        // 移除标题中的 # 符号
                        title = title.TrimStart('#').Trim();

                        if (!string.IsNullOrWhiteSpace(title) && !string.IsNullOrWhiteSpace(content))
                        {
                            dataItems.Add(new DataItem
                            {
                                Title = title,
                                Content = content
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析 Markdown 内容时发生异常: {ex.Message}");
            }

            return dataItems;
        }
    }
}

# AgentKnowledgePlugin 重构说明

## 重构概述

本次重构将 `AgentKnowledgePlugin` 和 `AddKnowLedgePlugin` 方法进行了重新设计，参考了 `DataPlatformToolsImporter` 的实现模式，实现了以下目标：

1. **直接传入Knowledge的id数值**：不再需要在描述中显式规定knowledgeCode
2. **生成多个对应的工具**：每个知识库生成一个独立的工具函数
3. **自动绑定knowledgeCode**：工具函数内部自动使用对应的knowledgeCode，用户只需传入问题

## 重构前后对比

### 重构前 (AgentKnowledgePlugin)

```csharp
[KernelFunction]
[Description("知识库问答，参数为知识库编码和问题，问题自动向量化后检索相关内容，返回最相关的知识片段文本（Top1）")]
public async Task<string> QueryKnowledgeAsync(string knowledeCode, string question)
{
    // 需要用户手动传入knowledgeCode和question
    // 工具描述中需要显式说明使用哪个knowledgeCode
}
```

**问题：**
- 用户需要知道具体的knowledgeCode
- 工具描述中需要硬编码knowledgeCode
- 一个通用方法处理所有知识库，容易混淆

### 重构后 (KnowledgeToolsImporter)

```csharp
// 为每个知识库生成独立的工具函数
string functionName = $"Query{knowledge.Name}Knowledge";
string description = $"{knowledge.Name}知识库问答工具。{knowledge.Description}。输入问题后自动检索相关内容并返回最相关的知识片段。";

// 工具函数内部自动绑定knowledgeCode
async Task<string> QueryKnowledgeFunc(KernelArguments arguments, CancellationToken ct)
{
    var question = arguments.TryGetValue("question", out var q) ? q?.ToString() : "";
    return await QueryKnowledge(knowledge.Code, question, ct); // 自动使用对应的knowledgeCode
}
```

**优势：**
- 用户只需传入问题，无需知道knowledgeCode
- 每个知识库有独立的工具，语义更清晰
- 工具名称和描述自动生成，包含知识库名称和描述

## 实现细节

### 1. KnowledgeToolsImporter 类

新创建的 `KnowledgeToolsImporter` 类负责：
- 接收知识库列表
- 为每个知识库生成独立的工具函数
- 处理工具名称长度限制（最大64字符）
- 自动去重相同ID的知识库

### 2. 工具函数生成

```csharp
foreach (var knowledge in uniqueKnowledges)
{
    string functionName = $"Query{knowledge.Name}Knowledge";
    string description = $"{knowledge.Name}知识库问答工具。{knowledge.Description}。输入问题后自动检索相关内容并返回最相关的知识片段。";
    
    // 确保工具名称不超过64个字符
    var truncatedName = TruncateToolName(functionName);
    
    var parameters = new List<KernelParameterMetadata>
    {
        new KernelParameterMetadata("question")
        {
            Description = "要查询的问题"
        }
    };
    
    functions.Add(KernelFunctionFactory.CreateFromMethod(
        method: (Func<KernelArguments, CancellationToken, Task<string>>)QueryKnowledgeFunc,
        functionName: truncatedName,
        description: description,
        parameters: parameters
    ));
}
```

### 3. AddKnowLedgePlugin 方法简化

```csharp
public async Task AddKnowLedgePlugin(Kernel kernel)
{
    var agent = _chatRunDto.Agent;
    if (agent == null || agent.Knowledgs == null)
    {
        return;
    }

    await new KnowledgeToolsImporter(kernel, _serviceProvider).ImportKnowledgeTools(agent.Knowledgs);
}
```

## 使用示例

### 重构前的使用方式

```
工具名称: query12345 (随机生成)
工具描述: 测试知识库工具。这是一个测试知识库参数为知识库编码和问题，入参中的knowledeCode请使用KB001
参数: knowledeCode, question
用户需要传入: knowledeCode="KB001", question="什么是人工智能？"
```

### 重构后的使用方式

```
工具名称: Query测试知识库Knowledge
工具描述: 测试知识库知识库问答工具。这是一个测试知识库。输入问题后自动检索相关内容并返回最相关的知识片段。
参数: question
用户只需传入: question="什么是人工智能？"
```

## 优势总结

1. **用户体验提升**：用户无需了解内部的knowledgeCode，只需要知道要查询哪个知识库
2. **工具语义清晰**：每个工具名称直接包含知识库名称，一目了然
3. **减少错误**：避免用户传入错误的knowledgeCode
4. **代码维护性**：参考成熟的DataPlatformToolsImporter模式，代码结构更清晰
5. **自动化程度高**：工具名称、描述、参数绑定都是自动生成的

## TopK配置参数增强

### 新增功能

在原有重构基础上，进一步增加了topk配置参数支持：

1. **新增topk参数**：
   - 参数名称：`topk`
   - 默认值：5
   - 参数描述：返回结果数量，默认5条
   - 取值范围：1-20（自动限制范围，避免返回过多结果）

2. **增强的返回格式**：
   ```
   检索到 3 条相关知识片段：

   [相似度: 0.856]
   这是第一条知识片段的内容...

   ---

   [相似度: 0.742]
   这是第二条知识片段的内容...

   ---

   [相似度: 0.689]
   这是第三条知识片段的内容...
   ```

3. **智能参数处理**：
   - 自动解析topk参数，无效值时使用默认值5
   - 限制topk范围在1-20之间，防止性能问题
   - 按相似度排序返回结果

### 使用示例对比

#### 重构前
```
工具名称: query12345
参数: knowledeCode="KB001", question="什么是人工智能？"
返回: 单条知识片段内容
```

#### 重构后（增加topk支持）
```
工具名称: Query测试知识库Knowledge
参数: question="什么是人工智能？", topk=3
返回: 3条按相似度排序的知识片段，包含相似度信息
```

### 技术实现细节

<augment_code_snippet path="src/Mysoft.GPTEngine.Domain/AgentSkillEnhancement/AgentKnowledgePlugin.cs" mode="EXCERPT">
```csharp
var parameters = new List<KernelParameterMetadata>
{
    new KernelParameterMetadata("question")
    {
        Description = "要查询的问题"
    },
    new KernelParameterMetadata("topk")
    {
        Description = "返回结果数量，默认5条",
        DefaultValue = "5"
    }
};
```
</augment_code_snippet>

## 兼容性说明

- 重构后的实现完全替换了原有的AgentKnowledgePlugin类
- AddKnowLedgePlugin方法的调用方式保持不变
- 对外接口保持兼容，不影响现有的调用代码
- 新增的topk参数为可选参数，不传入时使用默认值5

## 测试验证

创建了 `KnowledgeToolsImporterTests` 测试类来验证：
- 正确生成工具数量
- 工具名称正确性
- 重复ID去重功能
- 空列表处理
- TopK参数处理和范围限制

# 工具调用历史消息功能更新

## 概述

本次更新为历史消息功能增加了工具调用的支持，通过Semantic Kernel的自动工具调用机制，使AI能够更好地理解完整的对话上下文。

## 更新内容

### 1. **Semantic Kernel 工具调用集成**

#### 修改内容
- 利用Semantic Kernel的自动工具调用功能
- 工具调用和工具返回由Semantic Kernel自动处理
- 历史记录只保存用户输入和最终AI回复，保持简洁

#### 实现原理
Semantic Kernel会自动：
1. 检测需要工具调用的情况
2. 调用相应的工具函数
3. 将工具返回结果传递给AI
4. 生成最终的回复给用户

### 2. **ConversationMemoryService 更新**

#### 核心改进
- 会话级别隔离：使用 `userId + chatGuid` 组合进行精确的会话隔离
- 智能消息过滤：自动跳过工具调用相关的临时消息
- 简化存储策略：只保存用户输入和最终AI回复

#### ChatHistory加载逻辑
```csharp
switch (message.Role)
{
    case ChatRoleConstant.User:
        chatHistory.AddUserMessage(message.Content);
        break;
    case ChatRoleConstant.Assistant:
        // 跳过工具调用消息，让Semantic Kernel自动处理
        if (!message.Content.StartsWith("[TOOL_CALL]"))
        {
            chatHistory.AddAssistantMessage(message.Content);
        }
        break;
    case ChatRoleConstant.Tool:
        // 跳过工具返回消息，避免格式冲突
        continue;
}
```

### 3. **AgentSkillDomainService 更新**

#### 智能消息保存
- 自动检测是否包含工具调用
- 如果有工具调用，保存完整的对话流程
- 如果没有工具调用，使用原来的简单保存方式

#### 消息构造逻辑
```csharp
private List<ChatMessageDto> GetNewConversationMessages(string userInput, string assistantOutput)
{
    // 1. 添加用户消息
    // 2. 检查并添加工具调用消息
    // 3. 添加工具返回消息
    // 4. 添加最终助手回复
}
```

### 4. **对话轮次计算优化**

#### 新的轮次计算逻辑
- 以用户消息为一轮对话的开始标志
- 支持一轮对话中包含多个工具调用
- 智能保留最近N轮完整对话

#### 示例对话流程
```
轮次1:
- User: "帮我查询今天的天气"
- Assistant: [TOOL_CALL] {"function_name":"WeatherPlugin.GetWeather","arguments":{"location":"北京"}}
- Tool: [TOOL_RESULT:WeatherPlugin.GetWeather] {"temperature":25,"condition":"晴天"}
- Assistant: "今天北京的天气是晴天，温度25度"

轮次2:
- User: "那明天呢？"
- Assistant: [TOOL_CALL] {"function_name":"WeatherPlugin.GetWeather","arguments":{"location":"北京","date":"明天"}}
- Tool: [TOOL_RESULT:WeatherPlugin.GetWeather] {"temperature":23,"condition":"多云"}
- Assistant: "明天北京的天气是多云，温度23度"
```

## 技术优势

### 1. **完整上下文保持**
- AI可以看到之前的工具调用历史
- 避免重复调用相同的工具
- 更好的对话连贯性

### 2. **智能缓存策略**
- 基于用户ID和会话ID的双重隔离
- 自动清理过期的工具调用记录
- 优化的内存使用

### 3. **向后兼容**
- 保留所有旧的API接口
- 自动检测是否包含工具调用
- 渐进式升级路径

## 配置说明

### 1. **缓存配置**
```csharp
// 最大缓存消息数（考虑工具调用）
if (cachedMessages.Count > 90) // 估算每轮对话最多3条消息
{
    cachedMessages = cachedMessages.Skip(cachedMessages.Count - 90).ToList();
}
```

### 2. **消息过滤**
```csharp
// 只保存非隐藏的消息
x.IsHidden == 0
```

## 使用示例

### 1. **API调用示例**
```http
POST /Agent/ChatCompletion
{
    "input": "帮我查询天气并制定出行计划",
    "chatGuid": "chat-session-id",
    "userGuid": "user-id"
}
```

### 2. **历史记录查看**
历史记录现在会包含：
- 用户的原始问题
- AI的工具调用决策
- 工具的执行结果
- AI基于工具结果的最终回复

### 3. **清除特定会话的工具调用历史**
```http
POST /Agent/ClearUserChatConversationMemoryCompletely
{
    "UserId": "user-id",
    "ChatGuid": "chat-session-id"
}
```

## 监控和日志

### 1. **关键日志**
```
[ConversationMemoryService] 从数据库获取到用户 {userId} 会话 {chatGuid} 的 {count} 条对话消息（包含工具调用）
[GetNewConversationMessages] 发现 {count} 条工具调用相关消息
[InvokeAgentAsync] 已为用户 {userGuid} 会话 {chatGuid} 保存 {count} 条会话记忆（包含工具调用）
```

### 2. **性能监控**
- 工具调用频率统计
- 历史记录加载时间
- 缓存命中率

## 注意事项

### 1. **数据库影响**
- 工具调用会增加消息存储量
- 建议定期清理过期的工具调用记录
- 监控数据库存储空间

### 2. **性能考虑**
- 工具调用历史会增加上下文长度
- 可能影响AI响应时间
- 建议根据实际情况调整保留的轮次数

### 3. **安全性**
- 工具调用参数可能包含敏感信息
- 工具返回结果需要适当过滤
- 确保历史记录的访问权限控制

## 总结

通过本次更新，历史消息功能现在能够完整记录包含工具调用的对话流程，为AI提供更丰富的上下文信息，显著提升了多轮对话的质量和连贯性。同时保持了良好的向后兼容性和性能表现。

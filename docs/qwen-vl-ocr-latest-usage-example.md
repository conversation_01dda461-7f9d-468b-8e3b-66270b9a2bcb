# qwen-vl-ocr-latest 模型对话格式说明

## 概述

对于 `qwen-vl-ocr-latest` 模型，我们已经调整了多模态处理器，使其保持标准的字符串对话格式，而不是转换为数组格式。

## 对话格式对比

### qwen-vl-ocr-latest 模型（保持字符串格式）

```json
{
    "model": "qwen-vl-ocr-latest",
    "messages": [
        {
            "role": "system",
            "content": "你是一个智能助手"
        },
        {
            "role": "user", 
            "content": "你好"
        }
    ]
}
```

### 其他多模态模型（转换为数组格式）

对于其他多模态模型如 `qwen-vl-plus`、`qwen2-vl-7b-instruct` 等，仍然会转换为数组格式：

```json
{
    "model": "qwen-vl-plus",
    "messages": [
        {
            "role": "system",
            "content": [
                {
                    "type": "text",
                    "text": "你是一个智能助手"
                }
            ]
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "text", 
                    "text": "你好"
                }
            ]
        }
    ]
}
```

## 实现细节

在 `MultimodalHttpHandler.cs` 中，我们添加了特殊逻辑来检测 `qwen-vl-ocr-latest` 模型：

1. 当检测到模型名称为 `qwen-vl-ocr-latest` 时，跳过文本内容到数组格式的转换
2. 保持原始的字符串格式不变
3. 其他多模态模型继续使用数组格式

## 测试验证

我们已经添加了相应的测试用例来验证：

- `qwen-vl-ocr-latest` 模型保持字符串格式
- 其他多模态模型（如 `qwen2-vl-7b-instruct`）仍然转换为数组格式
- 已有的数组格式内容不会被重复转换

## 使用建议

在使用 `qwen-vl-ocr-latest` 模型时：

1. 可以直接使用标准的字符串格式发送对话消息
2. 不需要手动构造数组格式的 content
3. 系统会自动识别模型类型并保持正确的格式

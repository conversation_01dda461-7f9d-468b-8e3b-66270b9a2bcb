# 会话级别历史消息隔离功能更新

## 概述

本次更新解决了多个窗口历史记录混合的问题，通过增加ChatGUID作为条件，实现了会话级别的历史消息隔离。

## 问题描述

之前的ConversationMemoryService只使用`userguid`作为条件查询历史消息，导致同一用户的多个窗口会话会共享历史记录，造成以下问题：

1. **会话混合**：用户在窗口A的对话历史会出现在窗口B中
2. **上下文污染**：不同会话的上下文相互干扰，影响AI回复质量
3. **用户体验差**：用户无法在不同窗口中进行独立的对话

## 解决方案

### 1. 核心修改

#### 1.1 MysoftMemoryCache 更新
- 修改缓存键构造方式，使用 `userId + ":" + chatGuid` 组合
- 新增基于ChatGUID的缓存方法：
  - `SetConversationMemoryCache(string userId, string chatGuid, List<ChatMessageDto> messages, int expirationMinutes)`
  - `GetConversationMemoryCache(string userId, string chatGuid)`
  - `ClearConversationMemoryCache(string userId, string chatGuid)`

#### 1.2 ConversationMemoryService 更新
- 修改 `LoadConversationMemoryAsync` 方法，增加 `chatGuid` 参数
- 修改 `SaveConversationMemoryAsync` 方法，增加 `chatGuid` 参数
- 修改数据库查询条件，同时使用 `UserGUID` 和 `ChatGUID` 过滤
- 新增基于ChatGUID的清除方法：
  - `ClearConversationMemoryCompletelyAsync(string userId, string chatGuid)`
  - `ClearConversationMemory(string userId, string chatGuid)`

#### 1.3 AgentSkillDomainService 更新
- 修改调用ConversationMemoryService的地方，传递ChatGUID参数
- 从 `_chatRunDto.Chat.ChatGUID` 获取会话ID

#### 1.4 API 端点更新
- 新增 `ClearUserChatConversationMemoryCompletely` API端点
- 支持清除指定用户指定会话的历史记录

### 2. 向后兼容性

为了确保向后兼容，所有旧的方法都被保留并标记为 `[Obsolete]`：

- 旧方法会记录警告日志，提示升级到新版本
- 旧方法仍然可以正常工作，但无法实现会话级别隔离
- 建议逐步迁移到新的基于ChatGUID的方法

### 3. 新增功能

#### 3.1 会话级别隔离
```csharp
// 加载指定会话的历史记忆
await conversationMemoryService.LoadConversationMemoryAsync(chatHistory, userId, chatGuid);

// 保存到指定会话
await conversationMemoryService.SaveConversationMemoryAsync(userId, chatGuid, userMessage, assistantMessage);
```

#### 3.2 精确清除
```csharp
// 清除指定会话的记忆
await conversationMemoryService.ClearConversationMemoryCompletelyAsync(userId, chatGuid);
```

#### 3.3 API调用
```http
POST /Agent/ClearUserChatConversationMemoryCompletely
Content-Type: application/json

{
    "UserId": "user-guid-here",
    "ChatGuid": "chat-guid-here"
}
```

## 实现细节

### 1. 缓存键构造
```csharp
private string GetConversationMemoryCacheKey(string userId, string chatGuid)
{
    return ConversationMemoryCacheKeyPrefix + userId + ":" + chatGuid;
}
```

### 2. 数据库查询条件
```csharp
var chatGuidParsed = Guid.Parse(chatGuid);
var messages = await _chatMessageRepository.GetListAsync(x =>
    x.UserGUID == userId &&
    x.ChatGUID == chatGuidParsed &&
    (x.Role == ChatRoleConstant.User || x.Role == ChatRoleConstant.Assistant) &&
    x.IsHidden == 0);
```

### 3. ChatGUID获取
```csharp
string userGuid = _chatRunDto.Chat?.UserGUID ?? string.Empty;
string chatGuid = _chatRunDto.Chat?.ChatGUID.ToString() ?? string.Empty;
```

## 测试验证

### 1. 功能测试
1. 打开两个不同的会话窗口
2. 在窗口A中进行对话
3. 在窗口B中进行对话
4. 验证两个窗口的历史记录互不干扰

### 2. API测试
```bash
# 清除指定会话的记忆
curl -X POST "http://localhost:5000/Agent/ClearUserChatConversationMemoryCompletely" \
     -H "Content-Type: application/json" \
     -d '{"UserId":"user-123","ChatGuid":"chat-456"}'
```

## 日志记录

所有关键操作都有详细的日志记录：

```
[ConversationMemoryService] 开始为用户 {userId} 会话 {chatGuid} 加载会话记忆，最大轮数: {maxTurns}
[ConversationMemoryService] 成功为用户 {userId} 会话 {chatGuid} 保存会话记忆，当前缓存消息数: {count}
[ConversationMemoryService] 已彻底清除用户 {userId} 会话 {chatGuid} 的所有会话记忆
```

## 注意事项

1. **ChatGUID必须有效**：确保传递的ChatGUID是有效的GUID格式
2. **性能影响**：会话级别隔离会增加缓存条目数量，但提高了数据准确性
3. **迁移建议**：建议逐步将现有代码迁移到新的基于ChatGUID的方法

## 总结

通过增加ChatGUID作为会话标识符，成功实现了会话级别的历史消息隔离，解决了多窗口历史记录混合的问题，提升了用户体验和系统的可靠性。

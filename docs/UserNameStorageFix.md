# 用户名存储修复文档

## 问题描述

AgentSkillDomainService使用_conversationMemoryService存储日志时，没有存储用户名，导致gpt_chatmessage.UserName字段为空。

## 问题分析

### 根本原因
1. 在`ExtractNewMessagesFromChatHistory`方法中创建`ChatMessageDto`对象时，没有设置`UserName`字段
2. 在`ConversationMemoryService.SaveConversationMemoryAsync`方法中创建`ChatMessageDto`对象时，也没有设置`UserName`字段
3. 虽然`_chatRunDto.Chat`中包含`UserName`信息，但没有被传递到消息对象中

### 影响范围
- `AgentSkillDomainService.ExtractNewMessagesFromChatHistory`方法
- `ConversationMemoryService.SaveConversationMemoryAsync`方法
- `ConversationMemoryService.SaveConversationMessagesAsync`方法

## 修复方案

### 1. 修改AgentSkillDomainService.ExtractNewMessagesFromChatHistory方法

**文件**: `src/Mysoft.GPTEngine.Domain/AgentSkillDomainService.cs`

**修改内容**:
- 在方法开始处获取用户名：`var userName = _chatRunDto?.Chat?.UserName ?? string.Empty;`
- 在创建用户消息时设置用户名：`UserName = userName`
- 在创建助手回复时设置用户名：`UserName = userName`
- 在处理工具调用消息时设置用户名：`messageDto.UserName = userName`

### 2. 修改ConversationMemoryService.SaveConversationMemoryAsync方法

**文件**: `src/Mysoft.GPTEngine.Domain/Services/ConversationMemoryService.cs`

**修改内容**:
- 添加`userName`参数到方法签名
- 在创建缓存消息时设置用户名：`UserName = userName ?? string.Empty`
- 在创建数据库保存消息时设置用户名：`UserName = userName ?? string.Empty`

### 3. 修改ConversationMemoryService.SaveConversationMessagesAsync方法

**文件**: `src/Mysoft.GPTEngine.Domain/Services/ConversationMemoryService.cs`

**修改内容**:
- 添加`userName`参数到方法签名
- 在处理消息列表时，如果消息没有用户名则设置：
  ```csharp
  if (string.IsNullOrEmpty(message.UserName) && !string.IsNullOrEmpty(userName))
      message.UserName = userName;
  ```

### 4. 更新AgentSkillDomainService中的调用

**文件**: `src/Mysoft.GPTEngine.Domain/AgentSkillDomainService.cs`

**修改内容**:
- 在调用`SaveConversationMessagesAsync`时传递用户名参数
- 在调用`SaveConversationMemoryAsync`时传递用户名参数
- 从`_chatRunDto.Chat.UserName`获取用户名信息

### 5. 更新过时方法的兼容性调用

**文件**: `src/Mysoft.GPTEngine.Domain/Services/ConversationMemoryService.cs`

**修改内容**:
- 更新过时的`SaveConversationMemoryAsync`方法调用，传递`null`作为用户名参数

## 修改详情

### 关键代码变更

1. **ExtractNewMessagesFromChatHistory方法**:
```csharp
// 获取用户名信息
var userName = _chatRunDto?.Chat?.UserName ?? string.Empty;

// 在创建消息时设置用户名
newMessages.Add(new ChatMessageDto
{
    Role = ChatRoleConstant.User,
    Content = userInput,
    Index = 0,
    BatchGUID = batchGuid,
    UserName = userName, // 设置用户名
    IsHidden = 0
});
```

2. **SaveConversationMemoryAsync方法签名**:
```csharp
public async Task SaveConversationMemoryAsync(string userId, string chatGuid, string userMessage, string assistantMessage, string tenantCode = null, string tenantName = null, Guid? batchGuid = null, string userName = null)
```

3. **调用处更新**:
```csharp
// 获取租户信息和用户名
var tenantCode = _chatRunDto?.Chat?.TenantCode ?? string.Empty;
var tenantName = _chatRunDto?.Chat?.TenantName ?? string.Empty;
var userName = _chatRunDto?.Chat?.UserName ?? string.Empty;
var batchGuid = _chatRunDto?.BatchGuid ?? Guid.NewGuid();

await _conversationMemoryService.SaveConversationMessagesAsync(userGuid, chatGuid, newMessages, tenantCode, tenantName, batchGuid, userName);
```

## 验证方法

1. **编译验证**: 项目编译成功，无编译错误
2. **功能验证**: 
   - 运行应用程序
   - 进行聊天对话
   - 检查数据库中gpt_chatmessage表的UserName字段是否正确填充

## 向后兼容性

- 所有修改都是向后兼容的
- 新增的`userName`参数都设置为可选参数（默认值为`null`）
- 过时的方法调用已更新以传递正确的参数

## 总结

此修复确保了在AgentSkillDomainService使用ConversationMemoryService存储聊天消息时，用户名信息能够正确地从`_chatRunDto.Chat.UserName`传递到数据库的`gpt_chatmessage.UserName`字段中，解决了用户名丢失的问题。

# 知识库问答工具描述优化说明

## 优化目标

优化知识库问答工具的描述部分，确保通过 `knowledge.Name` 和 `knowledge.Description` 有足够的区分度，让大模型能够更好地理解和运用这些工具。

## 优化前后对比

### 优化前的描述格式

```csharp
string description = $"{knowledge.Name}知识库问答工具。{knowledge.Description}。输入问题后自动检索相关内容并返回最相关的知识片段。";
```

**存在的问题：**
1. 描述过于简单，缺乏足够的上下文信息
2. `knowledge.Name` 和 `knowledge.Description` 可能内容重复或区分度不够
3. 大模型难以理解工具的具体适用场景和使用方式
4. 缺乏对参数的详细说明

**示例输出：**
```
财务管理知识库问答工具。包含财务制度、报销流程等内容。输入问题后自动检索相关内容并返回最相关的知识片段。
```

### 优化后的描述格式

```csharp
private static string BuildKnowledgeToolDescription(AgentKnowledgeDto knowledge)
{
    var descriptionBuilder = new StringBuilder();
    
    // 基础工具说明
    descriptionBuilder.Append($"【{knowledge.Name}】专用知识库问答工具");
    
    // 如果有详细描述，添加知识库的具体内容和适用场景
    if (!string.IsNullOrWhiteSpace(knowledge.Description))
    {
        // 清理描述中的换行符和多余空格
        var cleanDescription = knowledge.Description.Replace("\r\n", " ").Replace("\n", " ").Replace("\r", " ");
        cleanDescription = System.Text.RegularExpressions.Regex.Replace(cleanDescription, @"\s+", " ").Trim();
        
        descriptionBuilder.Append($"。知识库内容：{cleanDescription}");
    }
    
    // 添加使用说明
    descriptionBuilder.Append("。");
    descriptionBuilder.Append("使用此工具可以查询该知识库中的相关信息，");
    descriptionBuilder.Append("系统会自动将问题向量化后检索最相关的知识片段，");
    descriptionBuilder.Append("并按相似度排序返回结果。");
    
    // 添加适用场景提示
    descriptionBuilder.Append("适用于需要获取该领域专业知识、政策解读、操作指南等场景。");
    
    // 添加参数说明
    descriptionBuilder.Append("参数说明：question为具体问题，topk为返回结果数量（默认5条，最多20条）。");
    
    return descriptionBuilder.ToString();
}
```

**优化后的示例输出：**
```
【财务管理】专用知识库问答工具。知识库内容：包含财务制度、报销流程、预算管理、成本控制等企业财务管理相关政策和操作指南。使用此工具可以查询该知识库中的相关信息，系统会自动将问题向量化后检索最相关的知识片段，并按相似度排序返回结果。适用于需要获取该领域专业知识、政策解读、操作指南等场景。参数说明：question为具体问题，topk为返回结果数量（默认5条，最多20条）。
```

## 参数描述优化

### 优化前的参数描述

```csharp
new KernelParameterMetadata("question")
{
    Description = "要查询的问题"
},
new KernelParameterMetadata("topk")
{
    Description = "返回结果数量，默认5条",
    DefaultValue = "5"
}
```

### 优化后的参数描述

```csharp
new KernelParameterMetadata("question")
{
    Description = $"要查询的具体问题。请根据【{knowledge.Name}】知识库的内容范围提出相关问题，系统会自动检索最匹配的知识片段"
},
new KernelParameterMetadata("topk")
{
    Description = "返回结果数量。指定需要返回多少条相关知识片段，取值范围1-20，默认5条。数量越多信息越全面但可能包含相关性较低的内容",
    DefaultValue = "5"
}
```

## 优化效果

### 1. 提升区分度
- 通过 `【{knowledge.Name}】` 的格式突出知识库名称
- 明确区分知识库内容和使用说明
- 为每个知识库生成独特的描述

### 2. 增强理解性
- 详细说明工具的工作原理（向量化检索、相似度排序）
- 明确适用场景和使用方式
- 提供完整的参数说明和取值范围

### 3. 改善可用性
- 清理描述中的格式问题（换行符、多余空格）
- 提供具体的参数使用指导
- 帮助大模型更准确地选择和使用工具

### 4. 标准化格式
- 统一的描述结构和格式
- 一致的参数说明模式
- 便于维护和扩展

## 技术实现要点

1. **文本清理**：自动处理描述中的换行符和多余空格
2. **结构化描述**：按照固定的格式组织描述内容
3. **动态参数**：根据知识库名称动态生成参数描述
4. **兼容性**：保持与现有代码结构的兼容性

## 使用示例

假设有以下知识库配置：

```json
{
    "Name": "人力资源管理",
    "Code": "HR_MGMT",
    "Description": "包含员工招聘、培训、绩效考核、薪酬福利等人力资源管理制度和流程"
}
```

生成的工具描述将是：

```
【人力资源管理】专用知识库问答工具。知识库内容：包含员工招聘、培训、绩效考核、薪酬福利等人力资源管理制度和流程。使用此工具可以查询该知识库中的相关信息，系统会自动将问题向量化后检索最相关的知识片段，并按相似度排序返回结果。适用于需要获取该领域专业知识、政策解读、操作指南等场景。参数说明：question为具体问题，topk为返回结果数量（默认5条，最多20条）。
```

这样的描述能够让大模型清楚地理解：
- 这是一个专门用于人力资源管理的知识库工具
- 知识库包含的具体内容范围
- 如何正确使用这个工具
- 参数的含义和取值范围

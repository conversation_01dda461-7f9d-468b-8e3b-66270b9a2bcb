# DataQueryActivity消息保存优化

## 问题描述

在DataQueryActivity中存在消息保存顺序和重复保存的问题：

1. **StartActivity保存用户输入**：用户输入首先由StartActivity保存到`_chatRunDto.ChatMessages`中
2. **DataQueryActivity直接清除消息**：使用`_chatRunDto.ChatMessages.Clear()`直接清除消息，可能丢失重要信息
3. **需要确保保存顺序**：需要在清除之前按顺序保存ChatMessages中的信息

## 解决方案

### 1. 新增SaveChatMessagesInOrder方法

在调用`_chatRunDto.ChatMessages.Clear()`之前，先按顺序保存ChatMessages：

```csharp
// 在清除ChatMessages之前，先按顺序保存现有的ChatMessages信息
await SaveChatMessagesInOrder();

// 清除ChatMessages中的消息，避免与SaveConversationMemory重复保存
_chatRunDto.ChatMessages.Clear();
```

### 2. 修改SaveConversationMemory方法

现在只需要保存助手回复，因为用户输入已经通过SaveChatMessagesInOrder保存：

```csharp
// 只保存助手回复，用户输入已经通过SaveChatMessagesInOrder按顺序保存
var assistantMessage = new ChatMessageDto { /* ... */ };
await _conversationMemoryService.SaveConversationMessagesAsync(
    userGuid, chatGuid, new List<ChatMessageDto> { assistantMessage }, tenantCode, tenantName, batchGuid, userName);
```

## SaveChatMessagesInOrder方法详细实现

这个新方法的特点：

- **按顺序保存**：使用`OrderBy(m => m.Index)`确保消息按正确顺序保存
- **补充字段信息**：为每条消息补充必要的用户、租户、批次等信息
- **使用现有服务**：调用`ConversationMemoryService.SaveConversationMessagesAsync`保存
- **错误处理**：包含完整的异常处理和日志记录

```csharp
private async Task SaveChatMessagesInOrder()
{
    // 按Index顺序排序ChatMessages，确保保存顺序正确
    var sortedMessages = _chatRunDto.ChatMessages
        .OrderBy(m => m.Index)
        .ToList();

    // 补充必要的字段信息
    foreach (var message in sortedMessages)
    {
        message.UserGUID = userGuid;
        message.UserName = userName;
        message.ChatGUID = Guid.Parse(chatGuid);
        message.TenantCode = tenantCode;
        message.TenantName = tenantName;
        message.BatchGUID = batchGuid;
    }

    // 使用ConversationMemoryService保存消息
    await _conversationMemoryService.SaveConversationMessagesAsync(
        userGuid, chatGuid, sortedMessages, tenantCode, tenantName, batchGuid, userName);
}
```

## 修改的文件

### DataQueryActivity.cs

1. **新增方法**：`SaveChatMessagesInOrder`
2. **修改调用顺序**：在Clear()之前调用SaveChatMessagesInOrder
3. **简化SaveConversationMemory**：只保存助手回复

## 保存流程

1. **DataQueryActivity执行完成**
2. **SaveChatMessagesInOrder**：按顺序保存ChatMessages中的所有消息
3. **ChatMessages.Clear()**：清除ChatMessages避免重复
4. **SaveConversationMemory**：只保存助手回复
5. **工具调用消息**：通过现有逻辑单独保存

## 优势

1. **确保保存顺序**：ChatMessages按Index顺序保存
2. **避免信息丢失**：清除前先保存所有重要信息
3. **避免重复保存**：清除后只保存助手回复
4. **数据一致性**：使用统一的保存服务
5. **代码清晰**：明确的职责分离

## 测试建议

建议测试以下场景：

1. 普通对话的消息保存顺序
2. 包含工具调用的对话
3. 多轮对话的消息完整性
4. ChatMessages清除前后的数据一致性
5. 异常情况下的数据保护

## 注意事项

- 这个修改确保了在清除ChatMessages之前，所有消息都按正确顺序保存
- 保持了与现有代码的兼容性
- 工具调用消息的保存逻辑没有改变
- 增强了数据安全性，避免因清除操作导致的数据丢失

# .NET 产品模板仓库

## 目录结构
```
src(产品代码目录)
│  ├─Mysoft.GPTEngine.WebApplication # GPT引擎启动模块
│  ├─Mysoft.GPTEngine.Application # GPT引擎的应用服务层
│  ├─Mysoft.GPTEngine.Application.Contracts # GPT引擎的应用服务层约束：包含接口、数据传输对象
│  ├─Mysoft.GPTEngine.Domain # GPT引擎的领域服务层，实现具体的业务逻辑
│  ├─Mysoft.GPTEngine.Domain.Shared # GPT引擎的领域服务：包含常量,枚举和其他对象
│  ├─Mysoft.GPTEngine.Plugin # GPT引擎的插件层，实现具体的插件功能（无业务含义类）
│  ├─Mysoft.GPTEngine.SemanticKernel.Core  # GPT引擎对SK的扩展
│  ├─Mysoft.GPTEngine.Diagnostics.Extend # GPT引擎对Diagnostics的扩展（天眼探针）

```


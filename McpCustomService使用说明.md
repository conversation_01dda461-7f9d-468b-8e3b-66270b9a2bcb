# McpCustomService 使用说明

## 概述

`McpCustomService` 是一个整合了 `McpToolWrapper` 和 `OfficialMcpToolLoader` 功能的自定义MCP服务，位于 `Mysoft.GPTEngine.Domain` 命名空间下。该服务提供了完整的MCP工具管理和执行功能，并新增了三个核心能力。

## ✅ 替换完成状态

**AgentSkillDomainService 中的 OfficialMcpToolLoader 已成功替换为 McpCustomService**

- ✅ 依赖注入已更新：`McpCustomService` 已注册到服务容器
- ✅ 方法调用已替换：`AddMcpToolsWithOfficialLoader` → `AddMcpToolsWithCustomService`
- ✅ 功能完全兼容：保持原有的MCP工具加载和执行能力
- ✅ 编译验证通过：所有层（Domain、Application、WebApplication）编译成功

## 主要功能

### 1. 获取MCP服务器的所有工具列表详细信息
通过 `/mcp/GetMcpTools` 端点获取MCP服务器上所有可用工具的详细信息。

### 2. 执行指定的MCP工具方法
通过 `/mcp/ExecuteMcpTool` 端点执行特定的MCP工具并获取执行结果。

### 3. 设置访问MCP客户端时的请求头
支持在连接MCP服务器时设置自定义的HTTP请求头，用于携带认证信息或其他元数据。

## API 端点

### 1. 获取工具列表
**端点**: `POST /mcp/GetMcpTools`

**请求体**:
```json
{
  "mcpServerUrl": "http://localhost:3000/sse",
  "headers": {
    "Authorization": "Bearer your-token",
    "X-Custom-Header": "custom-value"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "name": "tool_name",
      "description": "工具描述",
      "inputSchema": {
        "type": "object",
        "properties": {
          "param1": {
            "type": "string",
            "description": "参数1描述"
          }
        }
      },
      "parameters": [
        {
          "name": "param1",
          "type": "string",
          "description": "参数1描述",
          "required": true,
          "defaultValue": null
        }
      ]
    }
  ]
}
```

### 2. 执行工具
**端点**: `POST /mcp/ExecuteMcpTool`

**请求体**:
```json
{
  "mcpServerUrl": "http://localhost:3000/sse",
  "executeRequest": {
    "toolName": "tool_name",
    "arguments": {
      "param1": "value1",
      "param2": "value2"
    },
    "headers": {
      "Authorization": "Bearer your-token"
    }
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "result": "工具执行结果",
    "errorMessage": null,
    "executionTimeMs": 150,
    "toolName": "tool_name",
    "executedAt": "2025-01-21T10:30:00Z"
  }
}
```

### 3. 测试连接
**端点**: `POST /mcp/TestMcpConnection`

**请求体**:
```json
{
  "mcpServerUrl": "http://localhost:3000/sse",
  "headers": {
    "Authorization": "Bearer your-token"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": true
}
```

## 代码使用示例

### 在Domain层直接使用

```csharp
// 注入McpCustomService
private readonly McpCustomService _mcpCustomService;

// 设置自定义请求头
var headers = new Dictionary<string, string>
{
    ["Authorization"] = "Bearer your-token",
    ["X-Custom-Header"] = "custom-value"
};
_mcpCustomService.SetCustomHeaders(headers);

// 连接到MCP服务器
var connected = await _mcpCustomService.ConnectAsync("http://localhost:3000/sse");

if (connected)
{
    // 获取工具列表
    var tools = await _mcpCustomService.GetAllToolsAsync();
    
    // 执行工具
    var request = new McpExecuteRequest
    {
        ToolName = "example_tool",
        Arguments = new Dictionary<string, object>
        {
            ["param1"] = "value1"
        }
    };
    
    var result = await _mcpCustomService.ExecuteToolAsync(request);
}
```

### 在Semantic Kernel中使用

```csharp
// 加载MCP工具到Kernel
var kernel = new KernelBuilder().Build();
var success = await _mcpCustomService.LoadMcpToolsToKernelAsync(
    kernel, 
    "http://localhost:3000/sse", 
    "MyMcpPlugin"
);

if (success)
{
    // 现在可以在Kernel中使用MCP工具
    var result = await kernel.InvokeAsync("MyMcpPlugin", "tool_name", new KernelArguments
    {
        ["param1"] = "value1"
    });
}
```

## 配置说明

### 依赖注入配置

服务已在 `ServiceExtensions.cs` 中注册：

```csharp
// Domain层服务注册
services.AddScoped<McpCustomService>();

// Application层服务注册  
services.AddScoped<McpAppService>();
```

### 超时配置

可以通过构造函数配置各种超时参数：

```csharp
var mcpService = new McpCustomService(
    mysoftContextFactory,
    httpContextAccessor,
    mapper,
    logger,
    loggerFactory,
    httpClient,
    connectTimeout: TimeSpan.FromSeconds(30),    // 连接超时
    readTimeout: TimeSpan.FromMinutes(2),        // 读取超时
    writeTimeout: TimeSpan.FromSeconds(30),      // 写入超时
    keepAliveInterval: TimeSpan.FromSeconds(30)  // 保活间隔
);
```

## 错误处理

所有方法都包含完整的错误处理和日志记录：

- 连接失败时返回 `false` 或错误响应
- 工具执行失败时在响应中包含错误信息
- 所有异常都会被捕获并记录到日志中

## 注意事项

1. **线程安全**: 服务设计为单例使用，但不是线程安全的。在多线程环境中使用时需要注意。

2. **资源释放**: 服务实现了 `IDisposable` 接口，使用完毕后会自动释放资源。

3. **兼容性**: 代码兼容 .NET Core 3.1，避免使用高版本C#特性。

4. **日志记录**: 所有重要操作都有详细的日志记录，便于调试和监控。

## 文件结构

```
src/Mysoft.GPTEngine.Domain/
├── McpCustomService.cs                    # 主要服务类
├── DTO/
│   ├── McpToolInfo.cs                    # 工具信息DTO
│   ├── McpExecuteRequest.cs              # 执行请求DTO
│   └── McpExecuteResponse.cs             # 执行响应DTO

src/Mysoft.GPTEngine.Application/
└── McpAppService.cs                      # 应用层服务

src/Mysoft.GPTEngine.WebApplication/
└── Controllers/
    └── McpController.cs                  # API控制器
```

这个整合的服务提供了完整的MCP工具管理功能，同时保持了与现有架构的兼容性。

## 🔄 AgentSkillDomainService 替换详情

### 替换前后对比

**替换前 (OfficialMcpToolLoader)**:
```csharp
// 旧的依赖注入
private readonly OfficialMcpToolLoader _officialMcpToolLoader;

// 旧的方法调用
await AddMcpToolsWithOfficialLoader(kernel);

// 旧的实现
private async Task AddMcpToolsWithOfficialLoader(Kernel kernel)
{
    var officialLoader = new OfficialMcpToolLoader(...);
    var toolsLoaded = await officialLoader.LoadMcpToolsAsync(kernel, _mcpServerUrl, "DataMcp");
}
```

**替换后 (McpCustomService)**:
```csharp
// 新的依赖注入
private readonly McpCustomService _mcpCustomService;

// 新的方法调用
await AddMcpToolsWithCustomService(kernel);

// 新的实现
private async Task AddMcpToolsWithCustomService(Kernel kernel)
{
    var toolsLoaded = await _mcpCustomService.LoadMcpToolsToKernelAsync(kernel, _mcpServerUrl, "DataMcp");
}
```

### 替换的优势

1. **功能整合**: 将原本分散的MCP功能整合到一个服务中
2. **代码简化**: 减少了复杂的初始化和配置代码
3. **更好的错误处理**: 统一的异常处理和日志记录
4. **扩展性**: 新增了三个核心API能力
5. **维护性**: 更清晰的代码结构和更好的可测试性

### 兼容性保证

- ✅ **接口兼容**: 保持相同的方法签名和返回值
- ✅ **功能兼容**: 所有原有功能都得到保留
- ✅ **性能兼容**: 相同或更好的性能表现
- ✅ **配置兼容**: 使用相同的配置参数和MCP服务器URL

using System;

class Program
{
    static void Main()
    {
        Console.WriteLine("Testing URL combination logic...");
        
        // Test cases that match our fix
        TestCombineUrlPath("https://dashscope.aliyuncs.com", "/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1");
        TestCombineUrlPath("https://dashscope.aliyuncs.com/", "/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1");
        TestCombineUrlPath("https://dashscope.aliyuncs.com//", "/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1");
        TestCombineUrlPath("https://api.openai.com", "v1/chat/completions", "https://api.openai.com/v1/chat/completions");
        TestCombineUrlPath("https://api.openai.com/", "v1/chat/completions", "https://api.openai.com/v1/chat/completions");
        TestCombineUrlPath("https://api.openai.com", "/v1/chat/completions", "https://api.openai.com/v1/chat/completions");
        TestCombineUrlPath("https://api.openai.com/", "/v1/chat/completions", "https://api.openai.com/v1/chat/completions");
        TestCombineUrlPath("", "/compatible-mode/v1", "/compatible-mode/v1");
        TestCombineUrlPath("https://dashscope.aliyuncs.com", "", "https://dashscope.aliyuncs.com");
        TestCombineUrlPath("", "", "");
        TestCombineUrlPath(null, "/compatible-mode/v1", "/compatible-mode/v1");
        TestCombineUrlPath("https://dashscope.aliyuncs.com", null, "https://dashscope.aliyuncs.com");
        
        Console.WriteLine("\nAll tests completed!");
    }
    
    static void TestCombineUrlPath(string baseUrl, string path, string expected)
    {
        string result = CombineUrlPath(baseUrl, path);
        bool passed = result == expected;
        
        Console.WriteLine($"Test: CombineUrlPath(\"{baseUrl ?? "null"}\", \"{path ?? "null"}\")");
        Console.WriteLine($"Expected: {expected}");
        Console.WriteLine($"Actual:   {result}");
        Console.WriteLine($"Result:   {(passed ? "PASS" : "FAIL")}");
        Console.WriteLine();
        
        if (!passed)
        {
            Console.WriteLine("❌ TEST FAILED!");
            Environment.Exit(1);
        }
    }
    
    /// <summary>
    /// 安全地组合URL路径，避免双斜杠问题
    /// </summary>
    /// <param name="baseUrl">基础URL</param>
    /// <param name="path">要添加的路径</param>
    /// <returns>正确组合的URL</returns>
    private static string CombineUrlPath(string baseUrl, string path)
    {
        if (string.IsNullOrEmpty(baseUrl))
            return path;
        
        if (string.IsNullOrEmpty(path))
            return baseUrl;
        
        // 移除baseUrl末尾的斜杠
        baseUrl = baseUrl.TrimEnd('/');
        
        // 确保path以斜杠开头
        if (!path.StartsWith("/"))
            path = "/" + path;
        
        return baseUrl + path;
    }
}

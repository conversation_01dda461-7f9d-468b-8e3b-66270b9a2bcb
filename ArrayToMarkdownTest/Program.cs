using System;
using System.Text;
using Newtonsoft.Json.Linq;

namespace ArrayToMarkdownTest
{
    class Program
    {
        static void Main(string[] args)
        {
            // 测试新格式的数组数据
            var testArrayJson = @"[
                {
                    ""title"": ""下拉筛选-1"",
                    ""content"": ""| 公司 | 公司名称 |\n|---|---|\n| 深圳公司 | 深圳公司 |""
                },
                {
                    ""title"": ""业绩"",
                    ""content"": ""| 来访数 | 复访数 | 认筹数 | 认筹金额 | 认购套数 | 认购金额 | 筹转认套数 | 筹转认金额 | 净认购套数 | 净认购金额 | 签约套数 | 签约金额 |\n|---|---|---|---|---|---|---|---|---|---|---|---|\n| 1.046万 | 0.0522万 | 0.0622万 | 134473万 | 0.0654万 | 156446万 | 0.0589万 | 128976万 | 0.0065万 | 27471万 | 0.0793万 | 199233万 |""
                }
            ]";

            Console.WriteLine("输入的数组格式数据:");
            Console.WriteLine(testArrayJson);
            Console.WriteLine();

            // 解析 JSON 数组
            var dataArray = JArray.Parse(testArrayJson);

            // 转换为 Markdown
            var markdownResult = ConvertDataArrayToMarkdown(dataArray);

            Console.WriteLine("转换后的 Markdown 格式:");
            Console.WriteLine(markdownResult);
            Console.WriteLine();

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 将数组格式的数据转换为 Markdown 格式
        /// </summary>
        /// <param name="dataArray">数据数组</param>
        /// <returns>Markdown 字符串</returns>
        private static string ConvertDataArrayToMarkdown(JArray dataArray)
        {
            if (dataArray == null || dataArray.Count == 0)
            {
                return string.Empty;
            }

            try
            {
                var markdownBuilder = new StringBuilder();

                for (int i = 0; i < dataArray.Count; i++)
                {
                    var item = dataArray[i];

                    // 获取 title 和 content
                    var title = item["title"]?.ToString();
                    var content = item["content"]?.ToString();

                    if (!string.IsNullOrWhiteSpace(title) && !string.IsNullOrWhiteSpace(content))
                    {
                        // 添加标题（使用 ## 格式）
                        markdownBuilder.AppendLine($"## {title}");
                        markdownBuilder.AppendLine();

                        // 添加内容
                        markdownBuilder.AppendLine(content);

                        // 如果不是最后一项，添加额外的换行符分隔
                        if (i < dataArray.Count - 1)
                        {
                            markdownBuilder.AppendLine();
                        }
                    }
                }

                return markdownBuilder.ToString().TrimEnd();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转换数组为 Markdown 时发生异常: {ex.Message}");
                return string.Empty;
            }
        }
    }
}

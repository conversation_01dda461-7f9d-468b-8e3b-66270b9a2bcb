<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- THIS PROPERTY GROUP MUST COME FIRST -->
    <AssemblyName>Microsoft.SemanticKernel.Plugins.OpenApi.Extensions</AssemblyName>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <TargetFramework>netstandard2.0</TargetFramework>
    <VersionSuffix>alpha</VersionSuffix>
    <NoWarn>SKEXP0040</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>Semantic Kernel - OpenAPI Plugin Extensions</Title>
    <Description>Semantic Kernel OpenAPI Plugin Extensions</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.OpenApi.ApiManifest" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Functions.OpenApi\Functions.OpenApi.csproj" />
  </ItemGroup>
</Project>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- THIS PROPERTY GROUP MUST COME FIRST -->
    <AssemblyName>Microsoft.SemanticKernel.Plugins.OpenApi</AssemblyName>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <TargetFramework>netstandard2.0</TargetFramework>
    <VersionSuffix>alpha</VersionSuffix>
  </PropertyGroup>
  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>Semantic Kernel - OpenAPI Plugins</Title>
    <Description>Semantic Kernel OpenAPI Plugins</Description>
  </PropertyGroup>
  <ItemGroup>
    <InternalsVisibleTo Include="SemanticKernel.Functions.UnitTests" />
    <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
    <InternalsVisibleTo Include="Microsoft.SemanticKernel.Plugins.OpenApi.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="JsonSchema.Net" Version="5.4.2" />
    <PackageReference Include="Microsoft.Identity.Client.Extensions.Msal" Version="2.9.14" />
    <PackageReference Include="Microsoft.OpenApi" Version="1.0.1" />
    <PackageReference Include="Microsoft.OpenApi.Readers" Version="1.0.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Core" Version="1.60.0" />
  </ItemGroup>
</Project>
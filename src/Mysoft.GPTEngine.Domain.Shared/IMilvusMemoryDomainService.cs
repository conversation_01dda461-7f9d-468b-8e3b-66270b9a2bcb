using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Threading;
using Microsoft.SemanticKernel.Memory;

namespace Mysoft.GPTEngine.Domain.Shared
{
    public interface IMilvusMemoryDomainService
    {
        IAsyncEnumerable<MemoryQueryResult> SearchAsync(
            string collection,
            ReadOnlyMemory<float> queryEmbedding,
            int limit = 1,
            double minRelevanceScore = 0.0,
            bool withEmbeddings = false,
            int sourceTypeEnum = 0,
            [EnumeratorCancellation] CancellationToken cancellationToken = default);
    }

}

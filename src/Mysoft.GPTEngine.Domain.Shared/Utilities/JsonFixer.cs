using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using Mysoft.GPTEngine.Common.Helper;

namespace Mysoft.GPTEngine.Domain.Shared.Utilities
{
    public class JsonFixer
    {
        private static readonly Dictionary<char, char> DataCloseChar = new Dictionary<char, char>
    {
        {'{', '}'},
        {'[', ']'},
        {'"', '"'}
    };

        public static string FixJson(string content)
        {
            // 避免污染原始内容
            string jsonString = content;
            if (string.IsNullOrEmpty(jsonString)) return "{}";

            jsonString = CleanUpJson(jsonString);

            string cleanedString = jsonString
                .Trim()
                .Replace("\r\n", "")
                .Replace("\n", "")
                .Replace("\r", "")
                .Replace("  ", "");

            cleanedString = Regex.Replace(cleanedString, @"(?<=:)([a-zA-Z]+)(?=\s*(?![,\}])(?:[,\}\s]|$))", " null");

            var missingChars = new Stack<char>();
            foreach (char c in cleanedString)
            {
                if (missingChars.Count > 0 && c == missingChars.Peek())
                {
                    missingChars.Pop();
                }
                else if (DataCloseChar.ContainsKey(c))
                {
                    missingChars.Push(DataCloseChar[c]);
                    if (c == '{')
                    {
                        missingChars.Push(':');
                    }
                }
            }

            if (missingChars.Count > 0 && missingChars.Peek() == ':')
            {
                if (cleanedString.EndsWith("{"))
                {
                    missingChars.Pop();
                }
                else
                {
                    missingChars.Pop();
                    missingChars.Push('l');
                    missingChars.Push('u');
                    missingChars.Push('n');
                    missingChars.Push('n');
                }
            }

            string missingCharsString = new string(missingChars.ToArray());
            string completeString = cleanedString + missingCharsString;

            completeString = Regex.Replace(completeString, @",\s*""""(?=\s*,|\s*})", "");

            completeString = completeString
                .Replace("\"\":", "")
                .Replace("\":}", "\": null }")
                .Replace("\": }", "\": null }")
                .Replace(",\"\"}", "}")
                .Replace(",}", "}")
                .Replace("},]", "}]")
                .Replace("\\\"}", "\"}")
                .Replace("\\\"}", "\"}");

            completeString = Regex.Replace(completeString, @",\s*""(\w+)""\s*}", "}");

            return completeString;
        }

        public static string CleanUpJson(string jsonStr)
        {
            jsonStr = jsonStr.Replace("{{", "{")
                         .Replace("}}", "}")
                         .Replace("\"[{\r\n", "[{")
                         .Replace("}]\"", "}]")
                         .Replace("\\", " ")
                         .Replace("\\n", " ")
                         .Replace("\n", " ")
                         .Replace("\r", "")
                         .Trim();

            if (jsonStr.StartsWith("```json"))
            {
                jsonStr = jsonStr.Substring("```json".Length);
            }
            if (jsonStr.StartsWith("json"))
            {
                jsonStr = jsonStr.Substring("json".Length);
            }
            if (jsonStr.Contains("```"))
            {
                // 处理JSON完整输出后存在其他解释内容
                jsonStr = jsonStr.Substring(0, jsonStr.LastIndexOf("```", StringComparison.Ordinal));
            }

            return jsonStr;
        }

        public static (IJsonFixerProperty, IJsonFixerProperty) GetLastJsonProperty(string jsonString)
        {
            if (string.IsNullOrWhiteSpace(jsonString)) return (null, null);

            using (JsonDocument doc = JsonDocument.Parse(jsonString))
            {
                JsonElement root = doc.RootElement;

                // 确保根元素是一个对象
                if (root.ValueKind != JsonValueKind.Object)
                {
                    // Console.WriteLine("The JSON string must represent an object.");
                    return (null, null);
                }

                // 获取所有属性
                var properties = root.EnumerateObject().ToList();

                // 检查是否有属性
                if (properties.Count == 0)
                {
                    // Console.WriteLine("The JSON object is empty.");
                    return (null, null);
                }

                // 返回最后一个属性
                IJsonFixerProperty perFixerProperty = null;
                var lastProperty = properties.Last();
                IJsonFixerProperty lastFixerProperty = GetFixerProperty(lastProperty);
                if (properties.Count >= 2)
                {
                    var perProperty = properties[properties.Count - 2];
                    perFixerProperty = GetFixerProperty(perProperty);
                }
                return (perFixerProperty, lastFixerProperty);
            }

        }
        private static IJsonFixerProperty GetFixerProperty(JsonProperty property)
        {
            return property.Value.ValueKind == JsonValueKind.Array ? GetLastPropertyInfo(property) :
                    new JsonFixerProperty(property.Name, property.Value.GetRawText());
        }
        private static IJsonFixerProperty GetLastPropertyInfo(JsonProperty property)
        {
            string key = property.Name;
            string value = property.Value.GetRawText();

            // 检查属性是否是数组
            if (property.Value.ValueKind == JsonValueKind.Array)
            {
                var arrayElements = property.Value.EnumerateArray().ToList();
                if (arrayElements.Count > 0)
                {
                    var lastArrayElement = arrayElements.Last();
                    int lastArrayIndex = arrayElements.Count - 1;

                    // 检查数组元素是否是对象
                    if (lastArrayElement.ValueKind == JsonValueKind.Object)
                    {
                        return new JsonFixerArrayProperty(key, lastArrayElement.GetRawText(), lastArrayIndex);
                    }
                    //else if (lastArrayElement.ValueKind == JsonValueKind.Array)
                    //{
                    //    // 递归处理数组元素
                    //    var recursiveResult = GetLastPropertyInfo(lastArrayElement.EnumerateObject().First());
                    //    return new JsonFixerProperty(key, recursiveResult);
                    //}
                }
            }

            return new JsonFixerProperty(key, value);
        }
    }
    public class JsonFixerArrayProperty : JsonFixerProperty, IJsonFixerProperty
    {
        public JsonFixerArrayProperty(string name, string value, int index) : base(name, value)
        {
            Index = index;
        }

        public int Index { get; set; } = 0;
        public new string GetJsonValue(string key, string content)
        {
            if (string.Equals("[]", content)) return null;
            return JsonHelper.JsonSerialize(key, new { index = Index, value = JsonDocument.Parse(content).RootElement });
        }
    }
    public class JsonFixerProperty : IJsonFixerProperty
    {
        public JsonFixerProperty() { }
        public JsonFixerProperty(string name, string value)
        {
            Name = name;
            Value = value;
        }

        public string Name { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;

        public string GetJsonValue(string key, string content)
        {
            if (string.Equals("null", content) || string.Equals("[]", content)) return null;
            // 序列化为JSON字符串
            return JsonHelper.JsonSerialize(key, content);
        }
    }
    public interface IJsonFixerProperty
    {
        string Name { get; set; }
        string Value { get; set; }
        string GetJsonValue(string key, string content);
    }
}

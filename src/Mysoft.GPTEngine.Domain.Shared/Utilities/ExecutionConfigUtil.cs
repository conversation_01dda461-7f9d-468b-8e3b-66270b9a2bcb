using System;
using System.Collections.Generic;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.Shared.Utilities
{
    public static class ExecutionConfigUtil
    {
        private static readonly SectionConfig DefaultWordLevelSectionConfig 
            = JsonConvert.DeserializeObject<SectionConfig>("{\"mode\":2,\"levelConfig\":{\"maxLevel\":9,\"saveMode\":1},\"customConfig\":{\"symbol\":[1,2,4,13,12,10,14,15],\"maxTokensPerParagraph\":500,\"overlappingTokens\":20,\"filter\":[]}}");
        
        private static readonly SectionConfig DefaultMarkdownLevelSectionConfig 
            = JsonConvert.DeserializeObject<SectionConfig>("{\"mode\":2,\"levelConfig\":{\"maxLevel\":6,\"saveMode\":1},\"customConfig\":{\"symbol\":[1,2],\"maxTokensPerParagraph\":500,\"overlappingTokens\":20,\"filter\":[]}}");
        
        private static readonly SectionConfig DefaultCustomSectionConfig 
            = JsonConvert.DeserializeObject<SectionConfig>("{\"mode\":1,\"levelConfig\":{},\"customConfig\":{\"symbol\":[1,2,4,6],\"maxTokensPerParagraph\":500,\"overlappingTokens\":20,\"filter\":[]}}");

        private static readonly Dictionary<string, SectionConfig> SectionConfigMap = new Dictionary<string, SectionConfig>
        {
            { "word", DefaultWordLevelSectionConfig },
            { "pdf", DefaultWordLevelSectionConfig },
            { "txt", DefaultCustomSectionConfig },
            { "markdown", DefaultMarkdownLevelSectionConfig },
            { "html", DefaultCustomSectionConfig }
        };

        // 空格换行 换行 中文句号 英文句号 中文感叹号 英文感叹号 中文问号 英文问号 中文逗号 英文逗号
        // 中文分号 英文分号 问号感叹号 反小括号中括号 空格 两个空格
        // private static readonly Dictionary<int, string> SplitSymbolMap = new Dictionary<int, string>
        // {
        //     { 1, "\r\n" },{ 2, "\n" },{ 3, "。" },{ 4, "." },{ 5, "！" },{ 6, "!" },{ 7, "？" },{ 8, "?" },{ 9, "，" },{ 10, "," },
        //     { 11, "；" },{ 12, ";" },{ 13, "?!" },{ 14, ")]" },{ 15, " " },{ 16, "  " }
        // };

        private static List<KeyValuePair<int, string>> SplitSymbolList = new List<KeyValuePair<int, string>>();
        static ExecutionConfigUtil()
        {
            SplitSymbolList.Add(new KeyValuePair<int, string>(1, "\r\n")); //空格换行
            SplitSymbolList.Add(new KeyValuePair<int, string>(2, "\n")); //换行
            SplitSymbolList.Add(new KeyValuePair<int, string>(3, "。")); //中文句号
            SplitSymbolList.Add(new KeyValuePair<int, string>(4, ".")); //英文句号
            SplitSymbolList.Add(new KeyValuePair<int, string>(5, "！")); //中文感叹号
            SplitSymbolList.Add(new KeyValuePair<int, string>(6, "!")); //英文感叹号
            SplitSymbolList.Add(new KeyValuePair<int, string>(7, "？")); //中文问号
            SplitSymbolList.Add(new KeyValuePair<int, string>(8, "?")); //英文问号
            SplitSymbolList.Add(new KeyValuePair<int, string>(9, "，")); //中文逗号
            SplitSymbolList.Add(new KeyValuePair<int, string>(10, ",")); //英文逗号
            SplitSymbolList.Add(new KeyValuePair<int, string>(11, "；")); //中文分号
            SplitSymbolList.Add(new KeyValuePair<int, string>(12, ";")); //英文分号
            SplitSymbolList.Add(new KeyValuePair<int, string>(13, "?!")); //问号感叹号
            SplitSymbolList.Add(new KeyValuePair<int, string>(14, ")]")); //反小括号中括号
            SplitSymbolList.Add(new KeyValuePair<int, string>(15, " ")); //空格
            SplitSymbolList.Add(new KeyValuePair<int, string>(16, "  ")); //两个空格
        }


        public static ExecutionSetting GetDefaultConfig(string executionSettingStr,string fileType, int isUploadImage)
        {
            ExecutionSetting executionSetting = new ExecutionSetting();
            SectionConfig sectionConfig;
            if (!String.IsNullOrEmpty(executionSettingStr))
            {
                executionSetting = JsonConvert.DeserializeObject<ExecutionSetting>(executionSettingStr);
                //如果分段方式是默认的，或者分段配置是空的，则使用默认的
                if (executionSetting.SectionConfig != null && executionSetting.SectionConfig.Mode != (int)SectionModeEnum.Default)
                {
                    return executionSetting;
                }
                
            }
            else
            {
                executionSetting.IsUploadImage = isUploadImage;
            }
            
            if (!SectionConfigMap.TryGetValue(fileType, out sectionConfig))
            {
                sectionConfig = DefaultCustomSectionConfig;
            }
            executionSetting.SectionConfig = sectionConfig;
            
            
            return executionSetting;

        }

        public static string[] GetSplitSymbol(List<int> symbolList)
        {
            List<string> result = new List<string>();
            foreach (var pair in SplitSymbolList)
            {
                if (symbolList.Contains(pair.Key))
                {
                    result.Add(pair.Value);
                }
            }
            return result.ToArray();
        }
    }
}
using System;

namespace Mysoft.GPTEngine.Domain.Shared.Utilities
{
    public static class TimeZoneUtility
    {
        private static TimeZoneInfo _currentTimeZone = TimeZoneInfo.Local;

        public static TimeZoneInfo CurrentTimeZone
        {
            get { return _currentTimeZone; }
            set { _currentTimeZone = value; }
        }

        public static DateTime LocalNow()
        {
            // 获取当前UTC时间
            DateTime utcNow = DateTime.UtcNow;

            DateTime serverTime = TimeZoneInfo.ConvertTimeFromUtc(utcNow, _currentTimeZone);

            return serverTime;
        }
        public static string LocalNowToString()
        {
            return LocalNow().ToString("yyyy-MM-ddTHH:mm:ss");
        }
    }
}

namespace Mysoft.GPTEngine.Domain.Shared.Constants
{
    public static class EventDataConstant
    {
        public const string TextEvent = "0:{0}\n";
        public const string ProcessEvent = "1:{0}\n";
        public const string ErrorEvent = "2:{0}\n";
        public const string DataEvent = "3:[{0}]\n";
        public const string DoneEvent = "4:{0}\n";
        public const string ReplaceEvent = "5:{0}\n";
        public const string ContentReviewEvent = "6:{0}\n";
        public const string StreamEvent = "7:{0}\n";
        public const string ReasoningEvent = "8:{0}\n";
        public const string HeartBeatEvent = "9:{0}\n";


        public const string ErrorCodeEvent = "{0}:{1}\n";
        


        public static string[] GetMessageContentEvent(string content)
        {
            if (string.IsNullOrWhiteSpace(content)) return new string[] {"TextEvent", content};

            var prefixIndex = content.IndexOf(":");
            if (prefixIndex <= 0) return new string[] {"TextEvent", content};
            
            var prefix = content.Substring(0, prefixIndex + 1);
            var contentNew = content.Substring(prefixIndex + 1);

            switch (prefix)
            {
                case "0:":
                    return new string[] {"TextEvent", contentNew};
                case "1:":
                    return new string[] {"ProcessEvent", contentNew};
                case "2:":
                    return new string[] {"ErrorEvent", contentNew};
                case "3:":
                    return new string[] {"DataEvent", contentNew};
                case "4:":
                    return new string[] {"DoneEvent", contentNew};
                case "5:":
                    return new string[] {"ReplaceEvent", contentNew};
                case "6:":
                    return new string[] {"ContentReviewEvent", contentNew};
                case "7:":
                    return new string[] {"StreamEvent", contentNew};
                case "8:":
                    return new string[] {"ReasoningEvent", contentNew};
                case "9:":
                    return new string[] {"HeartBeatEvent", contentNew};
                default:
                    return new string[] {"TextEvent", content};
            }
        }
    }
}

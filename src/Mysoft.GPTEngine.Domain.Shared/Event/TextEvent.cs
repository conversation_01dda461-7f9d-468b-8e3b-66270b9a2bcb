using Mysoft.GPTEngine.Domain.Shared.Utilities;

namespace Mysoft.GPTEngine.Domain.Shared.Event
{
    public class TextEvent : EventBase
    {
        public string FlowCode { get; set; }
        public string Value { get; set; }
        public IJsonFixerProperty JsonFixerProperty { get; set; }
        public TextEvent(string flowCode, string value)
        {
            FlowCode = flowCode;
            Value = value;
        }
        public override string ToString()
        {
            return Value;
        }
    }
}

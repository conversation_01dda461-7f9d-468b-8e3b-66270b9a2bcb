namespace Mysoft.GPTEngine.Domain.Shared.Event
{
    public class LlmResultEvent : EventBase
    {
        public CompletionUsage Usage;
        public string Value { get; set; }
        public string FlowCode { get; set; }
        public LlmResultEvent(string flowCode, string value, CompletionUsage usage)
        {
            FlowCode = flowCode;
            Value = value;
            Usage = usage;
        }
    }
    public class CompletionUsage
    {
        public int UsagePromptTokens { get; set; } = 0;

        public int UsageCompletionTokens { get; set; } = 0;

        public int UsageTotalTokens => UsagePromptTokens + UsageCompletionTokens;
    }
}

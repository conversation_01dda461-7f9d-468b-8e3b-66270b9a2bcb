using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Domain.Shared
{
    public interface IApiPlugin
    {
        Task<string> AuthCallBack(HttpHeaders heades, string uri, CancellationToken cancellationToken = default);
        Task<string> PostAsync(string uri, string body, CancellationToken cancellationToken = default);
        
        Task<string> GetAsync(string uri, Dictionary<string,  object> requestParams, CancellationToken cancellationToken = default);
    }
}

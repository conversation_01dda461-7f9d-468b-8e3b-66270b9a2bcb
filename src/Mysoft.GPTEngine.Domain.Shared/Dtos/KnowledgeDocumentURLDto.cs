using System;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class KnowledgeDocumentURLDto
    {
        public KnowledgeDocumentURLDto() { 
            knowledgeImageURLDtos = new List<DocumentURLDto>();
            knowledgeDocumentURLDtos = new List<DocumentURLDto>();
        }

        public List<DocumentURLDto> knowledgeImageURLDtos { get; set; }

        public List<DocumentURLDto> knowledgeDocumentURLDtos { get; set; }
    }

    public class DocumentURLDto {
        public Guid DocumentGUID { get; set; }

        public string DocumentUrl { get; set; }
    }
}

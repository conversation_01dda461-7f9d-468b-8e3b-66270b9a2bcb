using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    /// <summary>
    /// 图片替换占位符Dto
    /// </summary>
    public class ImageUrlDto
    {
        public ImageUrlDto()
        {
            knowledgeFileSectionDtos = new List<KnowledgeFileSectionDto>();
            replaceDtos = new List<ReplaceDto>();
        }

        public List<KnowledgeFileSectionDto> knowledgeFileSectionDtos { get; set; }

        public List<ReplaceDto> replaceDtos { get; set; }
    }

    public class ReplaceDto
    {
        public string key { get; set; }

        public string value { get; set; }
    }
}

using System;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ChatDto : BaseDto
    {
        public Guid ChatGUID { get; set; }
        public Guid ApplicationGUID { get; set; }
        public Guid AssistantGUID { get; set; }
        public Guid SkillGUID { get; set; }
        public Guid? CurrentNodeGUID { get; set; }
        public Guid? CurrentOrchestrationGUID { get; set; }
        
        public string Title { get; set; }
        
        public int IsDeleted { get; set; }
        
        public String CustomerId { get; set; } = string.Empty;
        
        public String CustomerName { get; set; } = string.Empty;
        
        public String UserGUID { get; set; } = string.Empty;
        
        public String UserName { get; set; } = string.Empty;
        
        public String TenantCode { get; set; } = string.Empty;
        
        public String TenantName { get; set; } = string.Empty;
    }
}

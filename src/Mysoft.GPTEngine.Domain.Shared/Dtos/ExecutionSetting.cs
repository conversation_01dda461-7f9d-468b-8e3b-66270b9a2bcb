using System.Collections.Generic;
using System.Text.Json.Serialization;
using Mysoft.GPTEngine.Domain.Shared.Utilities;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ExecutionSetting
    {
        [JsonPropertyName("sectionConfig")]
        public SectionConfig SectionConfig { get; set; }

        public int IsUploadImage;
    }
    
    public class SectionConfig
    {
        [JsonPropertyName("mode")]
        public int Mode { get; set; }
        
        [JsonPropertyName("levelConfig")]
        public LevelConfig LevelConfig { get; set; }
        
        [JsonPropertyName("customConfig")]
        public CustomConfig CustomConfig { get; set; }
    }

    public class LevelConfig
    {
        [JsonPropertyName("maxLevel")]
        public int MaxLevel { get; set; }
        
        [JsonPropertyName("saveMode")]
        public int SaveMode { get; set; }
    }

    public class CustomConfig
    {
        [JsonPropertyName("symbol")]
        public List<int> Symbol { get; set; }
        
        [JsonPropertyName("maxTokensPerParagraph")]
        public int MaxTokensPerParagraph { get; set; }
        
        [JsonPropertyName("overlappingTokens")]
        public int OverlappingTokens { get; set; }

        public string[] GetSplitSymbol()
        {
            if (Symbol == null || Symbol.Count == 0) return null;

            return ExecutionConfigUtil.GetSplitSymbol(Symbol);
        }
        
        
    }
}
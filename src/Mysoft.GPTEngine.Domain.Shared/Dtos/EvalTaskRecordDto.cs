namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class EvalTaskRecordDto<T>
    {
        public string EvalTaskRecordGUID { get; set; }

        public string DocumentGUID { get; set; }

        public string EvalTaskGUID { get; set; }

        public int ExecCount { get; set; }

        public string TaskName { get; set; }

        public string EvalObject { get; set; }

        public int EvalObjectType { get; set; }

        public string ModelInstanceCode { get; set; }

        public T Data { get; set; }
        
        public string DocumentUrl { get; set; }
    }
}
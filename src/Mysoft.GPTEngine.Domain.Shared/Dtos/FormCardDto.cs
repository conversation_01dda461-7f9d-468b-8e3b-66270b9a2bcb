using System;
using System.Collections.Generic;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class FormCardDto
    {
        public string Type { get; set; } = "form";
        public string Code { get; set; }

        public string Next { get; set; } = string.Empty;
        
        public FormCardDataDto Data { get; set; } = new FormCardDataDto();
    }

    public class ProbRule
    {
        public double MaxProb { get; set; }

        public double MinProb { get; set; }

        public int Priority { get; set; }

        public ProbRule(double maxProb, double minProb, int priority)
        {
            this.MaxProb = maxProb;
            this.MinProb = minProb;
            this.Priority = priority;
        }
    }

    public class FormCardDataDto
    {
        public List<FormConfigDto> Config { get; set; } = new List<FormConfigDto>();

        public List<KeyValueDto> Data { get; set; } = new List<KeyValueDto>();
        public string Id { get; set; }
        public string Content { get; set; } = string.Empty;
        public string Describe { get; set; }
        public string TemplateId { get; set; }
        public List<CardParamDto> Props { get; set; }
        public string Title { get; set; }
        public List<ParamDto> Outputs { get; set; }
        public string NodeCode { get; set; }
        public Dictionary<string, int> ProbResult { get; set; } = new Dictionary<string, int>();
        public List<ProbRule> ProbRules { get; set; } = new List<ProbRule>() {
            new ProbRule(85, 0, 0),
            new ProbRule(95, 85, 1),
            new ProbRule(100, 95, 2)
        };
    }

    public class FormConfigDto
    {
        public String Code { get; set; }
        public String Name { get; set; }
        public String Type { get; set; }
        public Boolean Required { get; set; }
    }
    public class KeyValueDto
    {
        public String Key { get; set; } = String.Empty;
        public String Value { get; set; } = String.Empty;
        public String? Desc { get; set; }
        public String? Type { get; set; }
        public String? Code { get; set; }
    }
}

using System;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class DocumentFileDto
    {
        public String downloadUrl {  get; set; }
        public String fileName { get; set; }
        /// <summary>
        /// 知识库code
        /// </summary>
        public String knowledgeCode { get; set; }

        /// <summary>
        /// 来源枚举
        /// </summary>
        public int? FileSourceEnum { get; set; }

        /// <summary>
        /// 第三方id
        /// </summary>
        public String ThirdId { get; set; }

        /// <summary>
        /// 知识库文件guid
        /// </summary>
        public Guid? knowledgeFileGUID { get; set;}

        /// <summary>
        /// 知识库guid
        /// </summary>
        public Guid? knowledgeGUID { get; set; }

        /// <summary>
        /// 知识库切片guid
        /// </summary>
        public Guid? knowledgeFileSectionGUID { get; set; }

        /// <summary>
        /// 知识库文件guid
        /// </summary>
        public List<Guid> KnowledgeFileGUIDs { get; set; }

        /// <summary>
        /// 知识库切片id数组
        /// </summary>
        public List<Guid> knowledgeFileSectionGUIDs { get; set; }
        
        
        /// <summary>
        /// 知识库分片是否隐藏
        /// </summary>
        public int? Disable { get; set; }
    }


    public class KnowledgeSearchDto
    {
        /// <summary>
        /// 知识库切片CODE
        /// </summary>
        public String KnowledgeCode {  get; set; }
        
        /// <summary>
        /// 查询内容
        /// </summary>
        public String Input {  get; set; }
        
        /// <summary>
        /// 返回多少条数据
        /// </summary>
        public int TopK {  get; set; }
        
        /// <summary>
        /// 最小相似度
        /// </summary>
        public double MinScore {  get; set; }
    }

    public class DocumentSectionDto
    {
        /// <summary>
        /// 分片ID
        /// </summary>
        public String KnowledgeFileSectionGUID {  get; set; }

        /// <summary>
        /// 相似度
        /// </summary>
        public double Score {  get; set; }
    }

    public class KnowledgeFileGUIDsDto
    {
        public List<Guid> knowledgeFileGUIDs { get; set; }

        public string knowledgeCode { get; set; }
    }

}

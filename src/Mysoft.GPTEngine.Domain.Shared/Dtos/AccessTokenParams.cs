using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class AccessTokenParams
    {
        [JsonPropertyName("appid")]
        public string AppId { get; set; }
        
        [JsonPropertyName("appsecret")]
        public string AppSecret { get; set; }

        [JsonPropertyName("tenantCode")]
        public string TenantCode { get; set; }
        
        [JsonPropertyName("payload")]
        public AccessTokenContent PayLoad { get; set; }
    }
    
    public class AccessTokenContent
    {
        [JsonPropertyName("userGuid")]
        public string UserGUID { get; set; }
        
        [Json<PERSON>ropertyName("userCode")]
        public string UserCode { get; set; }
        
        [JsonPropertyName("userName")]
        public string UserName { get; set; }

        [Json<PERSON>ropertyName("mobile")]
        public string Mobile { get; set; }
        
        [JsonPropertyName("appType")]
        public string AppType { get; set; }
        
        [JsonPropertyName("appCode")]
        public string AppCode { get; set; }
        
        [JsonPropertyName("appName")]
        public string AppName { get; set; }
        
        [JsonPropertyName("appVersion")]
        public string AppVersion { get; set; }
        
        [JsonPropertyName("moduleCode")]
        public string ModuleCode { get; set; }
        
        [JsonPropertyName("moduleName")]
        public string ModuleName { get; set; }
        
        [JsonPropertyName("pageName")]
        public string PageName { get; set; }
        
        [JsonPropertyName("pageUrl")]
        public string PageUrl { get; set; }
        
        [JsonPropertyName("erpVersion")]
        public string ErpVersion { get; set; }
        
        [JsonPropertyName("metadata")]
        public string Metadata { get; set; }
        
        [JsonPropertyName("customerGuid")]
        public string CustomerGUID { get; set; }
        
        [JsonPropertyName("customerName")]
        public string CustomerName { get; set; }
        
        [JsonPropertyName("tenantCode")]
        public string TenantCode { get; set; }
        
        [JsonPropertyName("tenantName")]
        public string TenantName { get; set; }
        
    }

    public class TokenResult
    {
        [JsonPropertyName("accessToken")]
        public string AccessToken { get; set; }
    }

    public class CheckUserResult
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }
        
        [JsonPropertyName("message")]
        public string Message { get; set; }
        
        [JsonPropertyName("data")]
        public Dictionary<string, object> Data { get; set; }
        
    }
}
using System;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos.approval
{
    public class ExecutePlanParamDto
    {
        public string PlanGUID { get; set; }
        
        public string WorkSpaceCode { get; set; }
        
        public string PlanInstanceGUID { get; set; }
        
        public string BusinessId { get; set; }

        public PlanMessageNodeLogDto PlanMessageNodeLogDto { get; set; }
        
        public Dictionary<string, object> Options { get; set; }
        public Dictionary<string, List<string>> Rules { get; set; }
    }

    public class PlanMessageNodeLogDto
    {
        public Guid ChatGUID { get; set; }
        public Guid NodeGUID { get; set; }
        public Guid BatchGUID { get; set; }
        public string Name { get; set; }
        public int Index { get; set; }
    }

    public class SearchExecuteResultParamDto
    {
        public List<string> BusinessIds { get; set; }
        
        public List<string> InstanceIds { get; set; }
    }

}
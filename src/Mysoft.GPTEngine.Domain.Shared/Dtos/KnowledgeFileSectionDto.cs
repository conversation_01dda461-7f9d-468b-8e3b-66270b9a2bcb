using System;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{

    public class KnowledgeFileSectionDto
    {
        public Guid KnowledgeFileSectionGUID { get; set; }
        public string Content { get; set; }
        public string ParagraphTitle { get; set; }
        public int SectionSize { get; set; }
        public Guid KnowledgeFileGUID { get; set; }
        public int SectionNumber { get; set; }
        public Guid DocumentGUID { get; set; }
        public string FileName { get; set; }
        public string FileType { get; set; }
        public string FileUrl { get; set; }
        public long FileSize { get; set; }
        public int FileSourceEnum { get; set; }
        public string ThirdId { get; set; }
        public string ThirdViewURL { get; set; }
        public int Disable {  get; set; }
        
        public string Metadata { get; set; }

        public string ImagePreviewUrl { get; set; }
        //图片占位符guid
        public List<Guid> imageGUIDs { get; set; }
        
        public List<KnowledgeQuestionDto> Questions { get; set; }

        public List<QuestionDTO> newQuestions { get; set; }
    }

    public class QuestionDTO
    {
        public Guid KnowledgeQuestionRelationGUID { get; set; }

        public Guid? KnowledgeQuestionGUID { get; set; }

        public string Question { get; set; }

        public int Disable {  get; set; }
    }
}

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class SkillMetadataDto
    {
        public Guid Id { get; set; }
        public String Code { get; set; }
        public String Name { get; set; }
        public String Description { get; set; }
        public String Mode { get; set; }
        public String ModelInstanceCode { get; set; }
        public String MetaDataVersion { get; set; }
        public SkillFlowDto Flow { get; set; } = new SkillFlowDto();
        public SkillAgentDto Agent { get; set; } = new SkillAgentDto();
        public List<SkillOrchestrationDto> Orchestrations { get; set; } = new List<SkillOrchestrationDto>();
    }
    public class SkillOrchestrationDto
    {
        public Guid Id { get; set; }
        public String OrchestrationTemplate { get; set; }
        public List<PromptDto> Prompts { get; set; } = new List<PromptDto>();
        public List<PlguinDto> Plguins { get; set; } = new List<PlguinDto>();
    }
    public class PromptDto
    {
        public Guid Id { get; set; }
        public String PromptTemplate { get; set; }
        public List<ParamDto> InputParam { get; set; } = new List<ParamDto>();
        public List<ParamDto> OutputParam { get; set; } = new List<ParamDto>();
        public String ModelInstanceCode { get; set; }
        public Guid? ModelInstanceGUID { get; set; }
        public String ExecutionSetting { get; set; }
        public List<MessageContent> MessageContents { get; set; }
        public string OutputType { get; set; }
    }
    public class MessageContent
    {
        public string Role { get; set; }
        public string Content { get; set; }
        public int Index { get; set; }
    }
    public class PlguinDto
    {
        public Guid Id { get; set; }
        public Guid NodeId { get; set; }
        public String Code { get; set; }
        public String Name { get; set; }
        public PluginTypeEnum PluginType { get; set; }
        public String Yarm { get; set; }
    }
    public class SkillFlowDto
    {
        public List<FlowNode> Nodes { get; set; } = new List<FlowNode>();
    }
    public class FlowNode
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Type { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public NodeConfig Config { get; set; } = new NodeConfig();

        public delegate Task<string> LlmGetStreamingChatMessage(FlowNode flow, CancellationToken cancellationToken);

        public IAsyncEnumerable<StreamingChatMessageContent> StreamingChats { get; set; }
        public List<NodeDependency> _inputDependencies = new List<NodeDependency>();
        public PublishEventConfig PublishEventConfig { get; set; }

        public Stopwatch Stopwatch = new Stopwatch();

        public List<ParamDto> _refParams;
        public List<ParamDto> _outParams;
        public string _serviceId;
        public string? _executionSetting;
        public PublishEventConfig? _publishEventConfig;
        public string _promptGuid;
        public string _documentTxt;
        public string _input;
        public ChatHistory? _chatHistory;
        public string _questionInput;
        public KernelArguments _arguments;
        public bool ExcuteCurrentFlowNode { get; set; } = false;
    }
    public class NodeDependency
    {
        public string NodeCode { get; set; } = string.Empty;
        public LlmGetStreamingChatMessage nodeDependencyHandler { get; set; }
        public List<NodeDependency> ParentDependency { get; set; } = new List<NodeDependency>();
        public bool IsFinish { get; set; }
    }
    public class NodeConfig
    {
        public string Id { get; set; }
        public string AppCode { get; set; }
        public string OutputType { get; set; }
        public string TemplateId { get; set; }
        public string Type { get; set; }
        public string Content { get; set; }
        public string ContentType { get; set; }
        public string OperationId { get; set; }
        public List<ParamDto> Files { get; set; } = new List<ParamDto>();
        public List<CardParamDto> Props { get; set; }
        public List<ParamDto> Inputs { get; set; } = new List<ParamDto>();
        public List<ParamDto> Outputs { get; set; } = new List<ParamDto>();
        public List<Condition> Conditions { get; set; } = new List<Condition>();
        public string Mount { get; set; }
        public SemanticKernel.Core.Dtos.ParamValueDto Url { get; set; }
        public string Title { get; set; }
        public bool Visible { get; set; } = true;
        public bool AsSource { get; set; } = true;
        public bool AsMessage { get; set; } = false;
        public bool Stream { get; set; } = true;
        public bool Async { get; set; } = false;
        public int Pattern { get; set; } = 0;
        public string FormId { get; set; }
        public int Memories { get; set; } = 0;
        public bool IsCOR { get; set; } = false;
        public List<Classification> Classifications { get; set; } = new List<Classification>();
        public string RecognizeType { get; set; }
        public string ImageRecognizeType { get; set; }
        public bool IsEnableOCR => string.Equals(ImageRecognizeType, "ocr", StringComparison.OrdinalIgnoreCase);
        public string OcrService { get; set; }
        public string PluginId { get; set; }
        public string ToolId { get; set; }
        public bool UseTOC { get; set; } = false;
        public string UsePageRange { get; set; } = "all";
        public int UseFirstFewPages { get; set; } = 2;
        public int UseLastFewPages { get; set; } = 2;
        public bool ImagConverter { get; set; } = false;
        public List<string> Knowledges { get; set; } = new List<string>();
    }

    public class Condition
    {
        public string Title { get; set; }
        public string Target { get; set; }
        public string Type { get; set; }
        public string Priority { get; set; }
        public List<Expression> Expressions { get; set; }
        public bool Value => Expressions != null ? Expressions.All(x => x.Value) : true;
    }
    public class Expression
    {
        public string Type { get; set; }
        public List<Rule> Rules { get; set; }
        public bool Value => Type == "and" ? Rules.All(x => x.Value) : Rules.Any(x => x.Value);
    }
    public class Rule
    {
        public ParamDto Left { get; set; }
        public ParamDto Right { get; set; }
        public string Operator { get; set; }
        public bool Value { get; set; } = false;
    }
    public class Classification
    {
        public int Id { get; set; }
        public string Intention { get; set; }
    }
    public class SkillAgentDto
    {
        [JsonProperty("prompt")]
        [JsonPropertyName("prompt")]
        public AgentPromptDto Prompt { get; set; }

        [JsonProperty("tools")]
        [JsonPropertyName("tools")]
        public List<AgentPluginDto> Tools { get; set; }

        [JsonProperty("knowledgs")]
        [JsonPropertyName("knowledgs")]
        public List<AgentKnowledgeDto> Knowledgs { get; set; }

        [JsonProperty("mcps")]
        [JsonPropertyName("mcps")]
        public List<AgentMcpDto> Mcps { get; set; }

        [JsonProperty("inputs")]
        [JsonPropertyName("inputs")]
        public List<ParamDto> Inputs { get; set; } = new List<ParamDto>();

        /**
         * 最大交互次数
         */
        [JsonProperty("maxResultNumber")]
        [JsonPropertyName("maxResultNumber")]
        public int MaxResultNumber { get; set; } = 20;

        /**
         * 最大上下文轮次数
         */
        [JsonProperty("maxContextTurnsNumber")]
        [JsonPropertyName("maxContextTurnsNumber")]
        public int MaxContextTurnsNumber { get; set; } = 4;

        /**
         * 文件类型
         */
        [JsonProperty("uploadables")]
        [JsonPropertyName("uploadables")]
        public String Uploadables { get; set; }

        /**
         * 是否使用文件上传
         */
        [JsonProperty("useFileUpload")]
        [JsonPropertyName("useFileUpload")]
        public bool UseFileUpload { get; set; }

        /**
         * 是否使用扫码上传
         */
        [JsonProperty("useQRCodeUpload")]
        [JsonPropertyName("useQRCodeUpload")]
        public bool UseQRCodeUpload { get; set; }
        
        /**
         * 模型编码
         */
        [JsonProperty("modelInstanceCode")]
        [JsonPropertyName("modelInstanceCode")]
        public String modelInstanceCode { get; set; }
        
        /**
         * 多样性
         */
        [JsonProperty("generationDiversity")]
        [JsonPropertyName("generationDiversity")]
        public String generationDiversity { get; set; }
        
        /**
         * 模型配置
         */
        [JsonProperty("executionSetting")]
        [JsonPropertyName("executionSetting")]
        public executionSettingDto executionSetting { get; set; }
    }
    
    public class executionSettingDto {
        [JsonProperty("temperature")]
        [JsonPropertyName("temperature")]
        public float? temperature { get; set; }

        [JsonProperty("top_p")]
        [JsonPropertyName("top_p")]
        public float? topP { get; set; }

        [JsonProperty("max_tokens")]
        [JsonPropertyName("max_tokens")]
        public int? maxTokens { get; set; }

        [JsonProperty("enable_thinking")]
        [JsonPropertyName("enable_thinking")]
        public bool? enableThinking { get; set; }

        [JsonProperty("thinking_budget")]
        [JsonPropertyName("thinking_budget")]
        public int? thinkingBudget { get; set; }
    }
    
    public class AgentPromptDto {
        [JsonProperty("template")]
        [JsonPropertyName("template")]
        public String Template { get; set; }

        [JsonProperty("inputs")]
        [JsonPropertyName("inputs")]
        public List<ParamDto> Inputs { get; set; } = new List<ParamDto>();
    }
    
    public class AgentPluginDto {
        public String PluginGUID { get; set; }

        public String ToolGUID { get; set; }

        public String ToolCode { get; set; }

        public String Name { get; set; }

        public String Path { get; set; }

        // 入参
        public String Inputs { get; set; }

        // 返回参数
        public String Outputs { get; set; }

        public String Description { get; set; }
    }

    public class AgentKnowledgeDto {
        public String Id { get; set; }

        public String Code { get; set; }

        public String Name { get; set; }

        public String Description { get; set; }
    }

    public class AgentMcpDto {
        [JsonProperty("serviceGUID")]
        [JsonPropertyName("serviceGUID")]
        public String ServiceGUID { get; set; }

        [JsonProperty("serviceCode")]
        [JsonPropertyName("serviceCode")]
        public String ServiceCode { get; set; }

        [JsonProperty("toolGUID")]
        [JsonPropertyName("toolGUID")]
        public String ToolGUID { get; set; }

        [JsonProperty("toolName")]
        [JsonPropertyName("toolName")]
        public String ToolName { get; set; }
    }
}

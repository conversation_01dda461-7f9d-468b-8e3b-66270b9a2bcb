namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class PlanEvalResultDto
    {
        public string ActualNotPassResult { get; set; }

        public string ActualResultStatus { get; set; }

        public string BatchNo { get; set; }

        public string ChatGUID { get; set; }

        public string EvalConclusion { get; set; }

        public string EvalTaskGUID { get; set; }

        public string EvalTaskRecordGUID { get; set; }

        public string ExpectedFailResult { get; set; }

        public string ExpectedResultAmount { get; set; }

        public string PlanName { get; set; }

        public string PlanCode { get; set; }

        public string RuleName { get; set; }

        public string TaskName { get; set; }
        
        public string RuleContent { get; set; }
        
        public string RuleData { get; set; }
        
        public string RuleDataFile { get; set; }
    }
}
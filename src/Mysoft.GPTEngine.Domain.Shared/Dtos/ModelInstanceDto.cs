using System;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ModelInstanceDto
    {
        /// <summary>
        /// 模型实例GUID
        /// </summary>
        public Guid InstanceGUID { get; set; }
        /// <summary>
        /// 模型实例编码
        /// </summary>
        public string InstanceCode { get; set; }
        /// <summary>
        /// 模型实例名称
        /// </summary>
        public string InstanceName { get; set; }
        /// <summary>
        /// 模型GUID
        /// </summary>
        public Guid ModelGUID { get; set; }
        /// <summary>
        /// 模型GUID
        /// </summary>
        public string ModelCode { get; set; }
        /// <summary>
        /// 模型类型
        /// </summary>
        public ModelTypeEnum ModelType { get; set; }
        /// <summary>
        /// 终结点
        /// </summary>
        public string Endpoint { get; set; }
        /// <summary>
        /// 部署名称
        /// </summary>
        public string DeploymentName { get; set; }
        /// <summary>
        /// 密钥
        /// </summary>
        public string ApiKey { get; set; }

        /// <summary>
        /// clientID 百度模型使用
        /// </summary>
        public string ClientID { get; set; }
        
        public string Vendor { get; set; }
        /// <summary>
        /// 默认模型实例
        /// </summary>
        public bool IsDefault { get; set; }
        public ServiceTypeEnum ServiceTypeEnum { get; set; }
        public string fileId { get; set; }
        public int isImg { get; set; } = 0;
        public string StrategyId { get; set; }
        public int EnableCustomModel { get; set; }
        public String CustomModelCode { get; set; }
        public bool IsAvailable { get; set; }
        public int IsSupportTool { get; set; }
        public int SupportDeepThink { get; set; }
    }
}

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class CreateChatInputDto
    {
        public Guid SkillGUID { get; set; }
        public List<KeyValueDto> Arguments { get; set; }
    }

    public class ChatInputDto
    {
        public string Input { get; set; }
        public Guid ChatGUID { get; set; }
        public Guid AssistanGUID { get; set; }
        public Guid SkillGUID { get; set; }
        public List<KeyValueDto> Arguments { get; set; }
        public bool Next { get; set; }
        public string NextId { get; set; }
        public List<ChatMessageFileInputDto> Documents { get; set; }
        public List<ChatMessageFileInputDto> Images { get; set; }
        [JsonIgnore]
        public bool? CheckContent { get; set; }
        public bool? SaveLog { get; set; }
        public Guid? ApplicationGUID { get; set; }
        public Guid? EvalTaskGUID { get; set; }
        public bool Run  { get; set; }
    }

    public class ChatMessageFileInputDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string PreviewUrl { get; set; }
        public string DownloadUrl { get; set; }
        public string Size { get; set; }
        public string Status { get; set; }
        public string Type { get; set; }
    }
}

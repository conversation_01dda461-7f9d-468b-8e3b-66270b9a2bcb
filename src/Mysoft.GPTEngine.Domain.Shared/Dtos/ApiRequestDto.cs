using System;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ApiRequestDto
    {
        public string? uri { get; set; } 
        public string? jsonBody { get; set; } 
    }

    public class ThirdApiRequestDto
    {
        public List<ArgumentsDTO> Arguments { get; set; }

        public string PluginGUID { get; set; }
    }

    public class ArgumentsDTO
    {
        public String Key { get; set; }

        public String Value { get; set; }
    }
}

using System;
using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class ParamMappingRequestDto
    {
        public List<ParameterDto> source { get; set; } = new List<ParameterDto>();
        public List<ParameterDto> target { get; set; } = new List<ParameterDto>();
    }
    
    public class ParameterDto
    {
        public String Code { get; set; }
        public String Name { get; set; }
        public String Type { get; set; }
        public String Description { get; set; }
    }

}

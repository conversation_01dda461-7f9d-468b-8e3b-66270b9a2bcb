using System.Collections.Generic;
using Microsoft.SemanticKernel;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class MysoftActionDto<T> : MysoftActionDto
    {
        public T Data { get; set; }
    }
    public class MysoftActionDto : MysoftContext
    {
        public KernelArguments CreateKernelArguments()
        {
            var arguments = new KernelArguments();
            arguments.Add(nameof(UserContext.UserCode), UserContext.UserCode);
            arguments.Add(nameof(UserContext.UserName), UserContext.UserName);
            arguments.Add(nameof(TenantCode), TenantCode);
            return arguments;
        }
        public KernelArguments Arguments { get; set; }
        public List<ModelInstanceDto> ModelInstanceDtos { get; set; }
    }
}

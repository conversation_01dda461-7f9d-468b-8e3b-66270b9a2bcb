using System;
using Microsoft.SemanticKernel.Connectors.Milvus;
using Milvus.Client;
using Mysoft.GPTEngine.Common.CustomerException;

namespace Mysoft.GPTEngine.Domain.Shared.Dtos
{
    public class MysoftContext
    {
        public UserContext UserContext { get; set; } = new UserContext();
        public String TenantId { get; set; }
        public String TenantCode { get; set; }
        public String GptBuilderUrl { get; set; }
        public MemoryStore MemoryStore { get; set; } = new MemoryStore();
        
        public String TenantServiceUrl { get; set; }
        public AuthInfo AuthInfo { get; set; }

        /// <summary>
        /// 认证方式 1代表AccessToken 0代表PlatformToken
        /// </summary>
        public int AuthType { get; set; } = 1;

        public int AuthorizationType { get; set; } = 0;
        public int EnableUserAuthorization { get; set; }
        public string ApplicationPublisherUserCode { get; set; }
    }

    public class UserContext
    {
        public Guid UserId;
        public String UserCode;
        public String UserName;
        public bool EnableApplicationUserAuthorization { get; set; }
    }


    public class MemoryStore
    {
        public String Host { get; set; }
        public int Port { get; set; } = 19530;
        public String UserName { get; set; }
        public String Password { get; set; }

        public MilvusClient CreateMilvusClient(string database = null, bool isThrowException = true)
        {
            if (string.IsNullOrEmpty(Host))
            {
                if (isThrowException)
                {
                    throw new VectorException();
                }
                else
                {
                    return null;
                }
            }

            return new MilvusClient(Host, UserName, Password, Port, ssl: false, database: database);
        }
#pragma warning disable SKEXP0020
        public MilvusMemoryStore CreateMilvusMemoryStore(string tenantCode)
        {
            return new MilvusMemoryStore(Host, UserName, Password, Port, ssl: false, database: tenantCode);
        }
    }

    public class AuthInfo
    {
        public String ClientId { get; set; }

        public String Jwks { get; set; }
    }
}
using System;
using System.Collections.Generic;
using Mysoft.GPTEngine.Common.Helper;
using Newtonsoft.Json;
using Xunit;

namespace Mysoft.GPTEngine.Domain.Shared.Test
{
    public class JsonFixerTests
    {
        [Theory]
        [InlineData("```json{'k':'v'}```")]
        [InlineData("```json{'k':'v'}")]
        [InlineData("```json{'k':'v'}```<\uff5cend\u2581of\u2581sentence\uff5c>")]
        [InlineData("```json{\n  \"RegisteredPhone\": \"投标人：中建三局第三建设工程有限责任公司（标书1）与投标人：湖北楚天大地建设工程有限公司（标书3）的注册电话“02788889988” 重复\",\n  \"RegisteredEmail\": \"1\",\n  \"RegistrationAddress\": \"1\",\n  \"idnumber\": \"1\",\n  \"PhoneNumber\": \"1\",\n  \"CertificateName\": \"1\",\n  \"ProviderName\": \"投标人：湖北楚风建设工程有限公司（标书2）与投标人：湖北楚天大地建设工程有限公司（标书3）的单位名称“湖北楚天大地建设工程有限公司”重复\",\n  \"Bidder\": \"1\"\n}```")]
        [InlineData("```json\n\n{\n\n    \"items\": [\n\n        {\n\n            \"name\": \"B13栋\"\n\n        },\n\n        {\n\n            \"name\": \"B14栋\"\n\n        }\n\n    ],\n\n    \"unitName\": \"基坑支护工程\"\n\n}}\n\n```")]
        [InlineData("```json\n\n{\n\n  \"RegisteredPhone\": \"投标人：中建三局第三建设工程有限责任公司（标书1）与投标人：湖北楚天大地建设工程有限公司（标书3）的注册电话“02788889988”重复\",\n\n  \"ProviderName\": \"投标人：湖北楚风建设工程有限公司（标书2）与投标人：湖北楚天大地建设工程有限公司（标书3）的单位名称“湖北楚天大地建设工程有限公司”重复\"\n\n}\n\n``` \n\n\n\n根据给定的文档内容，以下是详细的对比结果：\n\n\n\n### 注册电话\n\n- **标书1（中建三局第三建设工程有限责任公司）**: 02788889988\n\n- **标书2（湖北楚风建设工程有限公司）**: 13085251222\n\n- **标书3（湖北楚天大地建设工程有限公司）**: 02788889988\n\n\n\n\n最终输出结果：\n\n\n\n```json\n\n{\n\n  \"RegisteredPhone\": \"投标人：中建三局第三建设工程有限责任公司（标书1）与投标人：湖北楚天大地建设工程有限公司（标书3）的注册电话“02788889988”重复\",\n\n  \"RegisteredEmail\": \"1\",\n\n  \"RegistrationAddress\": \"1\"\n\n}\n\n```")]
        [InlineData("```json\n\n{\n\n  \"RegisteredPhone\": \"投标人：中建三局第三建设工程有限责任公司（标书1）与投标人：湖北楚天大地建设工程有限公司（标书3）的注册电话“02788889988”重复\",\n\n  \"RegisteredEmail\": \"1\",\n\n  \"RegistrationAddress\": \"1\",\n\n  \"idnumber\": \"1\",\n\n  \"PhoneNumber\": \"1\",\n\n  \"CertificateName\": \"1\",\n\n  \"ProviderName\": \"投标人：湖北楚风建设工程有限公司（标书2）与投标人：湖北楚天大地建设工程有限公司（标书3）的单位名称“湖北楚天大地建设工程有限公司”重复\",\n\n  \"Bidder\": \"1\"\n\n}\n\n```<|end_of_sentence|>")]
        [InlineData("根据匹配规则和科目数据要求，分析结果如下：\n\n```json\n{\n  \"items\": [\n    {\"itemId\": \"i_29\", \"tranScope\": \"1\"},\n    {\"itemId\": \"i_70\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_87\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_104\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_134\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_148\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_165\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_195\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_205\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_222\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_291\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_372\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_487\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_625\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_637\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_653\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_688\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_714\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_748\", \"tranScope\": \"0\"},\n    {\"itemId\": \"i_788\", \"tranScope\": \"0\"}\n  ]\n}\n```\n\n### 匹配说明：\n1. **唯一有效项**：仅`i_29`（防水涂膜）符合全部条件：\n   - 分部`露台工程`匹配科目\"屋面防水施工面积\"\n   - 单位`M2`与科目单")]
        [InlineData("```json[{\"rule_id\":\"规则1\",\"conditions\":[\"检查降价三次房间是否为空\",\"当降价三次房间不为空时，需识别并输出不符合规则的数据项\"],\"status\":false,\"message\":\"有x套数房间连续降价3次及以上，包括a，b，c。\"}]]```")]
        // [InlineData("{\n  \"buyers\": [\n    {\n      \"cstname\": \"梁丽君\",\n      \"csttype\": \"个人\",\n      \"cardtype\": \"身份证\",\n      \"tel\": \"18650778991\",\n      \"cardid\": \"652526197210270726\",\n      \"address\": \"福州市台江区天花园G座217号\"\n    }\n  ],\n  \"qsdate\": \"2024-08-01\",\n  \"yqydate\": \"2021-08-26\",\n  \"isorderattachment\": \"true\",\n  \"bldArea\": \"87.5\",\n  \"cjrmbtotal\": \"1973904\",\n  \"projname\": \"北城新居-一期\",\n  \"roominfo\": \"北城新居-一期-9-503\",\n  \"bldname\": \"9\",\n  \"unit\": \"\",\n  \"room\": \"503\"\n}啊啊啊啊")]
        // [InlineData("{\n  \"RegisteredPhone\": \"投标人：中建三局第三建设工程有限责任公司（标书1）与投标人：湖北楚天大地建设工程有限公司（标书3）的注册电话"02788889988"重复\",\n  \"RegisteredEmail\": \"1\",\n  \"RegistrationAddress\": \"1\",\n  \"idnumber\": 1,\n  \"PhoneNumber\": 1,\n  \"CertificateName\": \"1\",\n  \"ProviderName\": \"投标人：湖北楚风建设工程有限公司（标书2）与投标人：湖北楚天大地建设工程有限公司（标书3）的单位名称"湖北楚天大地建设工程有限公司"重复\",\n  \"Bidder\": \"1\"\n}AAAAAAA")]
        // [InlineData("{\n  \"ContractName\": \"建设项目工程总承包合同\",\n  \"ContractCode\": \"\",\n  \"HtTypeName\": \"管理类-资产采购类\",\n  \"JfProviderName\": \"湖北联新产城建设开发有限公司\",\n  \"YfProviderName\": \"湖北省路桥集团天夏建设有限公司,湖北省建筑设计院有限公司\",\n  \"BfProviderNames\": \"\",\n  \"ProjName\": \"联投未来城项目一期工程总承包\",\n  \"SignDate\": \"\",\n  \"ContractValidDate\": \"\",\n  \"HtAmountBz\": 645,442,388.92,\n  \"BzName\": \"人民币\",\n  \"ContractFkConditionGrid\": [\n    {\n      \"FundParamName\": \"销售费用-预付款\",\n      \"FKRate\": \"70\",\n      \"FKAmount\": \"451,809,672.24\",\n      \"FkDate\": \"\",\n      \"FKCondition\": \"地下室结构按后浇带分区域完成50%或100%\"\n    },\n    {\n      \"FundParamName\": \"销售费用-进度款\",\n      \"FKRate\": \"70\",\n      \"FKAmount\": \"451,809,672.24\",\n      \"FkDate\": \"\",\n      \"FKCondition\": \"单栋达到预售节点\"\n    },\n    {\n      \"FundParamName\": \"销售费用-结算款\",\n      \"FKRate\": \"80\",\n      \"FKAmount\": \"516,353,911.14\",\n      \"FkDate\": \"\",\n      \"FKCondition\": \"单栋外部竣工验收完成，取得竣工验收备案证\"\n    },\n    {\n      \"FundParamName\": \"销售费用-质保金\",\n      \"FKRate\": \"1.5\",\n      \"FKAmount\": \"9,681,635.83\",\n      \"FkDate\": \"\",\n      \"FKCondition\": \"缺陷责任期满\"\n    }\n  ],\n  \"ContractTaxItemGrid\": [\n    {\n      \"TaxItem\": \"6%\",\n      \"HtAmount\": \"3,770,867.10\",\n      \"HtNonTaxAmount\": \"3,557,421.79\",\n      \"HtInputTaxAmount\": \"213,445.31\"\n    },\n    {\n      \"TaxItem\": \"9%\",\n      \"HtAmount\": \"641,671,521.82\",\n      \"HtNonTaxAmount\": \"588,689,469.56\",\n      \"HtInputTaxAmount\": \"52,982,052.26\"\n    }\n  ],\n  \"UtilityReceiveGrid\": [\n    {\n      \"PayProviderName\": \"湖北联新产城建设开发有限公司\",\n      \"ReceiveProviderName\": \"湖北省路桥集团天夏建设有限公司\",\n      \"BankAccount\": \"42050125813600000083\",\n      \"PayeeAmount\": \"645,442,388.92\"\n    },\n    {\n      \"PayProviderName\": \"湖北联新产城建设开发有限公司\",\n      \"ReceiveProviderName\": \"湖北省建筑设计院有限公司\",\n      \"BankAccount\": \"42050186860800002855\",\n      \"PayeeAmount\": \"645,442,388.92\"\n    }\n  ],\n  \"UtilityFtDetail\": [\n    {\n      \"SpecialBusinessUnitFullName\": \"\",\n      \"CostFullName\": \"\",\n      \"SourceDate\": \"\",\n      \"FtAmount\": \"\"\n    }\n  ],\n  \"yftaxpayeridentification\": \"\",\n  \"yfbankaccount\": \"\",\n  \"jftaxpayeridentification\": \"\",\n  \"jfbankaccount\": \"\"\n}")]
        [InlineData("{\"result\":[{\"sheetlype\":\"分部分项\",\"sheetNames\":[\"8-土建分部分项工程\"]}}", true)]
        [InlineData("{\n        \"result\": [\n            {\n                \"name\": \"B13栋\"\n            },\n            {\n                \"name\": \"B14栋\"\n            }\n        ],\n        \"unitName\": \"基坑支护工程\"\n  ")]
        public void ItReturnsNullChatHistoryWhenPromptIsPlainTextOrInvalid(string jsonString, bool shouldBeValid = true)
        {
            // Act
            var fixString = JsonValidateHelper.CleanUpJson(jsonString);
            
            // Assert
            Assert.True(IsValidJson(jsonString, fixString));
        }
        
        private bool IsValidJson(string jsonString, string strInput)
        {
            try
            {
                // JToken.Parse(strInput);
                var dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(strInput);
                Console.WriteLine("原文：{0}", jsonString);
                Console.WriteLine("修复：{0}", strInput);
                return true;
            }
            catch (Exception e)
            {
                Console.Error.WriteLine("JSON读取失败：{1}\n{0}", strInput, e.Message);
                return false;
            }
        }
    }
}
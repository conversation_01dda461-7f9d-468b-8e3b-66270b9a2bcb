using System;

namespace Mysoft.GPTEngine.Domain.Shared.Entity
{
    public interface IBaseEntity
    {
        public Guid? CreatedGUID { get; set; }
        public string CreatedName { get; set; }
        public DateTime? CreatedTime { get; set; }
        public Guid? ModifiedGUID { get; set; }
        public string ModifiedName { get; set; }
        public DateTime? ModifiedTime { get; set; }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain
{
    public class MysoftFlowApiService : ApiDomainService
    {

        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly ILogger<MysoftFlowApiService> _logger;
        private HttpClient _httpClient;
        private IConfigurationService _configurationService;
        public MysoftFlowApiService(IMysoftContextFactory mysoftContextFactory, IHttpClientFactory clientFactory, IConfigurationService configurationService
            , ILogger<MysoftFlowApiService> logger):base(configurationService)
        {
            _mysoftContextFactory = mysoftContextFactory;
            _logger = logger;
            _httpClient = clientFactory.CreateClient("Mysoft");
            _httpClient.Timeout = TimeSpan.FromMinutes(30);
            _configurationService = configurationService;
        }
        
        public async Task<string> AuthCallBack(HttpHeaders heades, string uri, string appCode, CancellationToken cancellationToken = default)
        {
            var mysoftContext = _mysoftContextFactory.GetMysoftContext();
            var myApiAuthorizationToken = await GetMyApiAuthorization(mysoftContext);
            if (string.IsNullOrWhiteSpace(myApiAuthorizationToken) == false)
            {
                heades.Add("my-api-Authorization", myApiAuthorizationToken);
            }
            AddHeaders(mysoftContext, heades, appCode);
            heades.Add("tenantCode", mysoftContext.TenantCode);
            return await Task.FromResult(uri);
        }
        
        private void AddHeaders(MysoftContext mysoftContext, HttpHeaders heades, string appCode) {
            string tenantCode = mysoftContext.TenantCode;
            string userCode = mysoftContext.UserContext.UserCode;
            Dictionary<string, object> aesParams = new Dictionary<string, object>();
            aesParams.Add("messageCode", userCode);
            aesParams.Add("timespan", DateTimeOffset.UtcNow.ToUnixTimeSeconds());
            aesParams.Add("__app_tenant_id", tenantCode);
            aesParams.Add("__app_code", "bpm");
            aesParams.Add("mysoft_app_code", appCode);

            String token = AesHelper.Encrypt(JsonConvert.SerializeObject(aesParams), "1qaz7410");
            heades.Add("mysoft-app-code", appCode);
            heades.Add("logintoken", token);

        }

        public Task<string> PostAsync(string uri, string body, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException();
        }

        public async Task<string> GetAsync(string uri, Dictionary<string, object> requestParams, string appCode, CancellationToken cancellationToken = default)
        {
            var mysoftContext = _mysoftContextFactory.GetMysoftContext();
            
            // 解析基础 URL
            Uri baseUri = new Uri(_configurationService.GetConfigurationItemByKey(EMCConfigConst.BpmUrl) + "/api/v20/process-gpt/get-wf-data-info");

            // 获取现有的查询字符串
            string existingQuery = baseUri.Query.TrimStart('?');

            // 拼接新的查询字符串
            string newQuery = string.Join("&", requestParams.Select(kvp => $"{kvp.Key}={HttpUtility.UrlEncode(kvp.Value == null ? "" : kvp.Value.ToString())}"));

            // 如果已有查询字符串，则合并
            string finalQuery = string.IsNullOrEmpty(existingQuery) ? newQuery : $"{existingQuery}&{newQuery}";

            // 构建最终的 URL
            string portPart = baseUri.Port > 0 ? $":{baseUri.Port}" : "";
            string url = $"{baseUri.Scheme}://{baseUri.Host}{portPart}{baseUri.PathAndQuery.TrimStart('?')}{(string.IsNullOrEmpty(baseUri.PathAndQuery.TrimStart('?')) ? "" : "?")}{finalQuery}";
            
            var httpContent = new StringContent("");
            
            url = await AuthCallBack(httpContent.Headers, url, appCode);
            return await this.SendRequestAsync(_httpClient, url, HttpMethod.Get, httpContent, cancellationToken);
        }
    }
}
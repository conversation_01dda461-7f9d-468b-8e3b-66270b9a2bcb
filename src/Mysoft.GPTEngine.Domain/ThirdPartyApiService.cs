using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain
{
    public class ThirdPartyApiService : ApiDomainService, IThirdPartyApiService
    {
        private IHttpContextAccessor _httpContextAccessor;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private Kernel _kernel;
        public ThirdPartyApiService(IHttpClientFactory clientFactory, IHttpContextAccessor httpContextAccessor, Kernel kernel,IConfigurationService configurationService, IMysoftContextFactory mysoftContextFactory):base(configurationService)
        {
            _httpContextAccessor = httpContextAccessor;
            _mysoftContextFactory = mysoftContextFactory;
            _kernel = kernel;
        }
        public async Task<string> AuthCallBack(HttpHeaders heades, string uri, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }

        public async Task<string> PostAsync(string uri, string body, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }

        public async Task<string> GetAsync(string uri, Dictionary<string, object> requestParams, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }
    }
}

<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <NoWarn>$(NoWarn);SKEXP0001,SKEXP0060</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspose.Cells" Version="20.7.0" />
<!--    <PackageReference Include="Aspose.PDF" Version="25.7.0" />-->
    <PackageReference Include="Aspose.PDF.Drawing" Version="25.7.0" />
    <PackageReference Include="Aspose.Words" Version="21.12.0" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Markdig" Version="0.37.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Milvus" Version="1.60.0-alpha" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.OpenApi" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Agents.Core" Version="1.60.0" />
    <PackageReference Include="ModelContextProtocol" Version="0.3.0-preview.2" />
    <PackageReference Include="ModelContextProtocol.Core" Version="0.3.0-preview.2" />
    <PackageReference Include="Mysoft.UnifiedApplicationAuthentication.Client" Version="1.0.6" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
    <PackageReference Include="PdfPig" Version="0.1.8" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.80.2" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.154" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mysoft.GPTEngine.Common\Mysoft.GPTEngine.Common.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Domain.Shared\Mysoft.GPTEngine.Domain.Shared.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.SemanticKernel.Core\Mysoft.GPTEngine.SemanticKernel.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="McpServerEnhancement\" />
    <Folder Include="Repositories\Approval\Datasource\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="AgentSkillPlugin\TransportAdapter.cs" />
    <Compile Remove="AgentSkillPlugin\StreamableHttpMcpClient.cs" />
  </ItemGroup>

</Project>

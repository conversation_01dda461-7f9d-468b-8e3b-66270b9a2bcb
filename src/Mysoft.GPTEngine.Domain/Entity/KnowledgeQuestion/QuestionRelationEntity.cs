using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion
{
    [SugarTable("gpt_KnowledgeQuestionRelation")]
    public class QuestionRelationEntity: BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeQuestionRelationGUID { get; set; }

        public Guid KnowledgeGUID { get; set; }

        public Guid KnowledgeQuestionGUID { get; set; }

        public Guid KnowledgeSectionGUID { get; set; }

        public int Disable { get; set; }
    }
}

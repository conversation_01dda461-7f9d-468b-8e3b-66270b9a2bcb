using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_PublishedSkill")]
    public class PublishedSkillEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid PublishedSkillGUID { get; set; }
        public Guid SkillGUID { get; set; }
        public String SkillVersions { get; set; }
        public String Metadata { get; set; }
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeFileSectionQuestion")]
    public class KnowledgeQuestionEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeFileSectionQuestionGUID { get; set; }

        public Guid KnowledgeFileSectionGUID { get; set; }

        public string Question { get; set; }
        
        public string Source { get; set; }
    }
}

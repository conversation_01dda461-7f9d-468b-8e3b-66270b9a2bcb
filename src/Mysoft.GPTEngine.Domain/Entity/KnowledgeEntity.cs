using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_Knowledge")]
    public class KnowledgeEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeGUID { get; set; }

        public string Code { get; set; }

        public string Description { get; set; }

        public int IsSystem { get; set; }

        public string Name { get; set; }

        public int SearchServicesEnum { get; set; }

        public string SearchServicesParams { get; set; }

        public string SearchServicesURL { get; set; }

        public int StatusEnum { get; set; }

        public int TypeEnum { get; set; }

        public string EmbeddingModelCode { get; set; }

        public int IsHasQuestion { get; set; }
    }
}

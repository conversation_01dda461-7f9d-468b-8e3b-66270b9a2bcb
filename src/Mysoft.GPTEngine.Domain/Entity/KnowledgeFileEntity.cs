using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeFile")]
    public class KnowledgeFileEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeFileGUID { get; set; }

        public Guid DocumentGUID { get; set; }

        public int QuestionGenerateEnum { get; set; }

        public string FileName { get; set; }

        public string FileType { get; set; }
        public long FileSize { get; set; } = 0;

        public int IndexStatusEnum { get; set; }

        public Guid KnowledgeGUID { get; set; }

        public int SectionTypeEnum { get; set; }

        public int IsUploadImage { get; set; }

        public int FileSourceEnum { get; set; }

        public String ThirdId { get; set; }

        public string ThirdViewURL { get; set; }
        
        public string ExecutionSetting { get; set; }
    }
}

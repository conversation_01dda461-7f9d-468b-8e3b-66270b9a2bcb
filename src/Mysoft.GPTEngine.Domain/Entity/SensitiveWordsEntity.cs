using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_SensitiveWords")]
    public class SensitiveWordsEntity: BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid SensitiveWordsGUID {  get; set; }

        public int IsEnabled {  get; set; }

        public string Reply { get; set; }

        public string SensitiveWordCode { get; set; }

        public string SensitiveWordName { get; set; }

        public string SensitiveWords { get; set; }

        public Guid SpaceGUID { get; set; }
    }
}

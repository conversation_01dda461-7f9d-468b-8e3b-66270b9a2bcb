using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_PromptTestScene")]
    public class PromptTestSceneEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid PromptTestSceneGUID { get; set; }
        public Guid PromptGUID { get; set; }
        public string PromptParams { get; set; }
        public int ExcuteStatusEnum { get; set; }
        public string ExpectContent { get; set; }
        public string OutputContent { get; set; }
        public string EstimateContent { get; set; }
        public decimal EstimateRecord { get; set; } = 0;
    }
}

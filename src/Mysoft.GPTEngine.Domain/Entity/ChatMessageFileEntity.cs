using System;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ChatMessageFile")]
    public class ChatMessageFileEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatMessageFileGUID { get; set; }
        public Guid ChatMessageGUID { get; set; }
        public Guid FileId { get; set; }
        public string FileName { get; set; }
        [SugarColumn(ColumnName = "PreviewUrl")]
        public string _previewUrl { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string PreviewUrl
        {
            get
            {
                return _previewUrl.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._previewUrl = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }
        [SugarColumn(ColumnName = "DownloadUrl")]
        public string _downloadUrl { get; set; }
        [SugarColumn(IsIgnore = true)]
        public string DownloadUrl
        {
            get
            {
                return _downloadUrl.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._downloadUrl = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }

        public string Size { get; set; }
        public string Status { get; set; }
        public string Type { get; set; }
        public string NodeType { get; set; } 
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_Assistant")]
    public class AssistantEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid AssistantGUID { get; set; }
        public String AssistantCode { get; set; }
        public String AssistantName { get; set; }
        public int OpenCheck { get; set; } = 0;
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeFileSection")]
    public class KnowledgeFileSectionEntity:BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeFileSectionGUID { get; set; }

        public string Content { get; set; }

        public int SectionSize { get; set; }
        
        public int SectionTokens { get; set; }

        public Guid KnowledgeFileGUID { get; set; }

        public int SectionNumber { get; set; }

        public int FileSourceEnum { get; set; }

        public string ThirdId { get; set; }

        public int Disable { get; set; } = 0;
        
        public string ParagraphTitle { get; set; }
        
        public string Metadata { get; set; }
        
        public string ExecutionSetting { get; set; }
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_mcpservicetool")]
    public class McpServiceToolEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string ToolGUID { get; set; }

        [SugarColumn(ColumnName = "VersionNumber")]
        public DateTime VersionNumber { get; set; }

        public string ServiceGUID { get; set; }

        public string ToolName { get; set; }

        public string ToolTitle { get; set; }

        public string ToolDescription { get; set; }

        public string InputSchema { get; set; }

        public string OutputSchema { get; set; }

        public string LastTestInput { get; set; }

        public string LastTestOutput { get; set; }

        public string SpaceGUID { get; set; }

        public int Status { get; set; } = 1;
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ModelInstance")]
    public class ModelInstanceEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid InstanceGUID { get; set; }
        public Guid ModelGUID { get; set; }
        public String InstanceName { get; set; }
        public String InstanceCode { get; set; }
        public bool IsDefault { get; set; }
        public String Endpoint { get; set; }
        public String DeploymentName { get; set; }
        public String ApiKey { get; set; }
        public String ClientID { get; set; }
        public String Describe { get; set; }
        public String Vendor { get; set; }
        public string StrategyId { get; set; }
        public bool IsAvailable { get; set; }
        public int EnableCustomModel { get; set; }
        public string CustomModelCode { get; set; }
        public int IsSupportTool { get; set; }
        public int SupportDeepThink { get; set; }
    }
}

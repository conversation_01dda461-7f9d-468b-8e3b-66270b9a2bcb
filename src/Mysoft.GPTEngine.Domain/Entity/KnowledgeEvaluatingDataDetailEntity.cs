using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeEvaluatingDataDetail")]
    public class KnowledgeEvaluatingDataDetailEntity:BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeGUID { get; set; }

        public String Answer { get; set; }

        public String KeyWord { get; set; }

        public Guid KnowledgeEvaluatingDataGUID { get; set; }

        public String Question { get; set; }
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_KnowledgeEvaluatingTaskDetailRecord")]
    public class KnowledgeEvaluatingTaskRecordEntity:BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid KnowledgeTaskRecordGUID { get; set; }

        public string Content {  get; set; }

        public string FileName { get; set; }

        public string OriginFileName { get; set; }

        public string KnowledgeName { get; set; }

        public Guid KnowledgeSectionGUID { get; set; }

        public Guid KnowledgeFileGUID { get; set; }


        public Guid KnowledgeTaskDetailGUID { get; set; }

        public double Score { get; set; }

        public int SectionNumber { get; set; }

        public string Title { get; set; }

        public string Icon { get; set; }
    }
}

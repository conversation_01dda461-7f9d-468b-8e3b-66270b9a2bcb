using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.eval
{
    [SugarTable("gpt_EvalTask")]
    public class EvalTaskEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string EvalTaskGUID { get; set; }

        public int Status { get; set; }
        
        public string LastRecordGUID { get; set; }
        
        
    }
}
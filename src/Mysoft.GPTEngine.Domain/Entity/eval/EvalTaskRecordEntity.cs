using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.eval
{
    [SugarTable("gpt_EvalTaskRecord")]
    public class EvalTaskRecordEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string EvalTaskRecordGUID { get; set; }

        public string DocumentGUID { get; set; }

        public string ErrorMsg { get; set; }

        public string EvalTaskGUID { get; set; }

        public int ExecCount { get; set; }

        public int ExecStatus { get; set; }

        public string TaskName { get; set; }
        
        public int SuccessCount { get; set; }
        
        public int Total { get; set; }
        
        public int ConclusionCount { get; set; }
        
        public int ExecutionTime { get; set; }
        
    }
}
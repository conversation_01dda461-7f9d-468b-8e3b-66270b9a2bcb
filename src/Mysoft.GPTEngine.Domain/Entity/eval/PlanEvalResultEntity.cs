using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.eval
{
    [SugarTable("gpt_PlanEvalResult")]
    public class PlanEvalResultEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PlanEvalResultGUID { get; set; }

        public string ActualNotPassResult { get; set; }

        public string ActualResultStatus { get; set; }

        public int BatchNo { get; set; }

        public string ChatGUID { get; set; }

        public string EvalConclusion { get; set; }

        public string EvalTaskGUID { get; set; }

        public string EvalTaskRecordGUID { get; set; }

        public string ExpectedFailResult { get; set; }

        public string ExpectedResultAmount { get; set; }

        public string PlanName { get; set; }

        public string PlanCode { get; set; }

        public string RuleName { get; set; }

        public string TaskName { get; set; }
        
        public int ExecutionTime { get; set; }
    }
}
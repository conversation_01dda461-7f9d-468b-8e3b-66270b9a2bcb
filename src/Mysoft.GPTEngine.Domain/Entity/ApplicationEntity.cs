using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_Application")]
    public class ApplicationEntity : BaseEntity
    {
        
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ApplicationGUID { get; set; }
        
        public string ApplicationCode { get; set; }

        public int EnableUserAuthorization { get; set; } = 0;

        public int AuthorizationType { get; set; } = 0;

    }
}
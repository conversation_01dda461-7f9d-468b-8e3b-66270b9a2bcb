using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ApplicationSecurity")]
    public class ApplicationSecurityEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ApplicationSecurityGUID { get; set; }
        
        public Guid ApplicationGUID { get; set; }
        
        public string AppSecret { get; set; }
        
        public string UserCheckUrl { get; set; }
        
    }
}
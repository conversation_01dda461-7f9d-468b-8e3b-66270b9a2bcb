using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_PromptTemplate")]
    public class PromptTemplateEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid PromptTemplateGUID { get; set; }
        public Guid PromptGUID { get; set; }
        public String PromptTemplate { get; set; }
        
        public String MessageContent { get; set; }

    }
}

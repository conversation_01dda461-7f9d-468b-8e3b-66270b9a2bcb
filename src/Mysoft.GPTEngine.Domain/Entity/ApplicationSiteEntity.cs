using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ApplicationSite")]
    public class ApplicationSiteEntity : BaseEntity
    {
        
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ApplicationSiteGUID { get; set; }
        
        public Guid ApplicationGUID { get; set; }
        
        public string SiteDomainName { get; set; }
        
    }
}
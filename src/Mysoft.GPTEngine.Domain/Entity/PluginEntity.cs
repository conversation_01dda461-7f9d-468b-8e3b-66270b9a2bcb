using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_Plugin")]
    public class PluginEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PluginGUID { get; set; }
        public string PluginCode { get; set; }
        public int Source { get; set; }

        public int AuthMode { get; set; }

        public string CreateTokenUrl { get; set; }

        public string ParamList { get; set; }

        public string PluginUrl { get; set; }
    }
}
using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.Approval
{
    [SugarTable("gpt_planRuleDataInstance")]
    public class PlanRuleDataInstanceEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public String PlanRuleDataInstanceGUID { get; set; }
        
        public String PlanInstanceGUID { get; set; }
        
        public String PlanRuleInstanceGUID { get; set; }
        
        public String Data { get; set; }

        public String Schema { get; set; }
        
        public String PlanDatasourceInstanceGUID { get; set; }
    }
}
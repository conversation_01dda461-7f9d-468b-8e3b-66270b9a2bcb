using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.Approval
{
    [SugarTable("gpt_planDatasourceInstance")]
    public class PlanDatasourceInstanceEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PlanDatasourceInstanceGUID { get; set; }

        public string PlanDatasourceGUID { get; set; }

        public string PlanInstanceGUID { get; set; }

        public string Name { get; set; }

        public int Source { get; set; }

        public string SourceId { get; set; }

        public string Metadata { get; set; }

        public string RequestParams { get; set; }

        public string RequestBody { get; set; }

        public int RequestStatus { get; set; }
        
        public int AutoReadAttachment { get; set; } = 1;
    }
}
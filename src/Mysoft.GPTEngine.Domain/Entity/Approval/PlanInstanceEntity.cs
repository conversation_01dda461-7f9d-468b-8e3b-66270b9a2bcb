using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.Approval
{
    [SugarTable("gpt_planInstance")]
    public class PlanInstanceEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PlanInstanceGUID { get; set; }
        
        public string BusinessId { get; set; }
        
        public int Status { get; set; }
        
        public string ErrorMessage { get; set; }
        
        public int Result { get; set; }
        
        public string ResultSummary { get; set; }
        
        public string BusinessFormatData { get; set; }

        public Guid PlanGUID { get; set; }
    }
}
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity.Approval
{
    [SugarTable("gpt_planRuleInstance")]
    public class PlanRuleInstanceEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string PlanRuleInstanceGUID { get; set; }
        
        public int Status { get; set; }
        
        public int Result { get; set; }
        
        public string Overview { get; set; }
        
        public string Details { get; set; }
        
        public string ErrorMessage { get; set; }

        public string UserMessage { get; set; }
    }
}
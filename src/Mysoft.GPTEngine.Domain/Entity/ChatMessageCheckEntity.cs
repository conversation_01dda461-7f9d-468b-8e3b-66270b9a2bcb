using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ChatMessageCheck")]
    public class ChatMessageCheckEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatMessageCheckGUID { get; set; }
        
        /// <summary>
        /// 模型实例GUID
        /// </summary>
        public Guid ModelInstanceGUID { get; set; }
        
        /// <summary>
        /// 技能GUID
        /// </summary>
        public Guid SkillGUID { get; set; }
        
        /// <summary>
        /// 会话ID
        /// </summary>
        public Guid ChartGUID { get; set; }
        
        /// <summary>
        /// 输入
        /// </summary>
        public string Input { get; set; }
        
        /// <summary>
        /// 输出
        /// </summary>
        public string Result { get; set; }
        
        /// <summary>
        /// 消息来源：输入，输出
        /// </summary>
        public CheckMessageSourceEnum MessageSource { get; set; }
        
        /// <summary>
        /// 检测来源：百度合规审查等
        /// </summary>
        public CheckSourceEnum CheckSource { get; set; }
        
        /// <summary>
        /// 审查不合规原因
        /// </summary>
        public string CheckResult { get; set; }
        
        
    }
}

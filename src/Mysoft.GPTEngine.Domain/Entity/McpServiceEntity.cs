using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_mcpservice")]
    public class McpServiceEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public string ServiceGUID { get; set; }

        [SugarColumn(ColumnName = "VersionNumber")]
        public DateTime VersionNumber { get; set; }

        public string ServiceName { get; set; }

        public string ServiceDescription { get; set; }

        public string ServiceURL { get; set; }

        public string ServiceTransport { get; set; }

        public string CustomHeaders { get; set; }

        public int TimeoutSeconds { get; set; } = 30;

        public int Status { get; set; } = 1;

        public string SpaceGUID { get; set; }

        public string ServiceCode { get; set; }

        public string ServiceIcon { get; set; }
    }
}

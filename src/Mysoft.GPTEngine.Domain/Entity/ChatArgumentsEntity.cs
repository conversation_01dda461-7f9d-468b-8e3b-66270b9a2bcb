using System;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ChatArguments")]
    public class ChatArgumentsEntity : BaseEntity
    {
        [SugarColumn(ColumnName = "Value")]
        public string _value { get; set; }

        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatArgumentsGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public String Key { get; set; } = string.Empty;
        [SugarColumn(IsIgnore = true)]
        public String Value
        {
            get
            {
                return _value.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._value = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }

        public String CustomerId { get; set; } = string.Empty;
        public String CustomerName { get; set; } = string.Empty;
        public String UserGUID { get; set; } = string.Empty;
        public String UserName { get; set; } = string.Empty;
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_PromptParam")]
    public class PromptParamEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ParamGUID { get; set; }
        public Guid PromptGUID { get; set; }
        public ParamTypeEnum ParamType { get; set; }
        public string ParamCode { get; set; }
        public string ParamName { get; set; }
        public string FiledType { get; set; }
        public string DefaultValue { get; set; }
        public bool IsRequired { get; set; }
        public string Describe { get; set; }
        public int Sort { get; set; }
        public bool IsSystemParam { get; set; }
    }
}

using System;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_Prompt")]
    public class PromptEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid PromptGUID { get; set; }
        public String ModelInstanceCode { get; set; }
        public String ExecutionSetting { get; set; }
        public string OutputType { get; set; }
        public int Mode { get; set; }

    }
}

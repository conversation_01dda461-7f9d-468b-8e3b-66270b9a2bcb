using System;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Entity
{
    [SugarTable("gpt_ChatMessage")]
    public class ChatMessageEntity : BaseEntity
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid ChatMessageGUID { get; set; }
        public Guid ChatGUID { get; set; }
        public Guid? NodeGUID { get; set; }
        [SugarColumn(ColumnName = "Content")]
        public string _content { get; set; } = string.Empty;
        [SugarColumn(IsIgnore = true)]
        public string Content
        {
            get
            {
                return _content.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._content = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }

        public string Role { get; set; }
        public int Index { get; set; }
        public Guid BatchGUID { get; set; }
        public String CustomerId { get; set; } = string.Empty;
        public String CustomerName { get; set; } = string.Empty;
        public String UserGUID { get; set; } = string.Empty;
        public String UserName { get; set; } = string.Empty;
        
        public String TenantCode { get; set; } = string.Empty;
        
        public String TenantName { get; set; } = string.Empty;

        public String Error { get; set; } = string.Empty;
        
        public int IsHidden { get; set; }

        [SugarColumn(ColumnName = "Answer")]
        public string _answer { get; set; } = string.Empty;
        [SugarColumn(IsIgnore = true)]
        public String Answer
        {
            get
            {
                return _answer.Replace(ApaasInfoConst.ApaasUrlPlaceHolder, ApaasInfoConst.ApaasUrlValue);
            }
            set
            {
                this._answer = value == null ? "" : value.Replace(ApaasInfoConst.ApaasUrlValue, ApaasInfoConst.ApaasUrlPlaceHolder);
            }
        }


        [SugarColumn(IsNullable = true)]
        public string PlanGUID { get; set; }
        public void SetAnswer(string answer)
        {
            this.Answer = answer?.Length > 1000 ? answer[..1000] : answer;
        }
    }
}

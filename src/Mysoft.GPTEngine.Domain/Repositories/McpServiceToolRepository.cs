using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    /**
     * 这是一个针对McpServiceToolEntity实体的数据库操作类，继承自SqlSugarRepository<McpServiceToolEntity>。
     * 它通过SqlSugarClient来与数据库进行交互。
     */
    public class McpServiceToolRepository : SqlSugarRepository<McpServiceToolEntity>
    {
        /**
         * 构造函数：初始化McpServiceToolRepository实例。
         *
         * @param sqlSugarProviderFactory SqlSugarProviderFactory实例，用于数据库操作。
         * @param mysoftContextFactory MysoftContextFactory实例，用于获取上下文。
         */
        public McpServiceToolRepository(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}

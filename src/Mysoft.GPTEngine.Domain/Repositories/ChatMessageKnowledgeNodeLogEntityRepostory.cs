using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    /**
     * 这是一个针对ChatMessageNodeLogEntity实体的数据库操作类，继承自SqlSugarRepository<ChatMessageNodeLogEntity>。
     * 它通过SqlSugarClient来与数据库进行交互。
     */
    public class ChatMessageKnowledgeNodeLogEntityRepostory : SqlSugarRepository<ChatMessageKnowledgeNodeLogEntity>
    {
        public ChatMessageKnowledgeNodeLogEntityRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}

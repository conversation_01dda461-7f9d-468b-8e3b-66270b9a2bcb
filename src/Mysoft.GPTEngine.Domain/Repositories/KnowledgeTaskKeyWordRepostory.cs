using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    public class KnowledgeTaskKeyWordRepostory : SqlSugarRepository<KnowledgeEvaluatingKeyWordEntity>
    {
        public KnowledgeTaskKeyWordRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {

        }
    }
}

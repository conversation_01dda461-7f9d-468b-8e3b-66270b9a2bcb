using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Newtonsoft.Json;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    public class SqlSugarProviderFactory : ISqlSugarProviderFactory
    {
        private readonly HttpClient _httpClient;
        private readonly SqlSugarClient _sqlSugarClient;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly IMysoftApiService _mysoftApiService;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        private readonly IConfigurationService _configurationService;

        public SqlSugarProviderFactory(SqlSugarClient sqlSugarClient, IMysoftContextFactory mysoftContextFactory, IConfigurationService configurationService,
          IMysoftApiService mysoftApiService, IHttpClientFactory clientFactory, MysoftMemoryCache mysoftMemoryCache)
        {
            _httpClient = clientFactory.CreateClient("Mysoft_TenantService");
            _sqlSugarClient = sqlSugarClient;
            _mysoftContextFactory = mysoftContextFactory;
            _mysoftApiService = mysoftApiService;
            _mysoftMemoryCache = mysoftMemoryCache;
            _configurationService = configurationService;
        }

        private static SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        public async Task<SqlSugarProvider> GetSqlSugarProvider()
        {
            var mysoftContext = _mysoftContextFactory.GetMysoftContext();
            var tenantCode = mysoftContext.TenantCode;
            
            if (_sqlSugarClient.IsAnyConnection(tenantCode) == false)
            {
                await _semaphore.WaitAsync(); // 获取许可证
                try
                {
                    if (_sqlSugarClient.IsAnyConnection(tenantCode) == false)
                    {
                        var tenantResultDto = await GetTenantResultDto(mysoftContext);

                        await AddMySqlConnection(tenantCode, tenantResultDto);
                    }
                    return await Task.FromResult(_sqlSugarClient.GetConnection(tenantCode));
                }
                finally
                {
                    _semaphore.Release(); // 释放许可证
                }
            }
            return await Task.FromResult(_sqlSugarClient.GetConnection(tenantCode));
        }
        
        private async Task<TenantResultDto> GetTenantResultDto(MysoftContext mysoftContext)
        {

            var tenantCode = mysoftContext.TenantCode;
            var res = _configurationService.GetDbInstanceItem(tenantCode);
            TenantResultDto tenantResultDto = new TenantResultDto()
            {
                Account = res.Username,
                Password = res.Password,
                Host = res.Host,
                Port = res.Port,
                DbName = res.Name,
                DbType = res.Type
            };

            //var apiName = "/api/********/assistant/dbInfo";
            //var result = await _mysoftApiService.PostAsync(mysoftContext.GptBuilderUrl + apiName, string.Empty);

            //Console.WriteLine("dbInfo: " + result);
            //var tenantResult = JsonConvert.DeserializeObject<MysoftApiResultDto>(result);
            //result = AesHelper.Decrypt(tenantResult.Data);

            //Console.WriteLine("租户列表：" + result);
            //Console.WriteLine("租户：" + tenantCode);

            //tenantResultDto = JsonConvert.DeserializeObject<TenantResultDto>(result);

            //_mysoftMemoryCache.SetDbCache(tenantResultDto);

            return await Task.FromResult(tenantResultDto);
        }

        private async Task AddMySqlConnection(string tenantCode, TenantResultDto tenantResultDb)
        {
            DbType dbTypeEnum = DbType.MySql;
            string connectionString = $"host={tenantResultDb.Host};port={tenantResultDb.Port};uid={tenantResultDb.Account};password={tenantResultDb.Password};database={tenantResultDb.DbName};";
            if (string.Equals("dm", tenantResultDb.DbType, StringComparison.OrdinalIgnoreCase))
            {
                dbTypeEnum = DbType.Dm;
                connectionString = $"Server={tenantResultDb.Host}; User Id={tenantResultDb.Account}; PWD={tenantResultDb.Password};SCHEMA={tenantResultDb.DbName};DATABASE={tenantResultDb.DbName};";
            }
            _sqlSugarClient.AddConnection(new ConnectionConfig()
            {
                DbType = dbTypeEnum,
                ConfigId = tenantCode,
                IsAutoCloseConnection = true,
                ConnectionString = connectionString
            });
            await Task.CompletedTask;
        }
        private async Task<T> SendRequestAsync<T>(string uri, HttpMethod method, HttpContent requestContent, CancellationToken cancellationToken = default)
        {
            using var request = new HttpRequestMessage(method, uri) { Content = requestContent };
            using var response = await _httpClient.SendWithSuccessCheckAsync(request, cancellationToken).ConfigureAwait(false);
            var result = await response.Content.ReadAsStringWithExceptionMappingAsync().ConfigureAwait(false);
            return await Task.FromResult(JsonConvert.DeserializeObject<T>(result));
        }
    }

    public class MysoftApiResultDto
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public bool Success { get; set; }
        public string Data { get; set; }
    }
    public class TenantResultDto
    {
        public string AppCodes { get; set; }
        public string ProductCode { get; set; }
        public string RdsId { get; set; }
        public string Account { get; set; }
        public string Password { get; set; }
        public string Host { get; set; }
        public string Port { get; set; }
        public string DbName { get; set; }
        public string DbType { get; set; }
    }
}

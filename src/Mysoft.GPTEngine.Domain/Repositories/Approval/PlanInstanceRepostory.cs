using Mysoft.GPTEngine.Domain.Entity.Approval;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories.Approval
{
    public class PlanInstanceRepostory : SqlSugarRepository<PlanInstanceEntity>
    {
        /**
         * 构造函数：初始化ModelRepostory实例。
         *
         * @param sqlSugarClient SqlSugarClient实例，用于数据库操作。
         */
        public PlanInstanceRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory,IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory,mysoftContextFactory)
        {
        }
    }
}
using Mysoft.GPTEngine.Domain.Entity.Approval;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories.Approval
{
    public class PlanRepostory : SqlSugarRepository<PlanEntity>
    {
        public PlanRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}

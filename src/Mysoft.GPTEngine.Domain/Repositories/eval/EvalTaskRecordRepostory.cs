using Mysoft.GPTEngine.Domain.Entity.eval;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories.eval
{
    public class EvalTaskRecordRepostory : SqlSugarRepository<EvalTaskRecordEntity>
    {
        public EvalTaskRecordRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory,IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory,mysoftContextFactory)
        {
        }
    }
}
using Mysoft.GPTEngine.Domain.Entity.eval;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories.eval
{
    public class PlanEvalResultRepostory : SqlSugarRepository<PlanEvalResultEntity>
    {
        public PlanEvalResultRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory,IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory,mysoftContextFactory)
        {
        }
    }
}
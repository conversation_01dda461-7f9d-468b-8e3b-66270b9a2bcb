using Mysoft.GPTEngine.Domain.Entity.eval;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories.eval
{
    public class EvalTaskRepostory : SqlSugarRepository<EvalTaskEntity>
    {
        public EvalTaskRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory,IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory,mysoftContextFactory)
        {
        }
    }
}
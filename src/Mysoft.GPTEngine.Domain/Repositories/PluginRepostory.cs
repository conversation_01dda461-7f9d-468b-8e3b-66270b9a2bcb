using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    /**
     * 这是一个针对PluginEntity实体的数据库操作类，继承自SqlSugarRepository<PluginEntity>。
     * 它通过SqlSugarClient来与数据库进行交互。
     */
    public class PluginRepostory : SqlSugarRepository<PluginEntity>
    {
        /**
         * 构造函数：PluginRepostory。
         * 
         * @param sqlSugarClient SqlSugarClient实例，用于数据库操作。
         */
        public PluginRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory,IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory,mysoftContextFactory)
        {
        }
    }
}

using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    public class KnowledgeTaskDetailRepostory : SqlSugarRepository<KnowledgeEvaluatingTaskDetailEntity>
    {
        public KnowledgeTaskDetailRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {

        }
    }
}

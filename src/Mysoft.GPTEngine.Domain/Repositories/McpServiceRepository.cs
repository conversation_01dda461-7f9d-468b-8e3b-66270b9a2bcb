using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    /**
     * 这是一个针对McpServiceEntity实体的数据库操作类，继承自SqlSugarRepository<McpServiceEntity>。
     * 它通过SqlSugarClient来与数据库进行交互。
     */
    public class McpServiceRepository : SqlSugarRepository<McpServiceEntity>
    {
        /**
         * 构造函数：初始化McpServiceRepository实例。
         *
         * @param sqlSugarProviderFactory SqlSugarProviderFactory实例，用于数据库操作。
         * @param mysoftContextFactory MysoftContextFactory实例，用于获取上下文。
         */
        public McpServiceRepository(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}

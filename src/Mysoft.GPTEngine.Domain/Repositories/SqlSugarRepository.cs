using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Entity;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using SqlSugar;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    public class SqlSugarRepository<TEntity> : IRepository<TEntity> where TEntity : class, IBaseEntity, new()
    {
        private readonly ISqlSugarProviderFactory _sqlSugarProviderFactory;
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public SqlSugarRepository(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory)
        {
            _sqlSugarProviderFactory = sqlSugarProviderFactory;
            _mysoftContextFactory = mysoftContextFactory;
        }

        /// <summary>
        /// 获取简单Db
        /// </summary>
        /// <returns></returns>
        public virtual async Task<SimpleClient<TEntity>> GetDbSimpleClient()
        {
            var _sqlSugarProvider = (await _sqlSugarProviderFactory.GetSqlSugarProvider()).CopyNew();
            return await Task.FromResult(new SimpleClient<TEntity>(_sqlSugarProvider));
        }

        #region Abp模块

        public virtual async Task<TEntity> FindAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = true, CancellationToken cancellationToken = default)
        {
            return await GetFirstAsync(predicate);
        }

        public virtual async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = true, CancellationToken cancellationToken = default)
        {
            return await GetFirstAsync(predicate);
        }

        public virtual async Task DeleteAsync(Expression<Func<TEntity, bool>> predicate, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            await this.DeleteAsync(predicate);
        }

        public virtual async Task DeleteDirectAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
        {
            await this.DeleteAsync(predicate);
        }

        public IQueryable<TEntity> WithDetails()
        {
            throw new NotImplementedException();
        }

        public IQueryable<TEntity> WithDetails(params Expression<Func<TEntity, object>>[] propertySelectors)
        {
            throw new NotImplementedException();
        }

        public Task<IQueryable<TEntity>> WithDetailsAsync()
        {
            throw new NotImplementedException();
        }

        public Task<IQueryable<TEntity>> WithDetailsAsync(params Expression<Func<TEntity, object>>[] propertySelectors)
        {
            throw new NotImplementedException();
        }

        public async Task<IQueryable<TEntity>> GetQueryableAsync()
        {
            throw new NotImplementedException();
        }

        public virtual async Task<List<TEntity>> GetListAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            return await GetListAsync(predicate);
        }

        public virtual async Task<TEntity> InsertAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            return await InsertReturnEntityAsync(entity);
        }

        public virtual async Task InsertManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            await InsertRangeAsync(entities.ToList());
        }

        public virtual async Task<TEntity> UpdateAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            await UpdateAsync(entity);
            return entity;
        }

        public virtual async Task UpdateManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            await UpdateRangeAsync(entities.ToList());
        }

        public virtual async Task DeleteAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            await DeleteAsync(entity);
        }

        public virtual async Task DeleteManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default)
        {
            await DeleteAsync(entities.ToList());
        }

        public virtual async Task<List<TEntity>> GetListAsync(bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            return await GetListAsync();
        }

        public virtual async Task<long> GetCountAsync(CancellationToken cancellationToken = default)
        {
            return await this.CountAsync(_ => true);
        }

        public virtual async Task<List<TEntity>> GetPagedListAsync(int skipCount, int maxResultCount, string sorting, bool includeDetails = false, CancellationToken cancellationToken = default)
        {
            return await GetPageListAsync(_ => true, skipCount, maxResultCount);
        }
        #endregion


        #region SimpleClient模块
        public virtual async Task<int> CountAsync(Expression<Func<TEntity, bool>> whereExpression)
        {
            return (await GetDbSimpleClient()).Count(whereExpression);
        }

        public virtual async Task<bool> DeleteAsync(TEntity deleteObj)
        {
            return (await GetDbSimpleClient()).Delete(deleteObj);
        }

        public virtual async Task<bool> DeleteAsync(List<TEntity> deleteObjs)
        {
            return (await GetDbSimpleClient()).Delete(deleteObjs);
        }

        public virtual async Task<bool> DeleteAsync(Expression<Func<TEntity, bool>> whereExpression)
        {
            return (await GetDbSimpleClient()).Delete(whereExpression);
        }
        public virtual async Task<TEntity> GetFirstAsync(Expression<Func<TEntity, bool>> whereExpression)
        {
            return (await GetDbSimpleClient()).GetFirst(whereExpression);
        }

        public virtual async Task<List<TEntity>> GetListAsync()
        {
            return (await GetDbSimpleClient()).GetList();
        }

        public virtual async Task<List<TEntity>> GetListAsync(Expression<Func<TEntity, bool>> whereExpression)
        {
            return (await GetDbSimpleClient()).GetList(whereExpression);
        }

        public virtual async Task<List<TEntity>> GetPageListAsync(Expression<Func<TEntity, bool>> whereExpression, int pageNum, int pageSize)
        {
            return (await GetDbSimpleClient()).GetPageList(whereExpression, new PageModel() { PageIndex = pageNum, PageSize = pageSize });
        }

        public virtual async Task<List<TEntity>> GetPageListAsync(Expression<Func<TEntity, bool>> whereExpression, int pageNum, int pageSize, Expression<Func<TEntity, object>>? orderByExpression = null, OrderByType orderByType = OrderByType.Asc)
        {
            return (await GetDbSimpleClient()).GetPageList(whereExpression, new PageModel { PageIndex = pageNum, PageSize = pageSize }, orderByExpression, orderByType);
        }

        public virtual async Task<TEntity> GetSingleAsync(Expression<Func<TEntity, bool>> whereExpression)
        {
            return (await GetDbSimpleClient()).GetSingle(whereExpression);
        }

        public virtual async Task<bool> InsertAsync(TEntity insertObj)
        {
            InitAuditField(insertObj, null);
            return (await GetDbSimpleClient()).Insert(insertObj);
        }

        public virtual async Task<bool> InsertOrUpdateAsync(TEntity data)
        {
            InitAuditField(data, null);
            return (await GetDbSimpleClient()).InsertOrUpdate(data);
        }

        public virtual async Task<bool> InsertOrUpdateAsync(List<TEntity> datas)
        {
            InitAuditField(datas);
            return (await GetDbSimpleClient()).InsertOrUpdate(datas);
        }

        public virtual async Task<bool> InsertRangeAsync(List<TEntity> insertObjs)
        {
            InitAuditField(insertObjs);
            return (await GetDbSimpleClient()).InsertRange(insertObjs);
        }

        public virtual async Task<long> InsertReturnBigIdentityAsync(TEntity insertObj)
        {
            InitAuditField(insertObj, null);
            return (await GetDbSimpleClient()).InsertReturnBigIdentity(insertObj);
        }

        public virtual async Task<TEntity> InsertReturnEntityAsync(TEntity insertObj)
        {
            InitAuditField(insertObj, null);
            return (await GetDbSimpleClient()).InsertReturnEntity(insertObj);
        }

        public virtual async Task<int> InsertReturnIdentityAsync(TEntity insertObj)
        {
            InitAuditField(insertObj, null);
            return (await GetDbSimpleClient()).InsertReturnIdentity(insertObj);
        }

        public virtual async Task<long> InsertReturnSnowflakeIdAsync(TEntity insertObj)
        {
            InitAuditField(insertObj, null);
            return (await GetDbSimpleClient()).InsertReturnSnowflakeId(insertObj);
        }

        public virtual async Task<bool> IsAnyAsync(Expression<Func<TEntity, bool>> whereExpression)
        {
            return (await GetDbSimpleClient()).IsAny(whereExpression);
        }

        public virtual async Task<bool> UpdateAsync(TEntity updateObj)
        {
            InitModifiedField(updateObj, null);
            return (await GetDbSimpleClient()).Update(updateObj);
        }

        public virtual async Task<bool> UpdateAsyncWithoutModifiedField(TEntity updateObj)
        {
            return (await GetDbSimpleClient()).Update(updateObj);
        }

        public virtual async Task<bool> UpdateAsync(Expression<Func<TEntity, TEntity>> columns, Expression<Func<TEntity, bool>> whereExpression)
        {
            return (await GetDbSimpleClient()).Update(columns, whereExpression);
        }

        public virtual async Task<bool> UpdateRangeAsync(List<TEntity> updateObjs)
        {
            InitModifiedField(updateObjs);
            return (await GetDbSimpleClient()).UpdateRange(updateObjs);
        }

        #endregion
        public void InitAuditField(List<TEntity> entities)
        {
            var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;
            foreach (var entity in entities)
            {
                InitAuditField(entity, userContext);
            }
        }
        public void InitModifiedField(List<TEntity> entities)
        {
            var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;
            foreach (var entity in entities)
            {
                InitModifiedField(entity, userContext);
            }
        }
        public void InitAuditField(TEntity entity, UserContext? userContext)
        {
            userContext ??= _mysoftContextFactory.GetMysoftContext()?.UserContext;

            entity.CreatedGUID = userContext.UserId;
            entity.CreatedName = userContext.UserName;
            entity.CreatedTime = TimeZoneUtility.LocalNow();
            entity.ModifiedGUID = userContext.UserId;
            entity.ModifiedName = userContext.UserName;
            entity.ModifiedTime = TimeZoneUtility.LocalNow();

        }
        public void InitModifiedField(TEntity entity, UserContext? userContext)
        {
            userContext ??= _mysoftContextFactory.GetMysoftContext()?.UserContext;

            entity.ModifiedGUID = userContext.UserId;
            entity.ModifiedName = userContext.UserName;
            entity.ModifiedTime = TimeZoneUtility.LocalNow();

        }
    }
}
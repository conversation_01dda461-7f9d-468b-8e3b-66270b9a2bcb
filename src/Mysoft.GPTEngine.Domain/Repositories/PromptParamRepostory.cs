using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    /**
     * 这是一个针对ModelEntity实体的数据库操作类，继承自SqlSugarRepository<ModelEntity>。
     * 它通过SqlSugarClient来与数据库进行交互。
     */
    public class PromptParamRepostory : SqlSugarRepository<PromptParamEntity>
    {
        /**
         * 构造函数：初始化ModelRepostory实例。
         * 
         * @param sqlSugarClient SqlSugarClient实例，用于数据库操作。
         */
        public PromptParamRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}

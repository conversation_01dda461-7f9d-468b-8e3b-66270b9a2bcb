using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories
{
    public class KnowledgeHyperLinkRepostory : SqlSugarRepository<KnowledgeHyperLinkEntity>
    {
        public KnowledgeHyperLinkRepostory(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {

        }
    }
}

using Mysoft.GPTEngine.Domain.Entity.KnowledgeQuestion;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.Repositories.KnowledgeQuestion
{
    public class QuestionRepository : SqlSugarRepository<QuestionEntity>
    {
        /**
         * 构造函数：初始化ModelRepostory实例。
         * 
         * @param sqlSugarClient SqlSugarClient实例，用于数据库操作。
         */
        public QuestionRepository(ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory) : base(sqlSugarProviderFactory, mysoftContextFactory)
        {
        }
    }
}

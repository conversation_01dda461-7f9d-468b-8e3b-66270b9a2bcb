using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain
{
    public class MysoftCustomerServiceApiService : ApiDomainService, IMysoftCustomerServiceApiService
    {
        private HttpClient _httpClient;
        private IHttpContextAccessor _httpContextAccessor;
        private string baseUrl = "https://kfagent-test.fdcyun.com";
        private string getTokenUrl = "/v20/app/agent/validate/getToken.svc";
        public MysoftCustomerServiceApiService(IHttpClientFactory clientFactory,IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor):base(configurationService)
        {
            _httpContextAccessor = httpContextAccessor;
            _httpClient = clientFactory.CreateClient("Mysoft_CustomerService");
        }
        public async Task<string> AuthCallBack(HttpHeaders heades, string uri, CancellationToken cancellationToken = default)
        {
            var tokeContent = new StringContent("{\"appKey\":\"tianjiGPT\",\"appSecret\":\"s7Jw8h2v5x1b4r9q3u6m5z8o1i2p4a7\"}", Encoding.UTF8, "application/json");
            var result = await SendRequestAsync(_httpClient, baseUrl + getTokenUrl, HttpMethod.Post, tokeContent, cancellationToken);
            var tokenResult = JsonConvert.DeserializeObject<MysoftCustomerServiceResultDto<TokenResultDataDto>>(result);
            if (tokenResult.Success == false)
            {
                throw new BusinessException(tokenResult.ErrorMessage);
            }
            return await Task.FromResult($"{baseUrl}{uri}?access_token={tokenResult.Data.Token}");
        }
        public async Task<string> PostAsync(string uri, string body, CancellationToken cancellationToken = default)
        {
            var httpContent = new StringContent(body, Encoding.UTF8, "application/json");

            uri = await AuthCallBack(httpContent.Headers, uri, cancellationToken);

            var result = await this.SendRequestAsync(_httpClient, uri, HttpMethod.Post, httpContent, cancellationToken);
            var askResult = JsonConvert.DeserializeObject<MysoftCustomerServiceResultDto<AskResultDataDto>>(result);
            if (askResult.Success == false)
            {
                throw new BusinessException(askResult.ErrorMessage);
            }
            return await Task.FromResult(askResult.Data.Content);
        }

        public async Task<string> GetAsync(string uri, Dictionary<string,  object> requestParams, CancellationToken cancellationToken = default)
        {
            return await Task.FromResult("");
        }

    }

    public class MysoftCustomerServiceResultDto<T>
    {
        public bool Success { get; set; }
        public string ErrorType { get; set; }
        public string ErrorMessage { get; set; }
        public T Data { get; set; }
    }
    public class TokenResultDataDto
    {
        public string Token { get; set; }
    }

    public class AskResultDataDto
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public double Score { get; set; }
    }
}

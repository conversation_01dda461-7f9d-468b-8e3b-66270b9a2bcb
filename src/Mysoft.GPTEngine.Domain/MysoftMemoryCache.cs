using System;
using System.Collections.Generic;
using Microsoft.Extensions.Caching.Memory;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;

namespace Mysoft.GPTEngine.Domain
{
    public class MysoftMemoryCache : MysoftMemoryCacheBase
    {

        private static readonly string DbCacheKeyPrefix = ":db";
        
        private static readonly string MyParamValueContentRevPrefix = ":myParamValue:";
        
        private static readonly string ModelInstanceCacheKeyPrefix = ":modelInstance";

        private static readonly string SensitiveWordsCacheKeyPrefix = ":sensitiveWords";

        private static readonly string CustomerKeyPrefix = ":customer";
        public MysoftMemoryCache(IMemoryCache memoryCache, IMysoftContextFactory mysoftContextFactory) 
            : base(memoryCache, mysoftContextFactory)
        {   
        }

        public void SetDbCache(TenantResultDto value)
        {
            ExpiredCacheUtil<TenantResultDto>.Instance.Set(GetFullKey(DbCacheKeyPrefix), value, TimeSpan.FromDays(1));
        }

        public TenantResultDto GetDbCache()
        {
            return ExpiredCacheUtil<TenantResultDto>.Instance.Get(GetFullKey(DbCacheKeyPrefix));
        }
        
        public void SetMyParamValuePrefixCache(String key, Dictionary<string, string> value)
        {
            ExpiredCacheUtil<Dictionary<string, string>>.Instance.Set(GetFullKey(MyParamValueContentRevPrefix + key), value, TimeSpan.FromMinutes(10));
        }

        public Dictionary<string, string> GetMyParamValuePrefixCache(String key)
        {
            return ExpiredCacheUtil<Dictionary<string, string>>.Instance.Get(GetFullKey(MyParamValueContentRevPrefix + key));
        }

        public void SetCustomerCache<T>(String key, T value, int minutes) where T : class
        {
            ExpiredCacheUtil<T>.Instance.Set(GetFullKey(CustomerKeyPrefix + key), value, TimeSpan.FromMinutes(minutes));
        }

        public T GetCustomerCache<T>(String key) where T : class
        {
            return ExpiredCacheUtil<T>.Instance.Get(GetFullKey(CustomerKeyPrefix + key));
        }


        public void SetModelInstanceCache(List<ModelInstanceDto> value)
        {
            ExpiredCacheUtil<List<ModelInstanceDto>>.Instance.Set(GetFullKey(ModelInstanceCacheKeyPrefix), value, TimeSpan.FromDays(1));
        }

        public List<ModelInstanceDto> GetModelInstanceCache()
        {
            return ExpiredCacheUtil<List<ModelInstanceDto>>.Instance.Get(GetFullKey(ModelInstanceCacheKeyPrefix));
        }

        public void SetSensitiveWordsCache(List<SensitiveWordsEntity> value, string spaceGUID)
        {
            ExpiredCacheUtil<List<SensitiveWordsEntity>>.Instance.Set(GetFullKey(SensitiveWordsCacheKeyPrefix + spaceGUID), value, TimeSpan.FromMinutes(60));
        }

        public List<SensitiveWordsEntity> GetSensitiveWordsCache(string spaceGUID)
        {
            return ExpiredCacheUtil<List<SensitiveWordsEntity>>.Instance.Get(GetFullKey(SensitiveWordsCacheKeyPrefix + spaceGUID));
        }

        // 会话记忆缓存相关方法
        private static readonly string ConversationMemoryCacheKeyPrefix = ":conversationMemory:";

        /// <summary>
        /// 设置用户会话记忆缓存（基于用户ID和会话ID）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <param name="messages">会话消息列表</param>
        /// <param name="expirationMinutes">过期时间（分钟）</param>
        public void SetConversationMemoryCache(string userId, string chatGuid, List<ChatMessageDto> messages, int expirationMinutes)
        {
            var cacheKey = GetConversationMemoryCacheKey(userId, chatGuid);
            ExpiredCacheUtil<List<ChatMessageDto>>.Instance.Set(
                GetFullKey(cacheKey),
                messages,
                TimeSpan.FromMinutes(expirationMinutes));
        }

        /// <summary>
        /// 获取用户会话记忆缓存（基于用户ID和会话ID）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <returns>会话消息列表</returns>
        public List<ChatMessageDto> GetConversationMemoryCache(string userId, string chatGuid)
        {
            var cacheKey = GetConversationMemoryCacheKey(userId, chatGuid);
            return ExpiredCacheUtil<List<ChatMessageDto>>.Instance.Get(
                GetFullKey(cacheKey));
        }

        /// <summary>
        /// 清除用户会话记忆缓存（基于用户ID和会话ID）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        public void ClearConversationMemoryCache(string userId, string chatGuid)
        {
            var cacheKey = GetConversationMemoryCacheKey(userId, chatGuid);
            ExpiredCacheUtil<List<ChatMessageDto>>.Instance.Remove(
                GetFullKey(cacheKey));
        }

        /// <summary>
        /// 清除用户所有会话记忆缓存（基于用户ID，兼容旧版本）
        /// </summary>
        /// <param name="userId">用户ID</param>
        [Obsolete("建议使用基于ChatGuid的清除方法")]
        public void ClearConversationMemoryCache(string userId)
        {
            // 为了向后兼容，保留此方法，但建议使用新的基于ChatGuid的方法
            ExpiredCacheUtil<List<ChatMessageDto>>.Instance.Remove(
                GetFullKey(ConversationMemoryCacheKeyPrefix + userId));
        }

        /// <summary>
        /// 构造会话记忆缓存键
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <returns>缓存键</returns>
        private string GetConversationMemoryCacheKey(string userId, string chatGuid)
        {
            return ConversationMemoryCacheKeyPrefix + userId + ":" + chatGuid;
        }

    }
}
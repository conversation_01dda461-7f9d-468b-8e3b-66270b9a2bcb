using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Markdig;
using Markdig.Extensions.Tables;
using Markdig.Syntax;
using Markdig.Syntax.Inlines;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode.Util
{
    public class MarkdownReader
    {
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly IMysoftContextFactory _mysoftContextFactory;

        private List<BatchDocumentUploadParam> _batchDocumentUploadParams = new List<BatchDocumentUploadParam>();
        
        List<MarkdownParagraph> _list = new List<MarkdownParagraph>();

        private List<ImageUploadCallBackDto> _imageUploadCallBackDtos = new List<ImageUploadCallBackDto>();

        private int _imageDocumentCount = 0;
        
        public MarkdownReader(MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory)
        {
            _mysoftApiDomainService = mysoftApiDomainService;
            _mysoftContextFactory = mysoftContextFactory;
        }

        public static MarkdownReader Builder(MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory)
        {
            return new MarkdownReader(mysoftApiDomainService, mysoftContextFactory);
        }
        
        public async Task<List<MarkdownParagraph>> ConvertToParagraph(string markdownContent, LevelConfig levelConfig)
        {
            // 使用Markdig解析Markdown
            var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            var document = Markdown.Parse(markdownContent, pipeline);

            var pathList = new string[10];
            var contentBuilder = new StringBuilder();
            
            // 提取所有标题
            foreach (var block in document)
            {
                if (block is HeadingBlock headingBlock)
                {
                    var level = headingBlock.Level;
                    if (level <= levelConfig.MaxLevel)
                    {
                        if(_list.Count > 0)
                        {
                            var index = _list.Count - 1;
                            _list[index].Content = contentBuilder.ToString();
                            contentBuilder = new StringBuilder();
                        }
                        
                        var markdownParagraph = new MarkdownParagraph();
                        var title = GetTitle(headingBlock);
                        if (levelConfig.SaveMode == (int)SectionSaveLevelModeEnum.CurrentLevelName)
                        {
                            markdownParagraph.Title = title;
                        }
                        else
                        {
                            pathList[level - 1] = title;
                            markdownParagraph.Title = GetFullTitle(pathList, level);
                        }
                        _list.Add(markdownParagraph);
                    }
                    else
                    {
                        if(_list.Count == 0){
                            var markdownParagraph = new MarkdownParagraph();
                            markdownParagraph.Title = "";
                            _list.Add(markdownParagraph);
                        }

                        contentBuilder.Append(GetTitle(headingBlock)+"\n");
                    }
                }
                else
                {
                    if(_list.Count == 0){
                        var markdownParagraph = new MarkdownParagraph();
                        markdownParagraph.Title = "";
                        _list.Add(markdownParagraph);
                    }

                    contentBuilder.Append(GetAllParagraphContent(block));
                }

                await UploadImages(false);

            }

            await UploadImages(true);
            
            if(_list.Count > 0)
            {
                var lastIndex = _list.Count - 1;
                _list[lastIndex].Content = contentBuilder.ToString();
            }

            ReplaceImageWithGuid();
            return _list;
        }

        private string GetTitle(HeadingBlock headingBlock)
        {
            if (headingBlock.Lines.Count > 0) return headingBlock.Lines.ToString();
            
            if (headingBlock.Inline == null || headingBlock.Inline.FirstChild == null)
            {
                return "";
            }

            if (headingBlock.Inline.FirstChild is EmphasisInline)
            {
                return ((EmphasisInline)headingBlock.Inline.FirstChild).FirstChild?.ToString();
            }

            if (headingBlock.Inline.FirstChild is LinkInline)
            {
                Inline inline = headingBlock.Inline.FirstChild.NextSibling;
                if (inline != null)
                {
                    return inline.ToString();
                }
            }

            return headingBlock.Inline.FirstChild.ToString();
        }
        
        private string GetFullTitle(string[] pathList, int level){
            if (level == 0) {
                return "";
            }
            var sb = new StringBuilder();
            for (var i = 0; i < pathList.Length; i++) {
                if (i < level) {
                    sb.Append(pathList[i] == null ? "" : pathList[i]);
                    sb.Append(" ");
                }else{
                    pathList[i] = "";
                }
            }

            return sb.ToString();
        }

        private string GetAllParagraphContent(Block block)
        {
            switch (block)
            {
                case ContainerBlock containerBlock:
                    return ReadContainerBlockTxt(containerBlock);
                case LeafBlock leafBlock:
                    return ReadLeafBlockTxt(leafBlock);
                default:
                    return block.ToString();
            }
        }

        private string ReadLeafBlockTxt(LeafBlock leafBlock)
        {
            if (leafBlock.Lines.Count > 0) return leafBlock.Lines.ToString() + "\n";

            if (leafBlock.Inline == null) return "";
            
            var sb = new StringBuilder();
            foreach (var inline in leafBlock.Inline)
            {
                sb.Append(ReadInlineTxt(inline));
            }
            sb.Append("\n");
            return sb.ToString();
        }
        
        private string ReadContainerBlockTxt(ContainerBlock containerBlock)
        {
            if (containerBlock == null || containerBlock.Count == 0) return "";
            
            var sb = new StringBuilder();
            foreach (var block in containerBlock)
            {
                sb.Append(GetAllParagraphContent(block));
            }
            
            // if (containerBlock is ListBlock)
            // {
            //     sb.Append("\n");
            // }
            return sb.ToString();
        }

        private string ReadInlineTxt(Inline inline)
        {
            switch (inline)
            {
                case AutolinkInline autolinkInline:
                    return ReadInlineTxt(autolinkInline);
                case CodeInline codeInline:
                    return ReadInlineTxt(codeInline);
                case HtmlEntityInline htmlEntityInline:
                    return ReadInlineTxt(htmlEntityInline);
                case LiteralInline literalInline:
                    return ReadInlineTxt(literalInline);
                case LineBreakInline lineBreakInline:
                    return ReadInlineTxt(lineBreakInline);
                case EmphasisInline emphasisInline:
                    return ReadInlineTxt(emphasisInline);
                case LinkInline linkInline:
                    return ReadInlineTxt(linkInline);
                case EmphasisDelimiterInline emphasisDelimiterInline:
                    return ReadInlineTxt(emphasisDelimiterInline);
                case LinkDelimiterInline linkDelimiterInline:
                    return ReadInlineTxt(linkDelimiterInline);
                case PipeTableDelimiterInline pipeTableDelimiterInline:
                    return ReadInlineTxt(pipeTableDelimiterInline);
                default:
                    return inline.ToString();
            }
        }

        private string ReadInlineTxt(AutolinkInline autolinkInline)
        {
            //extends LeafInline
            return autolinkInline.ToString();
        }
        
        private string ReadInlineTxt(CodeInline codeInline)
        {
            //extends LeafInline
            return codeInline.Content;
        }
        
        private string ReadInlineTxt(HtmlEntityInline htmlEntityInline)
        {
            //extends LeafInline
            return htmlEntityInline.Transcoded.ToString();
        }
        
        private string ReadInlineTxt(LiteralInline literalInline)
        {
            //extends LeafInline
            return literalInline.ToString();
        }
        
        private string ReadInlineTxt(LineBreakInline lineBreakInline)
        {
            //extends LeafInline
            return "\n";
        }
        
        
        private string ReadInlineTxt(EmphasisInline emphasisInline)
        {
            //extends ContainerInline
            var str = "";
            for (var i = 0; i < emphasisInline.DelimiterCount; i++)
            {
                str += emphasisInline.DelimiterChar.ToString();
            }
            
            return str + ReadContainerInline(emphasisInline) + str;
        }
        
        private string ReadInlineTxt(LinkInline linkInline)
        {
            //extends ContainerInline
            if (linkInline.IsImage) return CheckAndUploadBase64Image(linkInline);
                
            
            var title = linkInline.LinkRefDefLabel == null ? linkInline.LinkRefDefLabel : "";
            if (linkInline.FirstChild != null)
            {
                title = linkInline.FirstChild.ToString();
            }
            return "[" + title + "](" + linkInline.Url + ")";
        }
        
        private string ReadInlineTxt(EmphasisDelimiterInline emphasisDelimiterInline)
        {
            //extends DelimiterInline
            return emphasisDelimiterInline.Content.ToString();
        }
        
        private string ReadInlineTxt(LinkDelimiterInline linkDelimiterInline)
        {
            //extends DelimiterInline
            return ReadContainerInline(linkDelimiterInline);
        }
        
        private string ReadInlineTxt(PipeTableDelimiterInline pipeTableDelimiterInline)
        {
            //extends DelimiterInline
            var literal = pipeTableDelimiterInline.ToLiteral();
            return literal + ReadContainerInline(pipeTableDelimiterInline);
            
        }

        private string ReadContainerInline(ContainerInline containerInline)
        {
            var sb = new StringBuilder();
            foreach (var inline in containerInline)
            {
                sb.Append(ReadInlineTxt(inline));
            }

            return sb.ToString();
        }

        private string CheckAndUploadBase64Image(LinkInline linkInline)
        {
            if (linkInline.Url != null && linkInline.Url.StartsWith("data:image/"))
            {
                _imageDocumentCount += 1;
                Guid documentGUID = Guid.NewGuid();
                string base64Image = ExtractBase64Data(linkInline.Url);
                // 解码 Base64 字符串为字节数组
                byte[] bytes = Convert.FromBase64String(base64Image);
                
                BatchDocumentUploadParam batchDocumentUploadParam = new BatchDocumentUploadParam();
                DocumentUploadParam documentUploadParam = new DocumentUploadParam();
                documentUploadParam.fileName = string.Format("image_{0}.png", _imageDocumentCount);
                documentUploadParam.fileSize = bytes.Length.ToString();
                documentUploadParam.fileGUID = documentGUID.ToString();

                batchDocumentUploadParam.documentContentBase64 = base64Image;
                batchDocumentUploadParam.documentUploadParam = documentUploadParam;
                
                _batchDocumentUploadParams.Add(batchDocumentUploadParam);

                return @$"{{{{image:{documentGUID}}}}}";
            }
            return "![](" + linkInline.Url + ")";
        }
        
        private async Task UploadImages(bool isLast)
        {
            if (_batchDocumentUploadParams.Count <= 0 || (_batchDocumentUploadParams.Count < 30 && !isLast))
            {
                return;
            }

            string gptBuilderUrl = _mysoftContextFactory.GetMysoftContext().GptBuilderUrl + GPTBuilderRequestPathConst.UploadDocument;
            
            string res = await _mysoftApiDomainService.PostAsync(gptBuilderUrl, JsonConvert.SerializeObject(_batchDocumentUploadParams)).ConfigureAwait(false);

            _batchDocumentUploadParams = new List<BatchDocumentUploadParam>();
            //用同步的方式拿到图片信息
            _imageUploadCallBackDtos.AddRange(JsonConvert.DeserializeObject<ReturnDto<List<ImageUploadCallBackDto>>>(res).Data);
        }

        private void ReplaceImageWithGuid()
        {
            foreach (var item in _imageUploadCallBackDtos)
            {
                //如果上传成功
                if (item.isSuccess)
                {
                    foreach (var m in _list)
                    {
                        m.Content = m.Content.Replace($"{{{{image:{item.fileGUID}}}}}", $"{{{{image:{item.documentUploadResult.documentGuid}}}}}");
                    }
                }
            }
        }
        
        
        private string ExtractBase64Data(string base64DataWithPrefix)
        {
            // 分割字符串，获取 Base64 编码的部分
            int commaIndex = base64DataWithPrefix.IndexOf(',');
            if (commaIndex > 0)
            {
                return base64DataWithPrefix.Substring(commaIndex + 1);
            }
            throw new ArgumentException("Invalid Base64 data with prefix.");
        }
        
        
        
    }
    
    
    

    public class MarkdownParagraph
    {
        public string Title { get; set; }

        public string Content { get; set; }
    }
}
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Aspose.Words;
using Aspose.Words.Loading;
using Aspose.Words.Saving;
using AutoMapper;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Application.Configuration;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Body = DocumentFormat.OpenXml.Wordprocessing.Body;
using Document = Aspose.Words.Document;
using Hyperlink = DocumentFormat.OpenXml.Wordprocessing.Hyperlink;
using Paragraph = Aspose.Words.Paragraph;
using Run = DocumentFormat.OpenXml.Drawing.Run;
using Table = Aspose.Words.Tables.Table;
using Text = DocumentFormat.OpenXml.Wordprocessing.Text;

namespace Mysoft.GPTEngine.Domain.TextExtractDecode
{
    public class MsWordDecoder : BaseDecode, IDocumentDecoder
    {
        private readonly MysoftApiService _mysoftApiDomainService;

        public MsWordDecoder(Kernel _kernel, MysoftApiService mysoftApiDomainService, IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper) 
            : base(_kernel,mysoftApiDomainService, mysoftContextFactory, httpContextAccessor, mapper)
        {
            _mysoftApiDomainService = mysoftApiDomainService;
        }

        //Word文本解析
        public async Task<FileContent> DecodeAsync(MemoryStream data, ExecutionSetting executionSetting)
        {
            var result = new FileContent("text/word");
            StringBuilder sb = new StringBuilder();
            List<HyperLinkDto> hyperLinkList = new List<HyperLinkDto>();
            List<ImageUploadCallBackDto> imageUploadCallBackDtos = new List<ImageUploadCallBackDto>();
            int imageCount = 1;
            using (data)
            {
                using (MemoryStream msDoc = new MemoryStream())
                {
                    data.CopyTo(msDoc);
                    msDoc.Position = 0;
                    using (var wordprocessingDocument = WordprocessingDocument.Open(msDoc, true))
                    {
                        MainDocumentPart? mainPart = wordprocessingDocument.MainDocumentPart;
                        if (mainPart is null)
                        {
                            throw new InvalidOperationException("The main document part is missing.");
                        }

                        Body? body = mainPart.Document.Body;
                        if (body is null)
                        {
                            throw new InvalidOperationException("The document body is missing.");
                        }
                        // 1、是否上传图片，解析图片，如果不上传，也要把图片全部解析成空文本
                        IEnumerable<Drawing>? drawings = body.Descendants<Drawing>();
                        foreach (var draw in drawings)
                        {
                            var blip = draw.Descendants<Blip>().FirstOrDefault();
                            if (blip != null)
                            {
                                var imagePart = (ImagePart)mainPart.GetPartById(blip.Embed!);
                                bool isSmallImage = false;
                                //using (var imageStream = imagePart.GetStream())
                                //{
                                //    var image = System.Drawing.Image.FromStream(imageStream);
                                //    if (image.Height < ImageHandleParam.Height || image.Width < ImageHandleParam.Width)
                                //    {
                                //        isSmallImage = true;
                                //    }
                                //}
                                // 图片尺寸不够的时候，也要放一个空文本占位符
                                if (isSmallImage || executionSetting.IsUploadImage == (int)IsEnableEnum.No)
                                {
                                    Run placeholderRun = new Run(new Text(""));
                                    draw.Parent.ReplaceChild(placeholderRun, draw);
                                }
                                else
                                {
                                    //组装数据
                                    Guid documentGUID = Guid.NewGuid();
                                    result.BatchDocumentUploadParams.Add(ImageToBatchDocumentUploadParams(imagePart, documentGUID, imageCount));

                                    // 替换图片为占位符
                                    Run placeholderRun = new Run(new Text(@$"{{{{image:{documentGUID}}}}}"));
                                    draw.Parent.ReplaceChild(placeholderRun, draw);

                                    if(result.BatchDocumentUploadParams.Count == 30)
                                    {
                                        //执行上传
                                        imageUploadCallBackDtos.AddRange(await UploadDocument(result.BatchDocumentUploadParams));

                                        result.BatchDocumentUploadParams = new List<BatchDocumentUploadParam>();
                                    }
                                    blip = null;
                                    imageCount++;
                                }
                            }
                        }
                        if (result.BatchDocumentUploadParams.Count > 0)
                        {
                            //执行上传
                            imageUploadCallBackDtos.AddRange(await UploadDocument(result.BatchDocumentUploadParams));

                            result.BatchDocumentUploadParams = new List<BatchDocumentUploadParam>();
                        }
                        var hyperlinks = mainPart.HyperlinkRelationships;
                        // 2、提取超链接
                        foreach (Hyperlink hyperlink in body.Descendants<Hyperlink>())
                        {
                            var hyperlinkRelation = hyperlinks.FirstOrDefault(f => f.Id == hyperlink.Id);
                            if (hyperlinkRelation != null)
                            {
                                string anchor = hyperlink.Anchor;
                                Uri targetUri = hyperlinkRelation.Uri;
                                string txt = hyperlink.InnerText;

                                // 替换超链接为文本占位符
                                hyperlink.RemoveAllChildren();
                                var hyperlinkGUID = Guid.NewGuid();
                                hyperlink.AppendChild(new Run(new DocumentFormat.OpenXml.Drawing.Text(@$"{{{{hyperlink:{hyperlinkGUID}}}}}")));
                                HyperLinkDto hyperLinkDto = new HyperLinkDto();
                                hyperLinkDto.HyperLinkText = txt;
                                hyperLinkDto.HyperLinkURL = targetUri.OriginalString;
                                hyperLinkDto.HyperLinkGUID = hyperlinkGUID;
                                hyperLinkList.Add(hyperLinkDto);
                            }
                        }
                    }

                    //初始化aspose
                    AsposeHelper.InitAsposeWord();
                    Document document = new Document(msDoc);
                    MarkdownSaveOptions options = new MarkdownSaveOptions();
                    options.SaveFormat = SaveFormat.Markdown;
                    options.UpdateFields = true;
                    options.ExportImagesAsBase64 = true;
                    // 3、移除目录
                    RemoveMenu(document);
                    string md = string.Empty;
                    using (MemoryStream ms = new MemoryStream())
                    {
                        document.Save(ms, options);
                        md = Encoding.UTF8.GetString(ms.ToArray());
                        // 移除base64格式的图片文件，因为有些Word带有复杂结构的格式，在转md时会被自动识别为图片格式
                        md = ImageExtractionHelper.RemoveBase64Img(md);
                    }
                    List<ParagraphsDto> paragraphs = new List<ParagraphsDto>();
                    string[] titles = new string[9];
                    int lastLevel = 1;
                    // 4、遍历文档中的段落
                    if (executionSetting.SectionConfig.Mode == (int)SectionModeEnum.Level)
                    {
                        foreach (Paragraph paragraph in document.GetChildNodes(NodeType.Paragraph, true))
                        {
                            // 检查段落样式是否为标题样式
                            if (paragraph.ParagraphFormat.StyleIdentifier >= StyleIdentifier.Heading1 &&
                                paragraph.ParagraphFormat.StyleIdentifier <= GetStyleIdentifierByLevel(executionSetting.SectionConfig.LevelConfig.MaxLevel))
                            {
                                ParagraphsDto paragraphsDto = new ParagraphsDto();

                                var currentTitle = paragraph.GetText().Replace("\r", "").Trim();
                                if (executionSetting.SectionConfig.LevelConfig.SaveMode == (int)SectionSaveLevelModeEnum.CurrentLevelName)
                                {
                                    paragraphsDto.ParagraphFullTitle = currentTitle;
                                }
                                else
                                {
                                    // 拼接全长标题
                                    paragraphsDto.ParagraphFullTitle = ConcatenateTitles(
                                        (int)paragraph.ParagraphFormat.StyleIdentifier, currentTitle, titles,
                                        lastLevel);
                                }

                                lastLevel = (int)paragraph.ParagraphFormat.StyleIdentifier;
                                // 将段落文本添加到列表中
                                paragraphsDto.ParagraphTitle =
                                    "**" + paragraph.GetText().Replace("\r", "").Trim() + "**";
                                paragraphsDto.Level = (int)paragraph.ParagraphFormat.StyleIdentifier;
                                paragraphs.Add(paragraphsDto);
                            }
                        }
                    }

                    // 5、替换Markdown里面的超链接
                    foreach (var hyperLink in hyperLinkList)
                    {
                        md = md.Replace($"[{{{{hyperlink:{hyperLink.HyperLinkGUID}}}}}]({hyperLink.HyperLinkURL})", $"{{{{hyperlink:{hyperLink.HyperLinkGUID}}}}}");
                    }
                    // 6、根据文档结构解析Markdown
                    Dictionary<string, string> titleContents = AnalysisMd(paragraphs, md);
                    int pageNumber = 1;
                    foreach (var item in titleContents)
                    {
                        if (!string.IsNullOrWhiteSpace(item.Value))
                        {
                            result.Sections.Add(new FileSection(pageNumber, item.Value, true, item.Key));
                            pageNumber++;
                        }
                    }
                    // 没有标题就放到一个片段
                    if (pageNumber == 1)
                    {
                        result.Sections.Add(new FileSection(pageNumber, md, true, ""));
                    }
                    result.HyperLinkList = hyperLinkList;
                    //替换图片占位符
                    foreach (var item in imageUploadCallBackDtos)
                    {
                        //如果上传成功
                        if (item.isSuccess)
                        {
                            foreach (var m in result.Sections)
                            {
                                m.Content = m.Content.Replace($"{{{{image:{item.fileGUID}}}}}", $"{{{{image:{item.documentUploadResult.documentGuid}}}}}");
                            }
                        }
                    }
                    return await Task.FromResult(result);
                }
            }
        }

        public string ConcatenateTitles(int level, string title, string[] titles, int lastLevel)
        {
            // 层级跳跃的情况下，把中间层级清空
            if (level < lastLevel)
            {
                for (int i = level; i < lastLevel; i++)
                {
                    titles[i - 1] = "";
                }
            }
            // 将标题内容存储在数组中的对应层级位置
            titles[level - 1] = title;
            // 拼接从给定层级到数组开头的所有标题内容
            string concatenatedTitle = string.Empty;
            for (int i = level - 1; i >= 0; i--)
            {
                if (!string.IsNullOrEmpty(titles[i]))
                {
                    concatenatedTitle = titles[i] + " " + concatenatedTitle;
                }
            }

            return concatenatedTitle.Trim();
        }

        //解析Markdown文本
        private Dictionary<string, string> AnalysisMd(List<ParagraphsDto> titles, string document)
        {
            Dictionary<string, string> titleContents = new Dictionary<string, string>();

            int startIndex = 0;

            for (int i = 0; i < titles.Count; i++)
            {
                var paragraph = titles[i];
                int titleIndex = document.IndexOf(paragraph.ParagraphTitle, startIndex);
                if (titleIndex != -1)
                {
                    int lineStartIndex = document.IndexOf('\n', titleIndex) + 1;

                    int endIndex = i < titles.Count - 1 ? document.IndexOf(titles[i + 1].ParagraphTitle, titleIndex + paragraph.ParagraphTitle.Length) : document.Length;
                    if (endIndex == -1)
                    {
                        titleContents[paragraph.ParagraphFullTitle] = "";
                        continue;
                    }

                    int lineEndIndex = document.LastIndexOf('\n', endIndex - 1);

                    if (lineEndIndex == -1 || lineEndIndex < lineStartIndex)
                    {
                        titleContents[paragraph.ParagraphFullTitle] = "";
                    }
                    else
                    {
                        string content = document.Substring(lineStartIndex, lineEndIndex - lineStartIndex).Trim();
                        titleContents[paragraph.ParagraphFullTitle] = content;
                    }

                    startIndex = endIndex;
                }
            }

            return titleContents;
        }

        /// <summary>
        /// 图片转文档服务上传对象
        /// </summary>
        /// <returns></returns>
        private BatchDocumentUploadParam ImageToBatchDocumentUploadParams(ImagePart imagePart, Guid fileGUID, int imageCount)
        {
            using (var imageStream = imagePart.GetStream())
            {
                byte[] bytes = StreamHandleHelper.StreamToByteArray(imageStream);
                BatchDocumentUploadParam batchDocumentUploadParam = new BatchDocumentUploadParam();
                DocumentUploadParam documentUploadParam = new DocumentUploadParam();
                documentUploadParam.fileName = string.Format(ImageHandleParam.ImageName, imageCount);
                documentUploadParam.fileSize = bytes.Length.ToString();
                documentUploadParam.fileGUID = fileGUID.ToString();

                //batchDocumentUploadParam.documentContent = bytes;
                batchDocumentUploadParam.documentUploadParam = documentUploadParam;
                batchDocumentUploadParam.documentContentBase64 = Convert.ToBase64String(bytes);

                bytes = null;

                return batchDocumentUploadParam;
            }
        }

        private StyleIdentifier GetStyleIdentifierByLevel(int level)
        {
            switch (level)
            {
                case 1:
                    return StyleIdentifier.Heading1;
                case 2:
                    return StyleIdentifier.Heading2;
                case 3:
                    return StyleIdentifier.Heading3;
                case 4:
                    return StyleIdentifier.Heading4;
                case 5:
                    return StyleIdentifier.Heading5;
                case 6:
                    return StyleIdentifier.Heading6;
                case 7:
                    return StyleIdentifier.Heading7;
                case 8:
                    return StyleIdentifier.Heading8;
                case 9:
                    return StyleIdentifier.Heading9;
                default:
                    return StyleIdentifier.Heading9;

            }
        }

        public bool CanDecode(string documentType)
        {
            return documentType == "word";
        }

        public async Task<DocumentReadingDto> DomentReadAsync(MemoryStream data, NodeConfig nodeConfig)
        {
            var ocrService = nodeConfig.IsEnableOCR && !string.IsNullOrWhiteSpace(nodeConfig.OcrService) ? nodeConfig.OcrService : "";
            var isEnableOCR = !string.IsNullOrWhiteSpace(ocrService);
            DocumentReadingDto documentReadingDto = new DocumentReadingDto();
            List<string> markdownList = new List<string>();
            //aspose处理Markdown
            AsposeHelper.InitAsposeWord();
            PdfLoadOptions pdfLoadOptions = new PdfLoadOptions();
            pdfLoadOptions.SkipPdfImages = !isEnableOCR;
            Document document = new Document(data, pdfLoadOptions);
            TocExcute(document, nodeConfig);
            // 判断一下是否开启了OCR
            if (isEnableOCR)
            {
                await OCRExtract(document, ocrService);
            }
            List<HyperLinkDto> hyperLinkList = new List<HyperLinkDto>();
            InitHyperLink(hyperLinkList, document);
            if (!nodeConfig.UseTOC)
            {
                RemoveMenu(document);
            }
            string res = string.Empty;
            // 处理列表
            // 遍历所有段落
            DocumentTableHandle(document);
            // 创建并使用自定义的 TextExtractor
            // 遍历文档中的所有段落
            foreach (Paragraph paragraph in document.GetChildNodes(NodeType.Paragraph, true))
            {
                // 获取当前段落的文本并添加到字符串中
                res += paragraph.GetText() + Environment.NewLine;  // 添加换行符以保持段落结构
            }
            res = JsonUtility.HyperLinkClean(res);
            res = JsonUtility.ControlCharClean(res);
            res = JsonUtility.MenuClean(res);
            documentReadingDto.Content = res;
            //移除列表和下划线的逻辑
            return await Task.FromResult(documentReadingDto);
        }

        private string AsposeTableToMd(Table table)
        {
            List<string> rows = new List<string>();

            // 首先获取所有的列数，以便在Markdown表格中正确地放置分隔线
            int columnCount = 0;
            if (table.Rows.Count > 0)  // 确保表格至少有一行
            {
                foreach (var cell in table.FirstRow.Cells)
                {
                    columnCount++;
                }
            }

            // 构建表头
            string header = "|";
            foreach (var cell in table.FirstRow.Cells)
            {
                string cellText = cell.GetText();
                // 移除所有的域代码
                cellText = Regex.Replace(cellText, @"\v[\s\S]*?\v", string.Empty);
                // 移除其他可能的控制字符
                cellText = cellText.Replace("\a", "").Replace("\f", "").Replace("\n", " ").Trim();
                header += " " + cellText.Replace("\r", "").Trim() + " |";  // 添加单元格内容
            }
            rows.Add(header);  // 添加表头

            // 表头下的分隔线
            string headerSeparator = "|";
            for (int i = 0; i < columnCount; i++)
            {
                headerSeparator += " --- |";
            }
            rows.Add(headerSeparator);  // 添加分隔线

            // 处理每一行，跳过第一行（因为已经作为表头处理）
            for (int i = 1; i < table.Rows.Count; i++)  // 从第二行开始
            {
                var row = table.Rows[i];
                string rowContent = "|";  // 开始新行
                foreach (var cell in row.Cells)
                {
                    string cellText = cell.GetText();
                    // 移除所有的域代码
                    cellText = Regex.Replace(cellText, @"\v[\s\S]*?\v", string.Empty);
                    // 移除其他可能的控制字符
                    cellText = cellText.Replace("\a", "").Replace("\f", "").Replace("\n", " ").Trim();
                    rowContent += " " + cellText.Replace("\r", "").Trim() + " |";  // 添加单元格内容
                }
                rows.Add(rowContent);  // 添加完成的行
            }

            // 构建完整的Markdown表格字符串
            string markdownTable = string.Join("\n", rows);
            return markdownTable;
        }

        public async Task<string> GetDocumentTxt(MemoryStream data,NodeConfig nodeConfig)
        {
            var ocrService = nodeConfig.IsEnableOCR && !string.IsNullOrWhiteSpace(nodeConfig.OcrService) ? nodeConfig.OcrService : "";
            var isEnableOCR = !string.IsNullOrWhiteSpace(ocrService);
            AsposeHelper.InitAsposeWord();
            PdfLoadOptions pdfLoadOptions = new PdfLoadOptions();
            pdfLoadOptions.SkipPdfImages = !isEnableOCR;
            Document document = new Document(data, pdfLoadOptions);
            TocExcute(document, nodeConfig);
            // 判断一下是否开启了OCR
            if (isEnableOCR)
            {
                await OCRExtract(document, ocrService);
            }
            List<HyperLinkDto> hyperLinkList = new List<HyperLinkDto>();
            InitHyperLink(hyperLinkList, document);
            if (!nodeConfig.UseTOC)
            {
                RemoveMenu(document);
            }
            string res = string.Empty;
            // 处理列表
            // 遍历所有段落
            DocumentTableHandle(document);
            // 创建并使用自定义的 TextExtractor
            // 遍历文档中的所有段落
            foreach (Paragraph paragraph in document.GetChildNodes(NodeType.Paragraph, true))
            {
                // 获取当前段落的文本并添加到字符串中
                res += paragraph.GetText() + Environment.NewLine;  // 添加换行符以保持段落结构
            }
            res = JsonUtility.HyperLinkClean(res);
            res = JsonUtility.ControlCharClean(res);
            res = JsonUtility.MenuClean(res);
            return await Task.FromResult(res);
        }

        private void DocumentTableHandle(Document document)
        {
            foreach (Table table in document.GetChildNodes(NodeType.Table, true))
            {
                // 转换为Markdown格式
                string markdown = AsposeTableToMd(table);
                // 创建一个新的段落
                Paragraph newPara = new Paragraph(document);
                newPara.AppendChild(new Aspose.Words.Run(document, markdown));
                // 替换原有内容
                table.ParentNode.InsertBefore(newPara, table);
                table.Remove();
            }
        }

        public Task<List<ImageUploadCallBackDto>> ConvertDocumentToImg(MemoryStream ms)
        {
            throw new NotImplementedException();
        }
    }
}

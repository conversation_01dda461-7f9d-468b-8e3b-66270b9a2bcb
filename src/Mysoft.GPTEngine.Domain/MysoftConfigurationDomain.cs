using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Domain
{
    public class MysoftConfigurationDomain
    {
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        private readonly IConfigurationService _configurationService;
        public MysoftConfigurationDomain(MysoftApiService mysoftApiService, IMysoftContextFactory mysoftContextFactory,
            IConfigurationService configurationService,MysoftMemoryCache mysoftMemoryCache)
        {
            _mysoftApiDomainService = mysoftApiService;
            _mysoftContextFactory = mysoftContextFactory;
            _mysoftMemoryCache = mysoftMemoryCache;
            _configurationService = configurationService;
        }
        public MipInfo GetMipInfo()
        {
            // 直接读配置，不通过接口读取
            MipInfo mipInfo = new MipInfo();
            mipInfo.ServiceUrl = _configurationService.GetConfigurationItemByKey(EMCConfigConst.IpassUrl);
            return mipInfo;
        }
    }
}

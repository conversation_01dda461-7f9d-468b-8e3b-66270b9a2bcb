using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.UnifiedApplicationAuthentication.Client;
using Mysoft.UnifiedApplicationAuthentication.Client.Models;

namespace Mysoft.GPTEngine.Domain.ApiAuthorization
{
    public class MySoftAuth : IAuthorization
    {
        private readonly IConfigurationService _configurationService;
        private IHttpContextAccessor _httpContextAccessor;

        public MySoftAuth(Kernel kernel, MysoftMemoryCache mysoftMemoryCache, IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor)
        {
            _configurationService = configurationService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task AuthenticateRequestAsync(HttpRequestMessage request, OpenApiRequestOptions options,
            CancellationToken cancellationToken = default)
        {
            var myApiAuthorizationToken = await GetMyApiAuthorization(options.MysoftContextDto);
            AccessTokenContent accessTokenContent = _httpContextAccessor.GetItem<AccessTokenContent>(nameof(AccessTokenContent));
            AuthHelper.AppendAuthHeaders(options.MysoftContextDto, request.Headers, myApiAuthorizationToken, accessTokenContent);

            await Task.CompletedTask;
        }
        
        public async Task<String> GetMyApiAuthorization(MysoftContext mysoftContext)
        {
            if (mysoftContext.AuthInfo == null)
            {
                return await Task.FromResult("");
            }

            UnifiedApplicationAuthenticationOptions _options = new UnifiedApplicationAuthenticationOptions
            {
                Enable = true,
                JwkSetJson = _configurationService.GetConfigurationItemByKey("appSecret"),
                ClientId = mysoftContext.AuthInfo == null ? "4200" : mysoftContext.AuthInfo.ClientId
            };
 
            UnifiedApplicationAuthenticationClient _client = new UnifiedApplicationAuthenticationClient(_options);
            UnifiedApplicationAuthenticationProvider _provider = new UnifiedApplicationAuthenticationProvider(_client);
            var jwt = _client.CreateToken();
            return await Task.FromResult(jwt);
        }

    }
}

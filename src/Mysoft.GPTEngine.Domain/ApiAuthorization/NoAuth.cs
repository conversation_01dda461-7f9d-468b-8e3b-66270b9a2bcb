using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.ApiAuthorization
{
    public class NoAuth : IAuthorization
    {
        public NoAuth(Kernel kernel, MysoftMemoryCache mysoftMemoryCache, IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor)
        {
        }

        public async Task AuthenticateRequestAsync(HttpRequestMessage request, OpenApiRequestOptions options, CancellationToken cancellationToken = default)
        { 
            await Task.CompletedTask;
        }
    }
}

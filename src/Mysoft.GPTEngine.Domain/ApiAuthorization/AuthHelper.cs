using System;
using System.Net.Http.Headers;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Domain.ApiAuthorization
{
    public static class AuthHelper
    {
        public static void AppendAuthHeaders(MysoftContext mysoftContext, IHeaderDictionary headers,
            String myApiAuthorizationToken, AccessTokenContent accessTokenContent)
        {
            if (string.IsNullOrWhiteSpace(myApiAuthorizationToken) == false)
            {
                headers["my-api-Authorization"] = myApiAuthorizationToken;
                // 开启用户鉴权，并且鉴权方式为OAuth，从access_token中获取用户
                if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 1)
                {
                    headers["auth_user_info"] = "{\"UserCode\":\"" + (accessTokenContent == null ? "" : accessTokenContent.UserCode) + "\"}";
                }

                // 开启用户鉴权，并且鉴权方式为PAT，从platform_token获取开发者
                if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 0)
                {
                    headers["auth_user_info"] = "{\"UserCode\":\"" + mysoftContext.ApplicationPublisherUserCode + "\"}";
                }

                // 未开启用户鉴权，使用admin用户
                if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 0)
                {
                    headers["auth_user_info"] = "{\"UserCode\":\"admin\"}";
                }

                if (mysoftContext.AuthType == 1)
                {
                    headers["auth_user_info"] = "{\"UserCode\":\"" + mysoftContext.UserContext.UserCode + "\"}";
                }
            }
            else
            {
                headers["appid"] = GPTAppInfo.AppId;
                headers["appkey"] = GPTAppInfo.AppKey;
            }

            headers["tenantCode"] = mysoftContext.TenantCode;
        }
        
        public static void AppendAuthHeaders(MysoftContext mysoftContext, HttpRequestHeaders headers,
            String myApiAuthorizationToken, AccessTokenContent accessTokenContent)
        {
            if (string.IsNullOrWhiteSpace(myApiAuthorizationToken) == false)
            {
                headers.Add("my-api-Authorization", myApiAuthorizationToken);
                // 开启用户鉴权，并且鉴权方式为OAuth，从access_token中获取用户
                if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 1)
                {
                    headers.Add("auth_user_info", "{\"UserCode\":\"" + (accessTokenContent == null ? "" : accessTokenContent.UserCode) + "\"}");
                }

                // 开启用户鉴权，并且鉴权方式为PAT，从platform_token获取开发者
                if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 1 && mysoftContext.AuthorizationType == 0)
                {
                    headers.Add("auth_user_info", "{\"UserCode\":\"" + mysoftContext.ApplicationPublisherUserCode + "\"}");
                }

                // 未开启用户鉴权，使用admin用户
                if (mysoftContext.AuthType == 0 && mysoftContext.EnableUserAuthorization == 0)
                {
                    headers.Add("auth_user_info", "{\"UserCode\":\"admin\"}");
                }

                if (mysoftContext.AuthType == 1)
                {
                    headers.Add("auth_user_info", "{\"UserCode\":\"" + mysoftContext.UserContext.UserCode + "\"}");
                }
            }
            else
            {
                headers.Add("appid", GPTAppInfo.AppId);
                headers.Add("appkey", GPTAppInfo.AppKey);
            }

            headers.Add("tenantCode", mysoftContext.TenantCode);
        }
    }
}
using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.ApiAuthorization
{
    public static class ApiAuthorizationFactory
    {
        private static Dictionary<int, Type> authorization = new Dictionary<int, Type>
        {
            { 0, typeof(NoAuth) },
            { 1, typeof(OAuth) }, 
            { 2, typeof(MySoftAuth) },
            { 3, typeof(IpassAuth) }
        };

        public static IAuthorization GetAuthorization(int authMode, Kernel kernel, MysoftMemoryCache mysoftMemoryCache, IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor)
        {
            if (authorization.TryGetValue(authMode, out Type authorizationType))
            {
                // 参数列表
                object[] parameters = new object[] { kernel, mysoftMemoryCache, configurationService, httpContextAccessor };
                // 参数列表
                IAuthorization authorization = (IAuthorization)Activator.CreateInstance(authorizationType, parameters);
                return authorization;
            }
            else
            {
                throw new InvalidOperationException($"Unsupported authMode: {authMode}");
            }
        }
    }
}

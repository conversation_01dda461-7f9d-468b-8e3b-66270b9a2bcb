using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.ApiAuthorization
{
    public class OAuth : IAuthorization
    {
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        public OAuth(Kernel kernel, MysoftMemoryCache mysoftMemoryCache, IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor)
        {
            _mysoftMemoryCache = mysoftMemoryCache;
        }

        public async Task AuthenticateRequestAsync(HttpRequestMessage request,OpenApiRequestOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                string accessToken = _mysoftMemoryCache.GetCustomerCache<string>(options.CreateTokenUrl);
                if (string.IsNullOrEmpty(accessToken))
                {
                    HttpClient _httpClient = new HttpClient();
                    List<Param> paramList = JsonConvert.DeserializeObject<List<Param>>(options.ParamListStr);
                    using var requestAuth = new HttpRequestMessage(HttpMethod.Post, options.CreateTokenUrl);
                    InitRequestParam(requestAuth, options, paramList);
                    HttpResponseMessage response = await _httpClient.SendAsync(requestAuth);
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(jsonResponse);
                    accessToken = tokenResponse.AccessToken;
                    _mysoftMemoryCache.SetCustomerCache<string>(options.CreateTokenUrl, accessToken, tokenResponse.ExpiresIn / 60);
                }
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                await Task.CompletedTask;
            }
            catch (Exception e)
            {
                string errMsg = string.Format(ErrMsgConst.Plugin_ErrMsg, options.CreateTokenUrl, e.Message);
                throw new PluginException(errMsg, e);
            }
        }

        private void InitRequestParam(HttpRequestMessage requestAuth, OpenApiRequestOptions options, List<Param> paramList)
        {
            var formData = new List<KeyValuePair<string, string>>();
            foreach (var param in paramList)
            {
                switch (param.ParamType)
                {
                    case "1": // Header
                        requestAuth.Headers.Add(param.ParamsName, param.DefaultValue);
                        break;
                    case "3": // Request URI
                        requestAuth.RequestUri = new Uri(options.CreateTokenUrl + "/" + param.DefaultValue);
                        break;
                    case "4": // Form
                        formData.Add(new KeyValuePair<string, string>(param.ParamsName, param.DefaultValue));
                        break;
                    default:
                        break;
                }
            }
            if (formData.Count > 0)
            {
                requestAuth.Content = new FormUrlEncodedContent(formData);
            }
            // 组装body
            var bodyParams = paramList.Where(p => p.ParamType == "2").ToList();
            if (bodyParams.Any())
            {
                var bodyObject = new Dictionary<string, string>();
                foreach (var param in bodyParams)
                {
                    bodyObject[param.ParamsName] = param.DefaultValue;
                }
                var jsonContent = JsonConvert.SerializeObject(bodyObject);
                requestAuth.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            }
        }

        public class Param
        {
            public string ParamsName { get; set; }

            public string DefaultValue { get; set; }

            public string ParamType { get; set; }
        }

        public class TokenResponse
        {
            [JsonProperty("access_token")]
            public string AccessToken { get; set; }
            public string TokenType { get; set; }
            [JsonProperty("expires_in")]
            public int ExpiresIn { get; set; }
            public object ExampleParameter { get; set; }
        }
    }
}

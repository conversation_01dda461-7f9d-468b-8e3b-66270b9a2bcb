using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.UnifiedApplicationAuthentication.Client;
using Mysoft.UnifiedApplicationAuthentication.Client.Models;
using Newtonsoft.Json;
using HttpMethod = System.Net.Http.HttpMethod;

namespace Mysoft.GPTEngine.Domain.ApiAuthorization
{
    public class IpassAuth : IAuthorization
    {

        public IpassAuth(Kernel kernel, MysoftMemoryCache mysoftMemoryCache, IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor)
        {
        }

        public async Task AuthenticateRequestAsync(HttpRequestMessage request, OpenApiRequestOptions options,
            CancellationToken cancellationToken = default)
        {
            
            var myApiAuthorizationToken = await GetMyApiAuthorization(options);
            if (string.IsNullOrWhiteSpace(myApiAuthorizationToken) == false)
            {
                request.Headers.Add("my-api-Authorization", myApiAuthorizationToken);
            }
            else
            {
                var token = await GetIPassApiToken(options);
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
            

            await Task.CompletedTask;
        }
        
        private async Task<string> GetIPassApiToken(OpenApiRequestOptions options)
        {
            HttpClient httpClient = new HttpClient();
            var mipInfo = options.MipInfoDto;
            var tokenParam = new
            {
                client_id = mipInfo.ErpAppName,
                client_secret = mipInfo.ErpAppPwd
            };
            var jsonBody = JsonConvert.SerializeObject(tokenParam);
            var httpContent = new StringContent(jsonBody, Encoding.UTF8, "application/json");
            var uri = mipInfo.ServiceUrl + "/MIPApiAuth/Token";
            var result = await this.SendRequestAsync(httpClient, uri, HttpMethod.Post, httpContent, default);
            MipApiTokenDto mipApiTokenDto = JsonConvert.DeserializeObject<MipApiTokenDto>(result);
            return await Task.FromResult(mipApiTokenDto.Access_token);
        }
        
        public async Task<string> SendRequestAsync(HttpClient httpClient, string uri, HttpMethod method, HttpContent requestContent, CancellationToken cancellationToken, AuthenticationHeaderValue authenticationHeaderValue=null)
        {
            using var request = new HttpRequestMessage(method, uri) { Content = requestContent };
            if (authenticationHeaderValue != null)
            {
                request.Headers.Authorization = authenticationHeaderValue;
            }
            using var response = await httpClient.SendWithSuccessCheckAsync(request, cancellationToken).ConfigureAwait(false);
            return await response.Content.ReadAsStringWithExceptionMappingAsync().ConfigureAwait(false);
        }
        
        public async Task<String> GetMyApiAuthorization(OpenApiRequestOptions options)
        {
            if (options.MysoftContextDto.AuthInfo == null)
            {
                return await Task.FromResult("");
            }

            UnifiedApplicationAuthenticationOptions _options = new UnifiedApplicationAuthenticationOptions
            {
                Enable = true,
                JwkSetJson = options.MysoftContextDto.AuthInfo.Jwks,
                ClientId = options.MysoftContextDto.AuthInfo.ClientId
            };
 
            UnifiedApplicationAuthenticationClient _client = new UnifiedApplicationAuthenticationClient(_options);
            UnifiedApplicationAuthenticationProvider _provider = new UnifiedApplicationAuthenticationProvider(_client);
            var jwt = _client.CreateToken();
            return await Task.FromResult(jwt);
        }

    }
}

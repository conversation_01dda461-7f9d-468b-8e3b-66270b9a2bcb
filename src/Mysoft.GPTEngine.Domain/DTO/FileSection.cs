using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.Domain.DTO
{
    public class FileSection
    {
        /// <summary>
        /// Text page number/Audio segment number/Video scene number
        /// </summary>
        [JsonPropertyOrder(0)]
        [JsonPropertyName("number")]
        public int Number { get; set; }

        /// <summary>
        /// Whether the first/last sentence may continue from the previous/into
        /// the next section (e.g. like PDF docs).
        /// true: the first/last sentence do not cross over, the first doesn't
        ///       continue from the previous section, and the last sentence ends
        ///       where the section ends (e.g. Powerpoint, Excel).
        /// false: the first sentence may be a continuation from the previous section,
        ///        and the last sentence may continue into the next section.
        /// </summary>
        [JsonPropertyOrder(1)]
        [JsonPropertyName("complete")]
        public bool SentencesAreComplete { get; set; }

        /// <summary>
        /// Page text content
        /// </summary>
        [JsonPropertyOrder(2)]
        [JsonPropertyName("content")]
        public string Content { get; set; }

        /// <summary>
        /// 切片标题
        /// </summary>
        [JsonPropertyOrder(3)]
        [JsonPropertyName("title")]
        public string Title { get; set; }
        
        /// <summary>
        /// 元数据(数据库中使用)
        /// </summary>
        [JsonPropertyOrder(4)]
        [JsonPropertyName("metadata")]
        public string Metadata { get; set; }
        
        /// <summary>
        /// 切片数据来源ID(数据库中使用)
        /// </summary>
        [JsonPropertyOrder(4)]
        [JsonPropertyName("thirdId")]
        public string ThirdId { get; set; }

        public FileSection(int number, string? content, bool sentencesAreComplete, string title = "")
        {
            this.Number = number;
            this.SentencesAreComplete = sentencesAreComplete;
            this.Content = content ?? string.Empty;
            this.Title = title;
        }
        
        public FileSection()
        {}
        
    }
}

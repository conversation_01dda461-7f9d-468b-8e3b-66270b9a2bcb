using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.Domain.DTO
{
    public class ApprovalDataDto
    {
        [JsonPropertyName("data")]
        public string Data { get; set; }
        
        [JsonPropertyName("businessFiles")]
        public string BusinessFiles { get; set; }

        [JsonPropertyName("rule")]
        public string Rule { get; set; }
        
        [JsonPropertyName("name")]
        public string Name { get; set; }
        
        [JsonPropertyName("errordemo")]
        public string ErrorDemo { get; set; }

    }
}

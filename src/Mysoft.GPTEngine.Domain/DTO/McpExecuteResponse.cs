using System;

namespace Mysoft.GPTEngine.Domain.DTO
{
    /// <summary>
    /// MCP工具执行响应DTO
    /// </summary>
    public class McpExecuteResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 执行时间（毫秒）
        /// </summary>
        public long ExecutionTimeMs { get; set; }

        /// <summary>
        /// 工具名称
        /// </summary>
        public string ToolName { get; set; }

        /// <summary>
        /// 执行时间戳
        /// </summary>
        public DateTime ExecutedAt { get; set; } = DateTime.Now;
    }
}

using System;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Domain.DTO
{
    public class OpenApiRequestOptions
    {
        public int AuthMode { get; set; }
        
        public string Metadata { get; set; }
        
        public string CreateTokenUrl { get; set; }
        
        public string ParamListStr { get; set; }
        
        public KernelArguments Arguments { get; set; }
        
        public string Path { get; set; }
        
        public string OperationId { get; set; }
        
        public Uri ServerUrlOverride { get; set; }
        
        public MipInfo MipInfoDto { get; set; }
        
        public MysoftContext MysoftContextDto  { get; set; }
        
    }
}
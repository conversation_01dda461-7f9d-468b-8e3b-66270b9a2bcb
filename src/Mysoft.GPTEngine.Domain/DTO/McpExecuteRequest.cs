using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.DTO
{
    /// <summary>
    /// MCP工具执行请求DTO
    /// </summary>
    public class McpExecuteRequest
    {
        /// <summary>
        /// 工具GUID（优先使用）
        /// </summary>
        public string ToolGUID { get; set; }

        /// <summary>
        /// 工具名称（当ToolGUID为空时使用）
        /// </summary>
        public string ToolName { get; set; }

        /// <summary>
        /// 工具参数
        /// </summary>
        public Dictionary<string, object> Arguments { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 自定义请求头
        /// </summary>
        public Dictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();
    }
}

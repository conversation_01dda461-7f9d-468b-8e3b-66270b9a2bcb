using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;

namespace Mysoft.GPTEngine.Domain.DTO
{
    public class FileContent
    {
        [JsonPropertyOrder(0)]
        [JsonPropertyName("sections")]
        public List<FileSection> Sections { get; set; } = new List<FileSection>();

        [JsonPropertyOrder(1)]
        [JsonPropertyName("mimeType")]
        public string MimeType { get; set; }

        public FileContent(string mimeType)
        {
            this.MimeType = mimeType;
        }

        public List<BatchDocumentUploadParam> BatchDocumentUploadParams { get; set; } = new List<BatchDocumentUploadParam>();

        public List<HyperLinkDto> HyperLinkList { get; set; } = new List<HyperLinkDto>();
    }

    public class HyperLinkDto
    {
        public Guid HyperLinkGUID { get; set; }

        public string HyperLinkURL { get; set; }

        public string HyperLinkText { get; set; }
    }

    //批量上传文档服务的DTO
    public class BatchDocumentUploadParam
    {
        public DocumentUploadParam documentUploadParam
        {
            get;
            set;
        }=new DocumentUploadParam();

        public byte[] documentContent
        {
            get;
            set;
        }

        public string documentContentBase64
        {
            get;
            set;
        }
    }

    public class DocumentUploadParam
    {
        public String name
        {
            get; set;
        }
        public String fileGUID
        {
            get; set;
        }
        public String fileName
        {
            get; set;
        }
        public String fileSize
        {
            get; set;
        }
        public String docType
        {
            get; set;
        }
        public int chunks
        {
            get; set;
        } = 1;
        public int chunk
        {
            get; set;
        }
        public long chunkSize
        {
            get; set;
        }
        public Boolean isTempFile
        {
            get; set;
        } = false;
        public Boolean isEnableChuck
        {
            get; set;
        } = false;
        public String subSysFolder
        {
            get; set;
        } = SubSysConst.GPT_SUBSYS_CODE;
        public String fkIdentification
        {
            get; set;
        }
        public Boolean isSaveInDb
        {
            get; set;
        } = false;
        public String hashCode
        {
            get; set;
        }
        public Boolean isSaveHashCodeInDb
        {
            get; set;
        } = true;
    }
}

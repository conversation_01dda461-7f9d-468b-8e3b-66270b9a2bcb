using System.Collections.Generic;
using System.Text.Json.Nodes;

namespace Mysoft.GPTEngine.Domain.DTO
{
    /// <summary>
    /// MCP工具信息DTO
    /// </summary>
    public class McpToolInfo
    {
        /// <summary>
        /// 工具GUID（来自数据库）
        /// </summary>
        public string ToolGUID { get; set; }

        /// <summary>
        /// 工具名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 工具标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 工具描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 输入参数架构
        /// </summary>
        public JsonObject InputSchema { get; set; }

        /// <summary>
        /// 输出架构
        /// </summary>
        public string OutputSchema { get; set; }

        /// <summary>
        /// 参数列表
        /// </summary>
        public List<McpToolParameter> Parameters { get; set; } = new List<McpToolParameter>();

        /// <summary>
        /// 服务GUID
        /// </summary>
        public string ServiceGUID { get; set; }

        /// <summary>
        /// 最后测试输入
        /// </summary>
        public string LastTestInput { get; set; }

        /// <summary>
        /// 最后测试输出
        /// </summary>
        public string LastTestOutput { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
    }

    /// <summary>
    /// MCP工具参数信息
    /// </summary>
    public class McpToolParameter
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public object DefaultValue { get; set; }
    }
}

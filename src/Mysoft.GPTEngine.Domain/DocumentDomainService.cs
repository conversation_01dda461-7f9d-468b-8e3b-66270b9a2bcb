using AutoMapper;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain
{
    /// <summary>
    /// 文档服务操作类
    /// </summary>
    public class DocumentDomainService : DomainServiceBase
    {
        //构造方法
        public DocumentDomainService(IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper) : base(mysoftContextFactory, httpContextAccessor, mapper)
        {
        }


    }
}

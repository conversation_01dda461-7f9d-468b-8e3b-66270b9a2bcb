using System;
using Microsoft.Extensions.Caching.Memory;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain
{
    public class MysoftMemoryCacheBase
    {

        readonly IMemoryCache _cache;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        
        public MysoftMemoryCacheBase(IMemoryCache memoryCache, IMysoftContextFactory mysoftContextFactory)
        {   
            _cache = memoryCache;
            _mysoftContextFactory = mysoftContextFactory;
        }
        
        public void Set(string key, object value, MemoryCacheEntryOptions tenantCacheEntryOptions)
        {
            if (tenantCacheEntryOptions != null)
            {
                _cache.Set(GetFullKey(key), value, tenantCacheEntryOptions);
            }
            else
            {
                _cache.Set(GetFullKey(key), value);
            }
            
        }

        public T Get<T>(string key)
        {
            if (!_cache.TryGetValue(GetFullKey(key), out T cacheEntry))  
            {  
                return default(T);  
            }  
            return cacheEntry;  
        }
        
        public string Get<PERSON><PERSON><PERSON>ey(String key)
        {
            return _mysoftContextFactory.GetMysoftContext().TenantCode + key;
        }
    }
}
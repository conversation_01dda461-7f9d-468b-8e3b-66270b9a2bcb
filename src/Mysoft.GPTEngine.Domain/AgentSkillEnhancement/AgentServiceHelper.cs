using System;
using System.ClientModel.Primitives;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Services;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{
    /// <summary>
    /// Agent服务辅助类，提供executionSetting参数处理和通用方法
    /// </summary>
    public class AgentServiceHelper
    {
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly Kernel _kernel;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly ILogger<AgentServiceHelper> _logger;
        private readonly KnowledgeRepository _knowledgeRepository;

        public AgentServiceHelper(
            IKnowledgeDomainService knowledgeDomainService,
            Kernel kernel,
            MysoftApiService mysoftApiDomainService,
            IMysoftContextFactory mysoftContextFactory,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper,
            ILogger<AgentServiceHelper> logger,
            KnowledgeRepository knowledgeRepository = null)
        {
            _knowledgeDomainService = knowledgeDomainService;
            _kernel = kernel;
            _mysoftApiDomainService = mysoftApiDomainService;
            _mysoftContextFactory = mysoftContextFactory;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _logger = logger;
            _knowledgeRepository = knowledgeRepository;
        }

        /// <summary>
        /// 获取temperature参数，确保兼容性
        /// </summary>
        /// <param name="executionSetting">执行设置</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>temperature值，如果未设置则返回-1</returns>
        public static float GetTemperatureParameter(executionSettingDto executionSetting, ILogger logger = null)
        {
            try
            {
                if (executionSetting == null)
                {
                    logger?.LogDebug("[GetTemperatureParameter] executionSetting为null，返回默认值");
                    return -1f; // 表示未设置，使用系统默认值
                }

                float? temperature = executionSetting.temperature;
                if (temperature.HasValue)
                {
                    logger?.LogDebug("[GetTemperatureParameter] 从executionSetting获取temperature: {temperature}", temperature.Value);
                    return temperature.Value;
                }
                else
                {
                    logger?.LogDebug("[GetTemperatureParameter] temperature为null，返回默认值");
                    return -1f; // 表示未设置，使用系统默认值
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "[GetTemperatureParameter] 获取temperature参数时发生错误: {message}", ex.Message);
                return -1f; // 出错时返回默认值
            }
        }

        /// <summary>
        /// 获取topP参数，确保兼容性
        /// </summary>
        /// <param name="executionSetting">执行设置</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>topP值，如果未设置则返回-1</returns>
        public static float GetTopPParameter(executionSettingDto executionSetting, ILogger logger = null)
        {
            try
            {
                if (executionSetting == null)
                {
                    logger?.LogDebug("[GetTopPParameter] executionSetting为null，返回默认值");
                    return -1f; // 表示未设置，使用系统默认值
                }

                float? topP = executionSetting.topP;
                if (topP.HasValue)
                {
                    logger?.LogDebug("[GetTopPParameter] 从executionSetting获取topP: {topP}", topP.Value);
                    return topP.Value;
                }
                else
                {
                    logger?.LogDebug("[GetTopPParameter] topP为null，返回默认值");
                    return -1f; // 表示未设置，使用系统默认值
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "[GetTopPParameter] 获取topP参数时发生错误: {message}", ex.Message);
                return -1f; // 出错时返回默认值
            }
        }

        /// <summary>
        /// 获取maxTokens参数，确保兼容性
        /// </summary>
        /// <param name="executionSetting">执行设置</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>maxTokens值，如果未设置则返回默认值8192</returns>
        public static int GetMaxTokensParameter(executionSettingDto executionSetting, ILogger logger = null)
        {
            try
            {
                if (executionSetting == null)
                {
                    logger?.LogDebug("[GetMaxTokensParameter] executionSetting为null，返回默认值8192");
                    return 8192; // 默认值
                }

                int? maxTokens = executionSetting.maxTokens;
                if (maxTokens.HasValue)
                {
                    logger?.LogDebug("[GetMaxTokensParameter] 从executionSetting获取maxTokens: {maxTokens}", maxTokens.Value);
                    
                    // 如果值为0或负数，使用默认值
                    if (maxTokens.Value <= 0)
                    {
                        logger?.LogDebug("[GetMaxTokensParameter] maxTokens值无效({maxTokens})，使用默认值8192", maxTokens.Value);
                        return 8192;
                    }
                    
                    return maxTokens.Value;
                }
                else
                {
                    logger?.LogDebug("[GetMaxTokensParameter] maxTokens为null，返回默认值8192");
                    return 8192; // 默认值
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "[GetMaxTokensParameter] 获取maxTokens参数时发生错误: {message}", ex.Message);
                return 8192; // 出错时返回默认值
            }
        }

        /// <summary>
        /// 获取enableThinking参数，确保兼容性
        /// </summary>
        /// <param name="executionSetting">执行设置</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>是否启用思考模式，默认为false</returns>
        public static bool GetEnableThinkingParameter(executionSettingDto executionSetting, ILogger logger = null)
        {
            try
            {
                if (executionSetting == null)
                {
                    logger?.LogDebug("[GetEnableThinkingParameter] executionSetting为null，返回默认值false");
                    return false; // 默认不启用思考模式
                }

                bool? enableThinking = executionSetting.enableThinking;
                if (enableThinking.HasValue)
                {
                    logger?.LogDebug("[GetEnableThinkingParameter] 从executionSetting获取enableThinking: {enableThinking}", enableThinking.Value);
                    return enableThinking.Value;
                }
                else
                {
                    logger?.LogDebug("[GetEnableThinkingParameter] enableThinking为null，返回默认值false");
                    return false; // 默认不启用思考模式
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "[GetEnableThinkingParameter] 获取enableThinking参数时发生错误: {message}", ex.Message);
                return false; // 出错时返回默认值
            }
        }

        /// <summary>
        /// 获取thinkingBudget参数，确保兼容性
        /// </summary>
        /// <param name="executionSetting">执行设置</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>思考预算，如果未设置或无效则返回null</returns>
        public static int? GetThinkingBudgetParameter(executionSettingDto executionSetting, ILogger logger = null)
        {
            try
            {
                if (executionSetting == null)
                {
                    logger?.LogDebug("[GetThinkingBudgetParameter] executionSetting为null，返回null");
                    return null;
                }

                int? thinkingBudget = executionSetting.thinkingBudget;
                if (thinkingBudget.HasValue)
                {
                    logger?.LogDebug("[GetThinkingBudgetParameter] 从executionSetting获取thinkingBudget: {thinkingBudget}", thinkingBudget.Value);
                    
                    // 如果值为0或负数，返回null表示不设置预算限制
                    if (thinkingBudget.Value <= 0)
                    {
                        logger?.LogDebug("[GetThinkingBudgetParameter] thinkingBudget值无效({thinkingBudget})，返回null", thinkingBudget.Value);
                        return null;
                    }
                    
                    return thinkingBudget.Value;
                }
                else
                {
                    logger?.LogDebug("[GetThinkingBudgetParameter] thinkingBudget为null，返回null");
                    return null;
                }
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "[GetThinkingBudgetParameter] 获取thinkingBudget参数时发生错误: {message}", ex.Message);
                return null; // 出错时返回null
            }
        }

        /// <summary>
        /// 添加节点日志
        /// </summary>
        public void AddNodeLog(ChatRunDto chatRunDto)
        {
            var nodeLog = new ChatMessageNodeLogDto
            {
                ChatMessageNodeLogGUID = Guid.NewGuid(),
                ChatGUID = chatRunDto.Chat.ChatGUID,
                Name = "Agent",
                Description = "智能体技能",
                Index = 0,
                StartTime = TimeZoneUtility.LocalNow(),
                Config = JsonConvert.SerializeObject(chatRunDto.Agent),
            };
            chatRunDto.NodeLogs.Add(nodeLog);
        }

        /// <summary>
        /// 添加成功节点日志
        /// </summary>
        public void AddSucceedNodeLog(ChatRunDto chatRunDto, string? inputs = null, string? outputs = null)
        {
            var nodeLog = chatRunDto.NodeLogs[0];
            if (nodeLog == null) return;

            nodeLog.EndTime = TimeZoneUtility.LocalNow();
            nodeLog.Inputs = inputs;
            nodeLog.Outputs = outputs ?? outputs;
        }

        /// <summary>
        /// 获取智能体输入参数
        /// </summary>
        public async Task<KernelArguments> GetAgentInputArgument(ChatRunDto chatRunDto)
        {
            var kernelArguments = new KernelArguments()
            {
                ["now"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };
            var inputs = chatRunDto.Agent.Prompt.Inputs;
            foreach (var input in inputs)
            {
                kernelArguments.Add(input.Code, input.LiteralValue);
            }

            return await Task.FromResult(kernelArguments);
        }

        public async Task ArgumentParser(ChatRunDto chatRunDto, List<ParamDto> dtos)
        {
            foreach (var item in dtos)
            {
                if (item.Value == null) continue;
                await ArgumentParser(chatRunDto, item);
            }
        }

        public async Task ArgumentParser(ChatRunDto chatRunDto, ParamDto item)
        {
            if (item.Value == null) return;
            switch (item.Value.Type)
            {
                case "literal":
                    item.LiteralValue = item.Value.Content;
                    item.LiteralCode = string.Format(KernelArgumentsConstant.NodeInput, null, item.Code);
                    break;
                case "ref":
                    await RefArgumentParser(chatRunDto, item);
                    break;
                default: break;
            }
        }

        public async Task RefArgumentParser(ChatRunDto chatRunDto, ParamDto paramDto)
        {
            var data = await GetLiteralValue(chatRunDto, paramDto.Value.Content);
            if (data.Item1 is null)
            {
                return;
            }

            if (data.Item2 is ParamDto item)
            {
                paramDto.Name = paramDto.Name ?? item.Name;
                paramDto.Type = item.Type;
                //paramDto.Required = item.Required;
                paramDto.LiteralValue = string.IsNullOrEmpty(item.LiteralValue)
                    ? await GetArgumentValue<string>(chatRunDto, paramDto.Value.Content)
                    : item.LiteralValue;
                paramDto.LiteralCode =
                    string.IsNullOrEmpty(item.LiteralCode) ? paramDto.Value.Content : item.LiteralCode;

                if (paramDto.Type == FieldTypeConstant.ArrayField && paramDto.Schema != null &&
                    paramDto.Schema.Count > 0)
                {
                    var unSelectd = item.Schema.Where(item1 => !paramDto.Schema.Any(item2 =>
                            item2.Value.Type == "ref" && item2.Value.Content == item1.Code))
                        .ToList();
                    foreach (var unselectItem in unSelectd)
                    {
                        paramDto.LiteralValue =
                            JsonHelper.RemoveFieldFromJsonArray(item.LiteralValue, unselectItem.Code);
                    }

                    foreach (var scheme in paramDto.Schema)
                    {
                        //判断系统关键字
                        if (scheme.Value.Content.StartsWith(KernelArgumentsConstant._prefix))
                        {
                            var str = await GetArgumentValue<string>(chatRunDto, scheme.Value.Content);
                            paramDto.LiteralValue =
                                JsonHelper.AddFieldToJsonArray(paramDto.LiteralValue, scheme.Code, str);
                        }

                        if (scheme.Value.Type == "literal")
                        {
                            paramDto.LiteralValue = JsonHelper.AddFieldToJsonArray(paramDto.LiteralValue, scheme.Code,
                                scheme.Value.Content);
                        }
                    }
                }
            }
        }

        public async Task<(string, object)> GetLiteralValue(ChatRunDto chatRunDto, string key)
        {
            if (key == KernelArgumentsConstant.Input || key.StartsWith(KernelArgumentsConstant._prefixKeyword))
            {
                return (key, new ParamDto
                {
                    LiteralValue = await GetArgumentValue<string>(chatRunDto, key),
                    LiteralCode = key,
                    Type = FieldTypeConstant.TextField,
                });
            }
            else
            {
                return (key, new ParamDto
                {
                    LiteralValue = await GetArgumentValue<string>(chatRunDto, key),
                    LiteralCode = key,
                    Type = FieldTypeConstant.TextField,
                });
            }

            return (null, null);
        }

        public async Task<T> GetArgumentValue<T>(ChatRunDto chatRunDto, string key)
        {
            if (chatRunDto.ChatArguments.ContainsName(key))
            {
                return await Task.FromResult((T)chatRunDto.ChatArguments[key]);
            }

            return await Task.FromResult(default(T));
        }

        public async Task<DocumentReadingDto> DocumentReading(ChatRunDto chatRunDto)
        {
            //判断chatRunDto.ChatInput.Documents集合對象是否爲空
            if (chatRunDto.ChatInput.Documents == null || !chatRunDto.ChatInput.Documents.Any())
            {
                return new DocumentReadingDto();
            }

            DocumentReadingDto documentReadingDto = new DocumentReadingDto();

            var guid = chatRunDto.ChatInput.Documents.FirstOrDefault()?.Id;
            if (guid == null)
            {
                return documentReadingDto;
            }

            List<DocumentInfoBaseDto> documentInfoBaseDtos =
                await _knowledgeDomainService.GetDocumentInfo(new List<Guid> { guid.Value });
            //拿到下载地址
            string downloadUrl = documentInfoBaseDtos[0].DownloadUrl;
            string fileName = documentInfoBaseDtos[0].FileName;

            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(downloadUrl); // 下载文件

                MemoryStream stream = new MemoryStream(fileBytes);
                string type = FileTypeConvertHelper.GetFileType(fileName);
                IDocumentDecoder documentDecoder = DocumentDecoderFactory.GetDocumentDecoder(type, this._kernel,
                    this._mysoftApiDomainService, this._mysoftContextFactory, this._httpContextAccessor, this._mapper);
                var nodeConfig = new NodeConfig();
                documentReadingDto = await documentDecoder.DomentReadAsync(stream, nodeConfig);
            }

            return documentReadingDto;
        }
         /// <summary>
        /// 从内容项中提取文本内容
        /// </summary>
        /// <param name="item">内容项</param>
        /// <returns>提取的文本内容</returns>
        public string ExtractTextFromContentItem(KernelContent item)
        {
            try
            {
                // 处理已知的内容类型
                if (item is TextContent textContent)
                {
                    return textContent.Text ?? "";
                }

                // 对于MCP工具返回的TextContentBlock等类型，尝试通过反射获取文本内容
                var itemType = item.GetType();
                _logger.LogDebug("[ExtractTextFromContentItem] 尝试从类型 {type} 提取文本内容", itemType.FullName);

                // 尝试获取Text属性
                var textProperty = itemType.GetProperty("Text");
                if (textProperty != null && textProperty.PropertyType == typeof(string))
                {
                    var textValue = textProperty.GetValue(item) as string;
                    if (!string.IsNullOrEmpty(textValue))
                    {
                        _logger.LogDebug("[ExtractTextFromContentItem] 通过Text属性提取内容: {content}", textValue);
                        return textValue;
                    }
                }

                // 尝试获取Content属性
                var contentProperty = itemType.GetProperty("Content");
                if (contentProperty != null && contentProperty.PropertyType == typeof(string))
                {
                    var contentValue = contentProperty.GetValue(item) as string;
                    if (!string.IsNullOrEmpty(contentValue))
                    {
                        _logger.LogDebug("[ExtractTextFromContentItem] 通过Content属性提取内容: {content}", contentValue);
                        return contentValue;
                    }
                }

                // 尝试获取Value属性
                var valueProperty = itemType.GetProperty("Value");
                if (valueProperty != null && valueProperty.PropertyType == typeof(string))
                {
                    var valueValue = valueProperty.GetValue(item) as string;
                    if (!string.IsNullOrEmpty(valueValue))
                    {
                        _logger.LogDebug("[ExtractTextFromContentItem] 通过Value属性提取内容: {content}", valueValue);
                        return valueValue;
                    }
                }

                // 如果都没有找到，记录警告并返回ToString()结果
                var toStringResult = item.ToString();
                _logger.LogWarning("[ExtractTextFromContentItem] 无法从类型 {type} 中提取文本内容，使用ToString()结果: {result}",
                    itemType.FullName, toStringResult);

                return toStringResult ?? "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ExtractTextFromContentItem] 提取文本内容时发生错误，类型: {type}", item?.GetType().FullName);
                return item?.ToString() ?? "";
            }
        }

        /// <summary>
        /// 将ChatMessageContent转换为ChatMessageDto
        /// </summary>
        /// <param name="message">ChatMessageContent</param>
        /// <param name="index">消息索引</param>
        /// <returns>ChatMessageDto或null</returns>
        public ChatMessageDto ConvertChatMessageToDto(ChatMessageContent message, int index)
        {
            try
            {
                // 处理Assistant角色的工具调用消息
                if (message.Role == AuthorRole.Assistant && message.Items != null)
                {
                    foreach (var item in message.Items)
                    {
                        if (item is FunctionCallContent functionCall)
                        {
                            // 手动构建参数字典
                            var argumentsDict = new Dictionary<string, string>();
                            if (functionCall.Arguments != null)
                            {
                                foreach (var kvp in functionCall.Arguments)
                                {
                                    argumentsDict[kvp.Key] = kvp.Value?.ToString() ?? "";
                                }
                            }

                            var toolCallContent = new
                            {
                                function_name = $"{functionCall.PluginName}.{functionCall.FunctionName}",
                                arguments = argumentsDict,
                                id = functionCall.Id
                            };
                            var toolCallJson = JsonSerializer.Serialize(toolCallContent);

                            return new ChatMessageDto
                            {
                                Role = ChatRoleConstant.Assistant,
                                Content = $"[TOOL_CALL] {toolCallJson}",
                                Index = index,
                                IsHidden = 1 // 隐藏工具调用消息
                            };
                        }
                    }
                }

                // 处理Tool角色的工具返回消息
                if (message.Role == AuthorRole.Tool)
                {
                    // 首先检查是否有Items内容
                    if (message.Items != null)
                    {
                        _logger.LogDebug("[ConvertChatMessageToDto] Tool消息包含 {count} 个Items", message.Items.Count);

                        foreach (var item in message.Items)
                        {
                            _logger.LogDebug("[ConvertChatMessageToDto] 处理Item类型: {type}", item.GetType().FullName);

                            // 处理FunctionResultContent类型
                            if (item is FunctionResultContent functionResult)
                            {
                                _logger.LogDebug("[ConvertChatMessageToDto] 处理FunctionResultContent: {plugin}.{function}",
                                    functionResult.PluginName, functionResult.FunctionName);
                                return new ChatMessageDto
                                {
                                    Role = ChatRoleConstant.Tool,
                                    Content = $"[TOOL_RESULT:{functionResult.PluginName}.{functionResult.FunctionName}] {functionResult.Result}",
                                    Index = index,
                                    IsHidden = 1 // 隐藏工具返回消息
                                };
                            }
                            // 处理TextContent类型
                            else if (item is TextContent textContent)
                            {
                                _logger.LogDebug("[ConvertChatMessageToDto] 处理TextContent: {text}", textContent.Text);
                                return new ChatMessageDto
                                {
                                    Role = ChatRoleConstant.Tool,
                                    Content = textContent.Text ?? "",
                                    Index = index,
                                    IsHidden = 1 // 隐藏工具返回消息
                                };
                            }
                            // 处理其他类型的内容项，尝试提取文本内容
                            else
                            {
                                _logger.LogDebug("[ConvertChatMessageToDto] 处理其他类型内容项: {type}", item.GetType().FullName);
                                // 对于MCP工具返回的TextContentBlock等类型，尝试获取其文本内容
                                var itemContent = ExtractTextFromContentItem(item);
                                if (!string.IsNullOrEmpty(itemContent))
                                {
                                    _logger.LogDebug("[ConvertChatMessageToDto] 成功提取内容: {content}", itemContent);
                                    return new ChatMessageDto
                                    {
                                        Role = ChatRoleConstant.Tool,
                                        Content = itemContent,
                                        Index = index,
                                        IsHidden = 1 // 隐藏工具返回消息
                                    };
                                }
                                else
                                {
                                    _logger.LogWarning("[ConvertChatMessageToDto] 无法从类型 {type} 提取有效内容", item.GetType().FullName);
                                }
                            }
                        }
                    }

                    // 如果没有FunctionResultContent，检查是否有直接的content
                    if (!string.IsNullOrEmpty(message.Content))
                    {
                        return new ChatMessageDto
                        {
                            Role = ChatRoleConstant.Tool,
                            Content = message.Content,
                            Index = index,
                            IsHidden = 1 // 隐藏工具返回消息
                        };
                    }
                }

                // 处理普通文本消息
                if (!string.IsNullOrEmpty(message.Content))
                {
                    var role = message.Role.Label switch
                    {
                        "user" => ChatRoleConstant.User,
                        "assistant" => ChatRoleConstant.Assistant,
                        "tool" => ChatRoleConstant.Tool,
                        _ => ChatRoleConstant.Assistant
                    };

                    return new ChatMessageDto
                    {
                        Role = role,
                        Content = message.Content,
                        Index = index
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConvertChatMessageToDto] 转换消息时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 从ChatHistory中提取新增的消息（包含工具调用）
        /// </summary>
        /// <param name="chatRunDto">聊天运行DTO</param>
        /// <param name="userInput">用户输入</param>
        /// <param name="assistantOutput">助手输出</param>
        /// <returns>新增的消息列表</returns>
        public List<ChatMessageDto> ExtractNewMessagesFromChatHistory(ChatRunDto chatRunDto, string userInput, string assistantOutput)
        {
            var newMessages = new List<ChatMessageDto>();

            try
            {
                var batchGuid = chatRunDto?.BatchGuid ?? Guid.NewGuid();
                // 获取用户名信息
                var userName = chatRunDto?.Chat?.UserName ?? string.Empty;

                // 注意：这里的Index将在ConversationMemoryService中重新设置为正确的会话级别索引
                // 这里只是临时设置，确保消息有正确的相对顺序

                // 添加用户消息
                newMessages.Add(new ChatMessageDto
                {
                    Role = ChatRoleConstant.User,
                    Content = userInput,
                    Index = 0, // 临时索引，将在保存时重新设置
                    BatchGUID = batchGuid,
                    UserName = userName, // 设置用户名
                    IsHidden = 0 // 用户消息不隐藏
                });

                // 检查ChatHistory中是否有新增的工具调用消息
                var chatHistory = chatRunDto.ChatHistory;
                if (chatHistory != null && chatRunDto.InitialChatHistoryCount.HasValue)
                {
                    var initialCount = chatRunDto.InitialChatHistoryCount.Value;
                    var currentCount = chatHistory.Count;

                    _logger.LogInformation("[ExtractNewMessagesFromChatHistory] ChatHistory初始长度: {initial}, 当前长度: {current}", initialCount, currentCount);

                    // 提取新增的消息（跳过最后一条，因为那是最终的助手回复）
                    for (int i = initialCount; i < currentCount - 1; i++)
                    {
                        var message = chatHistory[i];
                        var messageDto = ConvertChatMessageToDto(message, newMessages.Count);
                        if (messageDto != null)
                        {
                            messageDto.BatchGUID = batchGuid;
                            messageDto.UserName = userName; // 设置用户名
                            messageDto.Index = newMessages.Count; // 临时索引，将在保存时重新设置
                            newMessages.Add(messageDto);
                        }
                    }
                }

                // 添加最终的助手回复
                newMessages.Add(new ChatMessageDto
                {
                    Role = ChatRoleConstant.Assistant,
                    Content = assistantOutput,
                    Index = newMessages.Count, // 临时索引，将在保存时重新设置
                    BatchGUID = batchGuid,
                    UserName = userName, // 设置用户名
                    IsHidden = 0 // 助手回复不隐藏
                });

                _logger.LogInformation("[ExtractNewMessagesFromChatHistory] 构造了 {count} 条新消息，BatchGUID: {batchGuid}", newMessages.Count, batchGuid);
                return newMessages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ExtractNewMessagesFromChatHistory] 提取新消息时发生错误");
                return new List<ChatMessageDto>();
            }
        }
        /// <summary>
        /// 从StreamingChatMessageContent中提取思考内容
        /// </summary>
        /// <param name="response">流式聊天消息内容</param>
        /// <param name="eventHelper">事件助手</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>思考内容，如果没有则返回空字符串</returns>
        public string ExtractThinkingContent(StreamingChatMessageContent response, AgentSkillEventHelper eventHelper, CancellationToken cancellationToken)
        {
            if (response == null || response.InnerContent == null)
                return "";

            var modelId = response.ModelId;
            JsonNode? jsonContent = null;

            try
            {
                _logger.LogDebug("[ExtractThinkingContent] ModelId: {modelId}, Content: '{content}', Role: {role}",
                    modelId, response.Content ?? "null", response.Role?.ToString() ?? "null");

                // 检查InnerContent的实际类型来决定序列化方法
                var innerContentType = response.InnerContent.GetType();
                _logger.LogDebug("[ExtractThinkingContent] InnerContent类型: {type}", innerContentType.FullName);

                // 优先使用ModelReaderWriter.Write（适用于OpenAI兼容模式和DeepSeek）
                try
                {
                    jsonContent = JsonNode.Parse(ModelReaderWriter.Write(response.InnerContent));
                    _logger.LogDebug("[ExtractThinkingContent] ModelReaderWriter序列化成功");
                }
                catch (Exception ex)
                {
                    _logger.LogDebug("[ExtractThinkingContent] ModelReaderWriter序列化失败，尝试JsonSerializer: {error}", ex.Message);
                    try
                    {
                        jsonContent = JsonNode.Parse(JsonSerializer.Serialize(response.InnerContent, new JsonSerializerOptions { WriteIndented = true }));
                        _logger.LogDebug("[ExtractThinkingContent] JsonSerializer序列化成功");
                    }
                    catch (Exception ex2)
                    {
                        _logger.LogError(ex2, "[ExtractThinkingContent] 所有序列化方法都失败: {error}", ex2.Message);
                        return "";
                    }
                }

                if (jsonContent == null)
                {
                    _logger.LogDebug("[ExtractThinkingContent] JSON内容为空");
                    return "";
                }

                // 添加调试日志，查看实际的JSON结构
                _logger.LogDebug("[ExtractThinkingContent] 完整JSON结构: {json}", jsonContent.ToString());

                // 提取reasoning_content，使用与SemanticKernelActivity相同的逻辑
                var choices = jsonContent["choices"];
                if (!(choices is JsonArray array) || array.Count == 0)
                {
                    _logger.LogDebug("[ExtractThinkingContent] 未找到choices数组或数组为空，JSON根节点包含的属性: {properties}",
                        string.Join(", ", jsonContent.AsObject().Select(kv => kv.Key)));
                    return "";
                }

                var firstChoice = array[0];
                if (firstChoice == null)
                {
                    _logger.LogDebug("[ExtractThinkingContent] 第一个choice为空");
                    return "";
                }

                // 根据实际的JSON结构尝试不同的路径，而不是依赖ModelId
                // 1. 优先尝试OpenAI兼容模式的delta路径（适用于流式响应）
                var delta = firstChoice["delta"];
                if (delta != null)
                {
                    var deltaReasoningContent = delta["reasoning_content"];
                    if (deltaReasoningContent != null && !string.IsNullOrEmpty(deltaReasoningContent.ToString()))
                    {
                        var content = deltaReasoningContent.ToString();
                        _logger.LogInformation("[ExtractThinkingContent] 通过delta路径提取到思考内容: {content}", content);

                        // 通过ReasoningEvent发送思考内容
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await eventHelper.ReasoningEvent(content, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "[ExtractThinkingContent] 发送ReasoningEvent失败: {error}", ex.Message);
                            }
                        });

                        return content;
                    }
                    _logger.LogDebug("[ExtractThinkingContent] delta节点存在但未找到reasoning_content");
                }

                // 2. 尝试Qwen原生模式的message路径
                var message = firstChoice["message"];
                if (message != null)
                {
                    var messageReasoningContent = message["reasoning_content"];
                    if (messageReasoningContent != null && !string.IsNullOrEmpty(messageReasoningContent.ToString()))
                    {
                        var content = messageReasoningContent.ToString();
                        _logger.LogInformation("[ExtractThinkingContent] 通过message路径提取到思考内容: {content}", content);

                        // 通过ReasoningEvent发送思考内容
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await eventHelper.ReasoningEvent(content, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "[ExtractThinkingContent] 发送ReasoningEvent失败: {error}", ex.Message);
                            }
                        });

                        return content;
                    }
                    _logger.LogDebug("[ExtractThinkingContent] message节点存在但未找到reasoning_content");
                }

                // 3. 如果都没找到，记录详细信息
                _logger.LogDebug("[ExtractThinkingContent] 未找到reasoning_content，firstChoice包含的属性: {properties}",
                    string.Join(", ", firstChoice.AsObject().Select(kv => kv.Key)));

                if (delta != null)
                {
                    _logger.LogDebug("[ExtractThinkingContent] delta节点包含的属性: {properties}",
                        string.Join(", ", delta.AsObject().Select(kv => kv.Key)));
                }

                if (message != null)
                {
                    _logger.LogDebug("[ExtractThinkingContent] message节点包含的属性: {properties}",
                        string.Join(", ", message.AsObject().Select(kv => kv.Key)));
                }

                return "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ExtractThinkingContent] 提取思考内容时出错: {error}, ModelId: {modelId}", ex.Message, modelId);
                return "";
            }
        }
        
        
        public async Task<TemplateDto> TemplateRenderAsync(string promptTemplateText, KernelArguments? arguments,
            Kernel? kernel = null)
        {
            kernel = kernel ?? _kernel;
            var promptTemplateFactory = new KernelPromptTemplateFactory();
            var promptTemplateConfig = new PromptTemplateConfig(promptTemplateText)
            {
                //AllowUnsafeContent = true
            };

            var promptTemplate = promptTemplateFactory.Create(promptTemplateConfig);
            if (kernel == null) throw new ArgumentNullException(nameof(kernel));
            var template = new TemplateDto
            {
                Content = await promptTemplate.RenderAsync(kernel, arguments),
                Inputs = promptTemplateConfig.InputVariables.Select(x => new ParamDto
                {
                    Code = x.Name,
                    LiteralValue = arguments?.GetValueOrDefault(x.Name)?.ToString()
                }).ToList()
            };
            template.Content = HttpUtility.HtmlDecode(template.Content);
            return await Task.FromResult(template);
        }
        
        /// <summary>
        /// 验证所有工具名称长度，记录超过64个字符的工具名称
        /// </summary>
        /// <param name="kernel">内核实例</param>
        public void ValidateToolNameLengths(Kernel kernel)
        {
            const int maxToolNameLength = 64;
            var toolsWithLongNames = new List<string>();
            var totalTools = 0;

            foreach (var plugin in kernel.Plugins)
            {
                foreach (var function in plugin)
                {
                    totalTools++;
                    if (function.Name.Length > maxToolNameLength)
                    {
                        toolsWithLongNames.Add($"{plugin.Name}.{function.Name} (长度: {function.Name.Length})");
                        _logger.LogWarning("[ValidateToolNameLengths] 工具名称过长: {pluginName}.{functionName}, 长度: {length}, 最大建议: {maxLength}",
                            plugin.Name, function.Name, function.Name.Length, maxToolNameLength);
                    }
                }
            }

            if (toolsWithLongNames.Any())
            {
                var warningMessage = $"发现 {toolsWithLongNames.Count} 个工具名称超过 {maxToolNameLength} 字符建议长度:\n" +
                                     string.Join("\n", toolsWithLongNames);
                _logger.LogWarning("[ValidateToolNameLengths] {warningMessage}", warningMessage);
                _logger.LogInformation("[ValidateToolNameLengths] 注意：过长的工具名称可能会被某些AI模型截断或处理不当");
            }
            else
            {
                _logger.LogInformation("[ValidateToolNameLengths] 所有工具名称长度都在建议范围内");
            }

            _logger.LogInformation("[ValidateToolNameLengths] 工具名称长度验证完成，共 {pluginCount} 个插件，{totalTools} 个工具，其中 {longNameCount} 个名称过长",
                kernel.Plugins.Count, totalTools, toolsWithLongNames.Count);
        }

        /// <summary>
        /// 批量查询知识库的TypeEnum信息
        /// </summary>
        /// <param name="knowledgeCodes">知识库代码数组</param>
        /// <returns>知识库代码到TypeEnum的映射</returns>
        public async Task<Dictionary<string, int>> GetKnowledgeTypeMapAsync(string[] knowledgeCodes)
        {
            try
            {
                if (knowledgeCodes == null || knowledgeCodes.Length == 0)
                {
                    return new Dictionary<string, int>();
                }

                if (_knowledgeRepository == null)
                {
                    _logger.LogError("[GetKnowledgeTypeMapAsync] KnowledgeRepository未初始化");
                    return new Dictionary<string, int>();
                }

                _logger.LogInformation("[GetKnowledgeTypeMapAsync] 批量查询 {count} 个知识库的TypeEnum信息", knowledgeCodes.Length);

                // 批量查询所有知识库实体
                var knowledgeEntities = await _knowledgeRepository.GetListAsync(k => knowledgeCodes.Contains(k.Code));

                // 构建代码到TypeEnum的映射
                var typeMap = knowledgeEntities.ToDictionary(k => k.Code, k => k.TypeEnum);

                _logger.LogInformation("[GetKnowledgeTypeMapAsync] 成功查询到 {found} 个知识库的TypeEnum信息，缺失 {missing} 个",
                    typeMap.Count, knowledgeCodes.Length - typeMap.Count);

                // 记录缺失的知识库
                var missingCodes = knowledgeCodes.Except(typeMap.Keys).ToList();
                if (missingCodes.Any())
                {
                    _logger.LogWarning("[GetKnowledgeTypeMapAsync] 未找到以下知识库: {missingCodes}", string.Join(", ", missingCodes));
                }

                return typeMap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetKnowledgeTypeMapAsync] 批量查询知识库TypeEnum时发生异常: {message}", ex.Message);
                return new Dictionary<string, int>();
            }
        }

    }
}

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{
    /// <summary>
    /// MCP工具导入器，负责根据Agent配置导入MCP工具到Kernel
    /// </summary>
    public class McpToolsImporter
    {
        private readonly Kernel _kernel;
        private readonly ILogger<McpToolsImporter> _logger;
        private readonly McpCustomService _mcpCustomService;
        private readonly McpServiceRepository _mcpServiceRepository;
        private readonly McpServiceToolRepository _mcpServiceToolRepository;
        private readonly IServiceProvider _serviceProvider;

        public McpToolsImporter(Kernel kernel, IServiceProvider serviceProvider)
        {
            _kernel = kernel;
            _logger = serviceProvider.GetService<ILoggerFactory>().CreateLogger<McpToolsImporter>();
            _mcpCustomService = serviceProvider.GetService<McpCustomService>() ??
                               throw new ArgumentNullException(nameof(McpCustomService));
            _mcpServiceRepository = serviceProvider.GetService<McpServiceRepository>() ??
                                   throw new ArgumentNullException(nameof(McpServiceRepository));
            _mcpServiceToolRepository = serviceProvider.GetService<McpServiceToolRepository>() ??
                                       throw new ArgumentNullException(nameof(McpServiceToolRepository));
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// 导入MCP工具到Kernel
        /// </summary>
        /// <param name="mcpConfigs">MCP配置列表</param>
        /// <returns></returns>
        public async Task ImportMcpTools(List<AgentMcpDto> mcpConfigs, ConcurrentDictionary<string, string> toolTitleMapping = null)
        {
            try
            {
                _logger.LogInformation("[ImportMcpTools] 开始导入MCP工具，配置数量: {count}", mcpConfigs?.Count ?? 0);

                if (mcpConfigs == null || !mcpConfigs.Any())
                {
                    _logger.LogInformation("[ImportMcpTools] 没有MCP工具配置需要导入");
                    return;
                }

                _logger.LogInformation("[ImportMcpTools] MCP工具详细配置: {@mcpConfigs}", mcpConfigs);

                // 按ServiceGUID分组，每个服务单独处理
                var serviceGroups = mcpConfigs.GroupBy(t => t.ServiceGUID);

                foreach (var serviceGroup in serviceGroups)
                {
                    var serviceGuid = serviceGroup.Key;
                    var toolConfigs = serviceGroup.ToList();

                    _logger.LogInformation("[ImportMcpTools] 处理服务 {serviceGuid}，包含 {toolCount} 个工具配置",
                        serviceGuid, toolConfigs.Count);

                    await ProcessServiceTools(serviceGuid, toolConfigs, toolTitleMapping);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ImportMcpTools] 导入MCP工具时发生异常: {message}", ex.Message);
            }
        }

        /// <summary>
        /// 处理单个服务的工具
        /// </summary>
        /// <param name="serviceGuid">服务GUID</param>
        /// <param name="toolConfigs">工具配置列表</param>
        /// <returns></returns>
        private async Task ProcessServiceTools(string serviceGuid, List<AgentMcpDto> toolConfigs, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            try
            {
                // 从数据库获取服务配置
                _logger.LogInformation("[ProcessServiceTools] 查询服务配置，ServiceGUID: {serviceGuid}", serviceGuid);
                var mcpService = await _mcpServiceRepository.GetAsync(s => s.ServiceGUID == serviceGuid);
                if (mcpService == null)
                {
                    _logger.LogWarning("[ProcessServiceTools] 未找到服务配置，ServiceGUID: {serviceGuid}", serviceGuid);
                    return;
                }

                _logger.LogInformation("[ProcessServiceTools] 获取到服务配置: ServiceName={serviceName}, ServiceURL={serviceUrl}, ServiceCode={serviceCode}, TimeoutSeconds={timeoutSeconds}",
                    mcpService.ServiceName, mcpService.ServiceURL, mcpService.ServiceCode, mcpService.TimeoutSeconds);

                // 设置自定义请求头
                await SetCustomHeaders(mcpService);

                // 设置超时配置
                await SetTimeoutConfiguration(mcpService);

                // 判断是否需要过滤工具
                var needFilterTools = toolConfigs.Any(t => !string.IsNullOrEmpty(t.ToolGUID));

                if (needFilterTools)
                {
                    // 有具体的toolGUID，只注入指定的工具
                    await LoadSpecificTools(mcpService, toolConfigs, toolTitleMapping);
                }
                else
                {
                    // 没有具体的toolGUID，注入全部工具
                    await LoadAllTools(mcpService, toolTitleMapping);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessServiceTools] 处理服务工具时发生异常，ServiceGUID: {serviceGuid}, 错误信息: {message}",
                    serviceGuid, ex.Message);
            }
        }

        /// <summary>
        /// 设置自定义请求头
        /// </summary>
        /// <param name="mcpService">MCP服务配置</param>
        /// <returns></returns>
        private async Task SetCustomHeaders(McpServiceEntity mcpService)
        {
            try
            {
                if (!string.IsNullOrEmpty(mcpService.CustomHeaders))
                {
                    var customHeaders = ParseCustomHeaders(mcpService.CustomHeaders);
                    _mcpCustomService.SetCustomHeaders(customHeaders);
                    _logger.LogInformation("[SetCustomHeaders] 为服务 {serviceGuid} 设置了 {count} 个自定义请求头",
                        mcpService.ServiceGUID, customHeaders.Count);
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[SetCustomHeaders] 解析自定义请求头失败，ServiceGUID: {serviceGuid}", mcpService.ServiceGUID);
            }
        }

        /// <summary>
        /// 设置超时配置
        /// </summary>
        /// <param name="mcpService">MCP服务配置</param>
        /// <returns></returns>
        private async Task SetTimeoutConfiguration(McpServiceEntity mcpService)
        {
            try
            {
                // 从数据库获取超时设置，应用到McpCustomService
                var timeoutSeconds = mcpService.TimeoutSeconds > 0 ? mcpService.TimeoutSeconds : 30;

                // 设置超时配置到McpCustomService
                _mcpCustomService.SetTimeout(timeoutSeconds);

                _logger.LogInformation("[SetTimeoutConfiguration] 服务 {serviceGuid} 超时设置: {timeoutSeconds}秒",
                    mcpService.ServiceGUID, timeoutSeconds);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[SetTimeoutConfiguration] 设置超时配置失败，ServiceGUID: {serviceGuid}", mcpService.ServiceGUID);
            }
        }

        /// <summary>
        /// 加载指定的工具
        /// </summary>
        /// <param name="mcpService">MCP服务配置</param>
        /// <param name="toolConfigs">工具配置列表</param>
        /// <returns></returns>
        private async Task LoadSpecificTools(McpServiceEntity mcpService, List<AgentMcpDto> toolConfigs, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            try
            {
                _logger.LogInformation("[LoadSpecificTools] 开始加载指定工具，服务: {serviceCode}", mcpService.ServiceCode);

                // 获取指定的toolGUID列表
                var toolGuids = toolConfigs.Where(t => !string.IsNullOrEmpty(t.ToolGUID))
                                          .Select(t => t.ToolGUID)
                                          .Distinct()
                                          .ToList();

                _logger.LogInformation("[LoadSpecificTools] 需要加载的工具GUID: {@toolGuids}", toolGuids);

                // 从数据库查询指定的工具信息
                var dbTools = await _mcpServiceToolRepository.GetListAsync(t => 
                    t.ServiceGUID == mcpService.ServiceGUID && 
                    toolGuids.Contains(t.ToolGUID) && 
                    t.Status == 1);

                if (!dbTools.Any())
                {
                    _logger.LogWarning("[LoadSpecificTools] 未找到指定的工具记录，ServiceGUID: {serviceGuid}", mcpService.ServiceGUID);
                    return;
                }

                _logger.LogInformation("[LoadSpecificTools] 从数据库获取到 {count} 个工具记录", dbTools.Count);

                // 使用McpCustomService加载指定的工具
                var toolsLoaded = await _mcpCustomService.LoadSpecificMcpToolsToKernelAsync(
                    _kernel, 
                    mcpService.ServiceURL, 
                    mcpService.ServiceCode, 
                    dbTools.Select(t => t.ToolName).ToList());

                if (toolsLoaded)
                {
                    _logger.LogInformation("[LoadSpecificTools] 成功为服务 {serviceGuid} 加载指定的MCP工具到Kernel", mcpService.ServiceGUID);

                    // 添加工具标题映射，使用服务名称+工具标题作为显示标题
                    if (toolTitleMapping != null)
                    {
                        foreach (var dbTool in dbTools)
                        {
                            string toolKey = $"{mcpService.ServiceCode}.{dbTool.ToolName}";
                            string serviceName = !string.IsNullOrEmpty(mcpService.ServiceName) ? mcpService.ServiceName : mcpService.ServiceCode;
                            string toolTitle = !string.IsNullOrEmpty(dbTool.ToolTitle) ? dbTool.ToolTitle : dbTool.ToolName;
                            string displayTitle = $"{serviceName}{toolTitle}";
                            toolTitleMapping.TryAdd(toolKey, displayTitle);
                            _logger.LogInformation("[LoadSpecificTools] 添加MCP工具标题映射: {toolKey} -> {title}", toolKey, displayTitle);
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("[LoadSpecificTools] 为服务 {serviceGuid} 加载指定的MCP工具失败", mcpService.ServiceGUID);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[LoadSpecificTools] 加载指定工具时发生异常: {message}", ex.Message);
            }
        }

        /// <summary>
        /// 加载全部工具
        /// </summary>
        /// <param name="mcpService">MCP服务配置</param>
        /// <returns></returns>
        private async Task LoadAllTools(McpServiceEntity mcpService, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            try
            {
                _logger.LogInformation("[LoadAllTools] 开始加载全部工具，服务: {serviceCode}", mcpService.ServiceCode);

                // 使用McpCustomService加载全部MCP工具到Kernel
                var toolsLoaded = await _mcpCustomService.LoadMcpToolsToKernelAsync(
                    _kernel, 
                    mcpService.ServiceURL, 
                    mcpService.ServiceCode);

                if (toolsLoaded)
                {
                    _logger.LogInformation("[LoadAllTools] 成功为服务 {serviceGuid} 加载全部MCP工具到Kernel", mcpService.ServiceGUID);

                    // 添加工具标题映射，使用服务名称+工具标题作为显示标题
                    if (toolTitleMapping != null)
                    {
                        try
                        {
                            // 获取该服务的所有工具名称，为每个工具添加服务名称+工具标题的映射
                            var allDbTools = await _mcpServiceToolRepository.GetListAsync(x => x.ServiceGUID == mcpService.ServiceGUID);
                            string serviceName = !string.IsNullOrEmpty(mcpService.ServiceName) ? mcpService.ServiceName : mcpService.ServiceCode;

                            foreach (var dbTool in allDbTools)
                            {
                                string toolKey = $"{mcpService.ServiceCode}.{dbTool.ToolName}";
                                string toolTitle = !string.IsNullOrEmpty(dbTool.ToolTitle) ? dbTool.ToolTitle : dbTool.ToolName;
                                string displayTitle = $"{serviceName}{toolTitle}";
                                toolTitleMapping.TryAdd(toolKey, displayTitle);
                                _logger.LogInformation("[LoadAllTools] 添加MCP工具标题映射: {toolKey} -> {title}", toolKey, displayTitle);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "[LoadAllTools] 查询工具标题映射时发生异常: {message}", ex.Message);
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("[LoadAllTools] 为服务 {serviceGuid} 加载全部MCP工具失败", mcpService.ServiceGUID);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[LoadAllTools] 加载全部工具时发生异常: {message}", ex.Message);
            }
        }

        /// <summary>
        /// 解析自定义请求头
        /// 支持两种格式：
        /// 1. JSON格式：{"Content-Type": "application/json", "Authorization": "Bearer token"}
        /// 2. 多行文本格式：
        ///    "Content-Type": "application/json"
        ///    "Authorization": "Bearer your-token"
        /// </summary>
        /// <param name="customHeadersText">自定义请求头文本</param>
        /// <returns>解析后的请求头字典</returns>
        private Dictionary<string, string> ParseCustomHeaders(string customHeadersText)
        {
            var headers = new Dictionary<string, string>();

            if (string.IsNullOrWhiteSpace(customHeadersText))
            {
                return headers;
            }

            // 首先尝试JSON格式解析
            try
            {
                var jsonHeaders = JsonConvert.DeserializeObject<Dictionary<string, string>>(customHeadersText);
                if (jsonHeaders != null)
                {
                    _logger.LogInformation("[ParseCustomHeaders] 使用JSON格式解析自定义请求头");
                    return jsonHeaders;
                }
            }
            catch
            {
                // JSON解析失败，尝试多行文本格式
                _logger.LogInformation("[ParseCustomHeaders] JSON格式解析失败，尝试多行文本格式");
            }

            // 多行文本格式解析
            var lines = customHeadersText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (string.IsNullOrEmpty(trimmedLine))
                    continue;

                // 查找冒号分隔符
                var colonIndex = trimmedLine.IndexOf(':');
                if (colonIndex > 0 && colonIndex < trimmedLine.Length - 1)
                {
                    var key = trimmedLine.Substring(0, colonIndex).Trim().Trim('"');
                    var value = trimmedLine.Substring(colonIndex + 1).Trim().Trim('"');

                    if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(value))
                    {
                        headers[key] = value;
                        _logger.LogDebug("[ParseCustomHeaders] 解析请求头: {key} = {value}", key, value);
                    }
                }
            }

            _logger.LogInformation("[ParseCustomHeaders] 使用多行文本格式解析自定义请求头，共解析 {count} 个", headers.Count);
            return headers;
        }
    }
}

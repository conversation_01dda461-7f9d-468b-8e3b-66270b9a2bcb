using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{
    public class KnowledgeToolsImporter
    {
        private readonly Kernel _kernel;
        private readonly ILogger<KnowledgeToolsImporter> _logger;
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
        private readonly AgentSkillEventHelper _eventHelper;

        /// <summary>
        /// 最小相关性分数，用于过滤查询结果
        /// </summary>
        public double MinRelevanceScore { get; set; } = 0.1;

        public KnowledgeToolsImporter(Kernel kernel, IServiceProvider serviceProvider)
        {
            _kernel = kernel;
            _logger = serviceProvider.GetService<ILoggerFactory>().CreateLogger<KnowledgeToolsImporter>();
            _knowledgeDomainService = serviceProvider.GetService<IKnowledgeDomainService>();
            _knowledgeFileSectionRepository = serviceProvider.GetService<KnowledgeFileSectionRepository>();
            _eventHelper = new AgentSkillEventHelper(serviceProvider.GetService<IHttpContextAccessor>());
        }

        public KnowledgeToolsImporter(Kernel kernel, IServiceProvider serviceProvider, double minRelevanceScore) : this(kernel, serviceProvider)
        {
            MinRelevanceScore = minRelevanceScore;
        }

        public async Task ImportKnowledgeTools(List<AgentKnowledgeDto> knowledges, ConcurrentDictionary<string, string> toolTitleMapping = null)
        {
            if (knowledges == null || !knowledges.Any())
            {
                _logger.LogInformation("[ImportKnowledgeTools] 没有知识库需要导入");
                return;
            }

            // 根据id去重，组装成集合
            var uniqueKnowledges = knowledges
                .GroupBy(k => k.Id)
                .Select(g => g.First())
                .ToList();

            var functions = new List<KernelFunction>();
            foreach (var knowledge in uniqueKnowledges)
            {
                _logger.LogInformation("[ImportKnowledgeTools] 导入知识库工具: id={id}, code={code}, name={name}",
                    knowledge.Id, knowledge.Code, knowledge.Name);

                // 生成符合ASCII规范的函数名，使用知识库Code而不是Name
                string functionName = $"Query{SanitizeFunctionName(knowledge.Code)}Knowledge";
                string description = BuildKnowledgeToolDescription(knowledge);

                // 确保工具名称不超过64个字符（考虑插件名长度）
                var truncatedName = ToolNameHelper.TruncateFunctionName(functionName, "KnowledgeTools");
                if (truncatedName != functionName)
                {
                    _logger.LogWarning("[ImportKnowledgeTools] 工具名称过长，已截断: {originalName} -> {truncatedName}",
                        functionName, truncatedName);
                }

                var parameters = new List<KernelParameterMetadata>
                {
                    new KernelParameterMetadata("question")
                    {
                        Description = $"要查询的具体问题。请根据【{knowledge.Name}】知识库的内容范围提出相关问题，系统会自动检索最匹配的知识片段"
                    },
                    new KernelParameterMetadata("topk")
                    {
                        Description = "返回结果数量。指定需要返回多少条相关知识片段，取值范围1-20，默认5条。数量越多信息越全面但可能包含相关性较低的内容",
                        DefaultValue = "5"
                    }
                };

                functions.Add(KernelFunctionFactory.CreateFromMethod(
                    method: (Func<KernelArguments, CancellationToken, Task<string>>)QueryKnowledgeFunc,
                    functionName: truncatedName,
                    description: description,
                    parameters: parameters
                ));

                // 添加工具标题映射，使用knowledge.Name作为显示标题
                if (toolTitleMapping != null)
                {
                    string toolKey = $"KnowledgeTools.{truncatedName}";
                    toolTitleMapping.TryAdd(toolKey, knowledge.Name);
                    _logger.LogInformation("[ImportKnowledgeTools] 添加工具标题映射: {toolKey} -> {title}", toolKey, knowledge.Name);
                }

                continue;

                async Task<string> QueryKnowledgeFunc(KernelArguments arguments, CancellationToken ct)
                {
                    var question = arguments.TryGetValue("question", out var q) ? q?.ToString() : "";
                    var topkStr = arguments.TryGetValue("topk", out var tk) ? tk?.ToString() : "5";

                    // 解析topk参数，默认为5
                    if (!int.TryParse(topkStr, out var topk) || topk <= 0)
                    {
                        topk = 5;
                    }

                    return await QueryKnowledge(knowledge.Code, question, topk, ct);
                }
            }

            var plugin = KernelPluginFactory.CreateFromFunctions("KnowledgeTools", null, functions);
            _kernel.Plugins.Add(plugin);
        }

        /// <summary>
        /// 构建知识库工具的详细描述，确保大模型能够准确理解和使用
        /// </summary>
        /// <param name="knowledge">知识库信息</param>
        /// <returns>优化后的工具描述</returns>
        private static string BuildKnowledgeToolDescription(AgentKnowledgeDto knowledge)
        {
            var descriptionBuilder = new StringBuilder();

            // 基础工具说明
            descriptionBuilder.Append($"【{knowledge.Name}】专用知识库问答工具");

            // 如果有详细描述，添加知识库的具体内容和适用场景
            if (!string.IsNullOrWhiteSpace(knowledge.Description))
            {
                // 清理描述中的换行符和多余空格
                var cleanDescription = knowledge.Description.Replace("\r\n", " ").Replace("\n", " ").Replace("\r", " ");
                cleanDescription = Regex.Replace(cleanDescription, @"\s+", " ").Trim();

                descriptionBuilder.Append($"。知识库内容：{cleanDescription}");
            }

            // 添加使用说明
            descriptionBuilder.Append("。");
            descriptionBuilder.Append("使用此工具可以查询该知识库中的相关信息，");
            descriptionBuilder.Append("系统会自动将问题向量化后检索最相关的知识片段，");
            descriptionBuilder.Append("并按相似度排序返回结果。");

            // 添加适用场景提示
            descriptionBuilder.Append("适用于需要获取该领域专业知识、政策解读、操作指南等场景。");

            // 添加参数说明
            descriptionBuilder.Append("参数说明：question为具体问题，topk为返回结果数量（默认5条，最多20条）。");

            return descriptionBuilder.ToString();
        }

        /// <summary>
        /// 清理函数名称，确保只包含ASCII字母、数字和下划线
        /// </summary>
        /// <param name="name">原始名称</param>
        /// <returns>清理后的名称</returns>
        private static string SanitizeFunctionName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return "Unknown";

            var result = new StringBuilder();
            foreach (char c in name)
            {
                if (IsAsciiLetterOrDigit(c) || c == '_')
                {
                    result.Append(c);
                }
                else if (char.IsWhiteSpace(c) || c == '-')
                {
                    // 将空格和连字符转换为下划线
                    result.Append('_');
                }
                // 其他字符（包括中文）直接忽略
            }

            var sanitized = result.ToString();

            // 确保不以数字开头
            if (sanitized.Length > 0 && char.IsDigit(sanitized[0]))
            {
                sanitized = "KB_" + sanitized;
            }

            // 如果结果为空或只有下划线，使用默认名称
            if (string.IsNullOrEmpty(sanitized) || sanitized.All(c => c == '_'))
            {
                sanitized = "Knowledge";
            }

            return sanitized;
        }

        /// <summary>
        /// 检查字符是否为ASCII字母或数字（兼容.NET Core 3.1）
        /// </summary>
        /// <param name="c">要检查的字符</param>
        /// <returns>如果是ASCII字母或数字返回true</returns>
        private static bool IsAsciiLetterOrDigit(char c)
        {
            return (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || (c >= '0' && c <= '9');
        }



        private async Task<string> QueryKnowledge(string knowledgeCode, string question, int topk = 5, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("[QueryKnowledge] 查询知识库: knowledgeCode={knowledgeCode}, question={question}, topk={topk}",
                knowledgeCode, question, topk);

            if (string.IsNullOrWhiteSpace(knowledgeCode) || string.IsNullOrWhiteSpace(question))
                return "参数无效";

            // 限制topk的范围，避免返回过多结果
            if (topk <= 0) topk = 5;
            if (topk > 20) topk = 20;

            try
            {
                // 查询知识库，使用配置的topk值和匹配分数
                var topResults = await _knowledgeDomainService.GetQueryTopResult(knowledgeCode, question, topk, MinRelevanceScore);
                if (topResults == null || topResults.Count == 0)
                    return "未检索到相关知识片段";

                // 获取所有匹配的片段
                var totalIdList = topResults.Select(x => x.id).ToList();
                var sectionEntities = await _knowledgeFileSectionRepository.GetListAsync(x => totalIdList.Contains(x.KnowledgeFileSectionGUID) && x.Disable == 0);

                if (sectionEntities == null || !sectionEntities.Any())
                {
                    return "未找到有效的知识片段";
                }

                // 按照查询结果的顺序组织返回内容
                var resultContents = new List<string>();
                foreach (var result in topResults)
                {
                    var sectionEntity = sectionEntities.FirstOrDefault(s => s.KnowledgeFileSectionGUID == result.id);
                    if (sectionEntity != null && !string.IsNullOrWhiteSpace(sectionEntity.Content))
                    {
                        // 构建完整的内容，包含标题和内容
                        var content = sectionEntity.Content;
                        if (!string.IsNullOrWhiteSpace(sectionEntity.ParagraphTitle))
                        {
                            content = $"{sectionEntity.ParagraphTitle}\n{content}";
                        }

                        // 添加相似度信息和内容
                        resultContents.Add($"[相似度: {result.score:F3}]\n{content}");
                    }
                }

                if (!resultContents.Any())
                {
                    return "未找到有效的知识片段内容";
                }

                // 返回格式化的结果
                return $"检索到 {resultContents.Count} 条相关知识片段：\n\n" +
                       string.Join("\n\n---\n\n", resultContents);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("collection not found"))
                {
                    return $"知识库不存在: {knowledgeCode}";
                }
                _logger.LogError(ex, "[QueryKnowledge] 知识库检索异常: knowledgeCode={knowledgeCode}", knowledgeCode);
                return $"知识库检索异常: {ex.Message}";
            }
        }
    }
}
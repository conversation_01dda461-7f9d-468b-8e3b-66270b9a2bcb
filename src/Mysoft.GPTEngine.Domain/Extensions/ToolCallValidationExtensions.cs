using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// 工具调用验证扩展方法
    /// 提供统一的工具调用序列验证和修复功能
    /// </summary>
    public static class ToolCallValidationExtensions
    {
        /// <summary>
        /// 验证和修复ChatHistory中的工具调用序列
        /// </summary>
        /// <param name="chatHistory">要验证的ChatHistory</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>修复后的ChatHistory</returns>
        public static ChatHistory ValidateAndFixToolCalls(this ChatHistory chatHistory, ILogger logger)
        {
            const string methodName = nameof(ValidateAndFixToolCalls);
            
            try
            {
                logger.LogInformation("[{Method}] 开始验证ChatHistory工具调用序列，消息数: {Count}", methodName, chatHistory.Count);

                var validatedHistory = new ChatHistory();

                for (int i = 0; i < chatHistory.Count; i++)
                {
                    var message = chatHistory[i];
                    bool shouldAddMessage = true;

                    // 检查Tool消息是否有对应的tool_calls
                    if (message.Role == AuthorRole.Tool)
                    {
                        // 检查前面是否有Assistant消息包含tool_calls
                        bool hasPrecedingToolCall = false;
                        for (int j = validatedHistory.Count - 1; j >= 0; j--)
                        {
                            var prevMessage = validatedHistory[j];
                            if (prevMessage.Role == AuthorRole.Assistant && prevMessage.Items != null)
                            {
                                var toolCalls = prevMessage.Items.Where(item => item is FunctionCallContent).Cast<FunctionCallContent>().ToList();
                                if (toolCalls.Any())
                                {
                                    hasPrecedingToolCall = true;
                                    break;
                                }
                            }
                            else if (prevMessage.Role == AuthorRole.User)
                            {
                                // 如果遇到用户消息，停止查找
                                break;
                            }
                        }

                        if (!hasPrecedingToolCall)
                        {
                            logger.LogWarning("[{Method}] 跳过孤立的Tool消息（索引 {Index}），没有对应的tool_calls", methodName, i);
                            shouldAddMessage = false;
                        }
                    }
                    // 检查Assistant消息中的工具调用
                    else if (message.Role == AuthorRole.Assistant && message.Items != null && message.Items.Any())
                    {
                        var toolCalls = message.Items.Where(item => item is FunctionCallContent).Cast<FunctionCallContent>().ToList();
                        if (toolCalls.Any())
                        {
                            logger.LogDebug("[{Method}] 发现Assistant消息包含 {Count} 个工具调用", methodName, toolCalls.Count);

                            // 检查后续是否有对应的工具返回
                            var hasAllResponses = true;
                            foreach (var toolCall in toolCalls)
                            {
                                bool hasResponse = false;
                                // 在后续消息中查找对应的工具返回
                                for (int j = i + 1; j < chatHistory.Count; j++)
                                {
                                    var nextMessage = chatHistory[j];
                                    if (nextMessage.Role == AuthorRole.Tool && nextMessage.Items != null && nextMessage.Items.Any())
                                    {
                                        var toolResults = nextMessage.Items.Where(item => item is FunctionResultContent).Cast<FunctionResultContent>().ToList();
                                        if (toolResults.Any(tr => tr.CallId == toolCall.Id))
                                        {
                                            hasResponse = true;
                                            break;
                                        }
                                    }
                                    // 如果遇到新的用户消息，停止查找
                                    if (nextMessage.Role == AuthorRole.User)
                                    {
                                        break;
                                    }
                                }

                                if (!hasResponse)
                                {
                                    hasAllResponses = false;
                                    logger.LogWarning("[{Method}] 工具调用 {ToolCallId} ({PluginName}.{FunctionName}) 没有找到对应的工具返回", 
                                        methodName, toolCall.Id, toolCall.PluginName, toolCall.FunctionName);
                                }
                            }

                            // 如果有工具调用没有对应的返回，跳过这个消息及其后续的不完整序列
                            if (!hasAllResponses)
                            {
                                logger.LogWarning("[{Method}] 跳过不完整的工具调用序列，从消息索引 {Index} 开始", methodName, i);
                                shouldAddMessage = false;
                                
                                // 跳过后续相关的工具消息，直到遇到用户消息或Assistant的普通回复
                                while (i + 1 < chatHistory.Count)
                                {
                                    var nextMsg = chatHistory[i + 1];
                                    if (nextMsg.Role == AuthorRole.Tool || 
                                        (nextMsg.Role == AuthorRole.Assistant && nextMsg.Items != null && nextMsg.Items.Any() && 
                                         nextMsg.Items.Any(item => item is FunctionCallContent)))
                                    {
                                        i++; // 跳过这个消息
                                        logger.LogDebug("[{Method}] 跳过相关消息，索引: {Index}, 角色: {Role}", methodName, i, nextMsg.Role);
                                    }
                                    else
                                    {
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if (shouldAddMessage)
                    {
                        validatedHistory.Add(message);
                        logger.LogDebug("[{Method}] 保留消息，索引: {Index}, 角色: {Role}", methodName, i, message.Role);
                    }
                }

                logger.LogInformation("[{Method}] 验证完成，原始消息数: {Original}, 修复后消息数: {Validated}", 
                    methodName, chatHistory.Count, validatedHistory.Count);

                return validatedHistory;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{Method}] 验证ChatHistory时发生错误，返回原始ChatHistory", methodName);
                return chatHistory;
            }
        }

        /// <summary>
        /// 验证和修复ChatMessageDto列表中的工具调用配对
        /// </summary>
        /// <param name="messages">原始消息列表</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>修复后的消息列表和工具调用ID映射</returns>
        public static (List<ChatMessageDto> FixedMessages, Dictionary<string, string> ToolCallIdMapping) 
            ValidateAndFixToolCallPairs(this List<ChatMessageDto> messages, ILogger logger)
        {
            const string methodName = nameof(ValidateAndFixToolCallPairs);
            var fixedMessages = new List<ChatMessageDto>();
            var toolCallIdMap = new Dictionary<string, string>(); // 存储工具调用ID映射

            try
            {
                logger.LogInformation("[{Method}] 开始验证 {Count} 条消息的工具调用配对", methodName, messages.Count);

                for (int i = 0; i < messages.Count; i++)
                {
                    var message = messages[i];

                    // 如果是工具调用消息，检查是否有完整的工具调用序列
                    if (message.Role == ChatRoleConstant.Assistant && message.Content.StartsWith("[TOOL_CALL]"))
                    {
                        var toolCallSequence = new List<ChatMessageDto> { message };
                        var hasCompleteSequence = false;
                        var toolCallId = ExtractToolCallIdFromContent(message.Content, logger);

                        // 查找后续的工具返回消息
                        for (int j = i + 1; j < messages.Count; j++)
                        {
                            var nextMessage = messages[j];

                            if (nextMessage.Role == ChatRoleConstant.Tool)
                            {
                                // 为工具返回消息设置正确的ID
                                if (!string.IsNullOrEmpty(toolCallId))
                                {
                                    toolCallIdMap[nextMessage.Content] = toolCallId;
                                }
                                toolCallSequence.Add(nextMessage);
                                // 找到工具返回后，检查是否还有更多工具调用
                                continue;
                            }
                            else if (nextMessage.Role == ChatRoleConstant.Assistant && !nextMessage.Content.StartsWith("[TOOL_CALL]"))
                            {
                                // 找到最终的助手回复，序列完整
                                toolCallSequence.Add(nextMessage);
                                hasCompleteSequence = true;
                                i = j; // 跳过已处理的消息
                                break;
                            }
                            else if (nextMessage.Role == ChatRoleConstant.User)
                            {
                                // 遇到用户消息，序列不完整
                                break;
                            }
                        }

                        if (hasCompleteSequence)
                        {
                            // 添加完整的工具调用序列
                            fixedMessages.AddRange(toolCallSequence);
                            logger.LogDebug("[{Method}] 添加完整的工具调用序列，包含 {Count} 条消息", methodName, toolCallSequence.Count);
                        }
                        else
                        {
                            // 跳过不完整的工具调用序列
                            var contentPreview = message.Content.Length > 100 ? message.Content.Substring(0, 100) + "..." : message.Content;
                            logger.LogWarning("[{Method}] 跳过不完整的工具调用序列，起始消息: {Content}", methodName, contentPreview);
                        }
                    }
                    else if (message.Role == ChatRoleConstant.Tool)
                    {
                        // 孤立的工具返回消息，跳过
                        var contentPreview = message.Content.Length > 100 ? message.Content.Substring(0, 100) + "..." : message.Content;
                        logger.LogWarning("[{Method}] 跳过孤立的工具返回消息: {Content}", methodName, contentPreview);
                    }
                    else
                    {
                        // 普通的用户或助手消息
                        fixedMessages.Add(message);
                    }
                }

                logger.LogInformation("[{Method}] 验证完成，原始消息: {Original}, 修复后消息: {Fixed}", methodName, messages.Count, fixedMessages.Count);
                return (fixedMessages, toolCallIdMap);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{Method}] 验证工具调用配对时发生错误", methodName);
                // 发生错误时，返回只包含用户和普通助手消息的安全列表
                var safeMessages = messages.Where(m => m.Role == ChatRoleConstant.User ||
                                                     (m.Role == ChatRoleConstant.Assistant && !m.Content.StartsWith("[TOOL_CALL]")))
                                         .ToList();
                return (safeMessages, new Dictionary<string, string>());
            }
        }

        /// <summary>
        /// 从工具调用内容中提取ID
        /// </summary>
        /// <param name="content">工具调用内容</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>工具调用ID</returns>
        private static string ExtractToolCallIdFromContent(string content, ILogger logger)
        {
            const string methodName = nameof(ExtractToolCallIdFromContent);
            
            try
            {
                if (!content.StartsWith("[TOOL_CALL] "))
                {
                    return null;
                }

                var jsonContent = content.Substring("[TOOL_CALL] ".Length);
                var toolCallData = JsonSerializer.Deserialize<JsonElement>(jsonContent);

                return toolCallData.TryGetProperty("id", out var idElement) ? idElement.GetString() : null;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{Method}] 提取工具调用ID失败: {Content}", methodName, content);
                return null;
            }
        }

        /// <summary>
        /// 验证工具调用序列的完整性
        /// </summary>
        /// <param name="toolCallId">工具调用ID</param>
        /// <param name="subsequentMessages">后续消息列表</param>
        /// <param name="logger">日志记录器</param>
        /// <returns>是否找到对应的工具返回</returns>
        public static bool HasCorrespondingToolResult(string toolCallId, IEnumerable<ChatMessageContent> subsequentMessages, ILogger logger)
        {
            const string methodName = nameof(HasCorrespondingToolResult);
            
            try
            {
                foreach (var message in subsequentMessages)
                {
                    if (message.Role == AuthorRole.Tool && message.Items != null)
                    {
                        var toolResults = message.Items.Where(item => item is FunctionResultContent).Cast<FunctionResultContent>();
                        if (toolResults.Any(tr => tr.CallId == toolCallId))
                        {
                            logger.LogDebug("[{Method}] 找到工具调用 {ToolCallId} 的对应返回", methodName, toolCallId);
                            return true;
                        }
                    }
                    
                    // 如果遇到用户消息，停止查找
                    if (message.Role == AuthorRole.User)
                    {
                        break;
                    }
                }

                logger.LogDebug("[{Method}] 未找到工具调用 {ToolCallId} 的对应返回", methodName, toolCallId);
                return false;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "[{Method}] 验证工具调用 {ToolCallId} 时发生错误", methodName, toolCallId);
                return false;
            }
        }
    }
}

using System;
using Microsoft.Extensions.DependencyInjection;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// HttpClient 扩展方法
    /// </summary>
    public static class HttpClientExtensions
    {
        /// <summary>
        /// 为 HttpClient 添加优化的日志处理器
        /// </summary>
        /// <param name="builder">HttpClient 构建器</param>
        /// <param name="configureOptions">配置选项的委托</param>
        /// <returns>HttpClient 构建器</returns>
        public static IHttpClientBuilder AddOptimizedLogging(this IHttpClientBuilder builder, Action<LoggingHttpHandlerOptions> configureOptions = null)
        {
            var options = new LoggingHttpHandlerOptions();
            configureOptions?.Invoke(options);

            return builder.AddHttpMessageHandler(() => new LoggingHttpHandler(null, options));
        }

        /// <summary>
        /// 为 HttpClient 添加性能优化的日志处理器（默认禁用流式日志）
        /// </summary>
        /// <param name="builder">HttpClient 构建器</param>
        /// <returns>HttpClient 构建器</returns>
        public static IHttpClientBuilder AddPerformanceOptimizedLogging(this IHttpClientBuilder builder)
        {
            var options = new LoggingHttpHandlerOptions
            {
                EnableStreamLogging = false,  // 禁用流式日志以提高性能
                EnableRequestLogging = true,
                EnableResponseLogging = true,
                EnableResponseBodyLogging = false  // 禁用响应体日志以提高性能
            };

            return builder.AddHttpMessageHandler(() => new LoggingHttpHandler(null, options));
        }

        /// <summary>
        /// 为 HttpClient 添加详细的日志处理器（启用所有日志）
        /// </summary>
        /// <param name="builder">HttpClient 构建器</param>
        /// <returns>HttpClient 构建器</returns>
        public static IHttpClientBuilder AddDetailedLogging(this IHttpClientBuilder builder)
        {
            var options = new LoggingHttpHandlerOptions
            {
                EnableStreamLogging = true,
                EnableRequestLogging = true,
                EnableResponseLogging = true,
                EnableResponseBodyLogging = true
            };

            return builder.AddHttpMessageHandler(() => new LoggingHttpHandler(null, options));
        }

        /// <summary>
        /// 为 HttpClient 添加支持思考模式的日志处理器
        /// </summary>
        /// <param name="builder">HttpClient 构建器</param>
        /// <param name="enableThinking">是否启用思考模式</param>
        /// <param name="thinkingBudget">思考预算（可选）</param>
        /// <returns>HttpClient 构建器</returns>
        public static IHttpClientBuilder AddThinkingModeLogging(this IHttpClientBuilder builder, bool enableThinking = true, int? thinkingBudget = null)
        {
            var options = new LoggingHttpHandlerOptions
            {
                EnableStreamLogging = false,  // 默认禁用流式日志以提高性能
                EnableRequestLogging = true,
                EnableResponseLogging = true,
                EnableResponseBodyLogging = false,
                ThinkingMode = new ThinkingModeOptions
                {
                    EnableThinking = enableThinking,
                    ThinkingBudget = thinkingBudget
                }
            };

            return builder.AddHttpMessageHandler(() => new LoggingHttpHandler(null, options));
        }
    }
}

using System.Collections.Generic;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// LoggingHttpHandler 配置选项
    /// </summary>
    public class LoggingHttpHandlerOptions
    {
        /// <summary>
        /// 是否启用详细的流式响应日志记录
        /// 默认为 false，以避免阻塞流式输出
        /// </summary>
        public bool EnableStreamLogging { get; set; } = false;

        /// <summary>
        /// 是否启用请求日志记录
        /// </summary>
        public bool EnableRequestLogging { get; set; } = true;

        /// <summary>
        /// 是否启用响应日志记录
        /// </summary>
        public bool EnableResponseLogging { get; set; } = true;

        /// <summary>
        /// 是否启用非流式响应体日志记录
        /// </summary>
        public bool EnableResponseBodyLogging { get; set; } = true;

        /// <summary>
        /// 日志前缀
        /// </summary>
        public string LogPrefix { get; set; } = "[LoggingHttpHandler]";

        /// <summary>
        /// 思考模式配置
        /// </summary>
        public ThinkingModeOptions ThinkingMode { get; set; } = new ThinkingModeOptions();

        /// <summary>
        /// 是否启用多模态消息格式转换（转换为OpenAI标准格式）
        /// </summary>
        public bool EnableMultimodalSupport { get; set; } = true;
    }

    /// <summary>
    /// 思考模式配置选项
    /// </summary>
    public class ThinkingModeOptions
    {
        /// <summary>
        /// 是否启用思考模式
        /// </summary>
        public bool EnableThinking { get; set; } = false;

        /// <summary>
        /// 思考预算（思考token数量限制）
        /// </summary>
        public int? ThinkingBudget { get; set; } = null;

        /// <summary>
        /// 额外的请求参数
        /// </summary>
        public Dictionary<string, object> ExtraParameters { get; set; } = new Dictionary<string, object>();
    }
}

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// 支持思考模式的HttpClient消息处理器
    /// 可以在OpenAI兼容的API请求中添加思考模式相关参数
    /// </summary>
    public class ThinkingModeHttpHandler : DelegatingHandler
    {
        private readonly ThinkingModeOptions _thinkingOptions;
        private readonly ILogger<ThinkingModeHttpHandler> _logger;

        public ThinkingModeHttpHandler(HttpMessageHandler innerHandler = null, ThinkingModeOptions thinkingOptions = null, ILogger<ThinkingModeHttpHandler> logger = null)
            : base(innerHandler ?? new HttpClientHandler())
        {
            _thinkingOptions = thinkingOptions ?? new ThinkingModeOptions();
            _logger = logger;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            // 只处理POST请求且内容类型为application/json的请求
            if (request.Method == HttpMethod.Post &&
                request.Content != null &&
                request.Content.Headers.ContentType?.MediaType == "application/json")
            {
                await ModifyRequestBodyAsync(request);
            }

            return await base.SendAsync(request, cancellationToken);
        }

        private async Task ModifyRequestBodyAsync(HttpRequestMessage request)
        {
            try
            {
                // 读取原始请求体
                var originalContent = await request.Content.ReadAsStringAsync();

                if (string.IsNullOrEmpty(originalContent))
                    return;

                // 解析JSON
                var jsonDocument = JsonDocument.Parse(originalContent);
                var rootElement = jsonDocument.RootElement;

                // 检查是否是聊天完成请求（包含model和messages字段）
                if (!rootElement.TryGetProperty("model", out _) ||
                    !rootElement.TryGetProperty("messages", out _))
                {
                    Console.WriteLine("[ThinkingModeHttpHandler] 不是聊天完成请求，跳过修改");
                    _logger?.LogInformation("[ThinkingModeHttpHandler] 不是聊天完成请求，跳过修改");
                    return;
                }

                // 创建新的JSON对象
                var modifiedJson = new Dictionary<string, object>();

                // 复制原有属性
                foreach (var property in rootElement.EnumerateObject())
                {
                    modifiedJson[property.Name] = GetJsonValue(property.Value);
                }

                // 检查是否需要添加思考模式参数
                bool hasThinkingParams = false;
                if (_thinkingOptions.EnableThinking)
                {
                    modifiedJson["enable_thinking"] = true;
                    hasThinkingParams = true;
                    Console.WriteLine("[ThinkingModeHttpHandler] 添加思考模式参数: enable_thinking = true");
                    _logger?.LogInformation("[ThinkingModeHttpHandler] 添加思考模式参数: enable_thinking = true");

                    if (_thinkingOptions.ThinkingBudget.HasValue)
                    {
                        modifiedJson["thinking_budget"] = _thinkingOptions.ThinkingBudget.Value;
                        Console.WriteLine($"[ThinkingModeHttpHandler] 添加思考预算参数: thinking_budget = {_thinkingOptions.ThinkingBudget.Value}");
                        _logger?.LogInformation("[ThinkingModeHttpHandler] 添加思考预算参数: thinking_budget = {thinkingBudget}", _thinkingOptions.ThinkingBudget.Value);
                    }
                }

                // 检查是否有额外参数需要添加
                bool hasExtraParams = _thinkingOptions.ExtraParameters?.Count > 0;

                // 记录当前配置状态
                Console.WriteLine($"[ThinkingModeHttpHandler] 配置状态: EnableThinking={_thinkingOptions.EnableThinking}, ThinkingBudget={_thinkingOptions.ThinkingBudget}, ExtraParams={hasExtraParams}");
                _logger?.LogInformation("[ThinkingModeHttpHandler] 配置状态: EnableThinking={enableThinking}, ThinkingBudget={thinkingBudget}, ExtraParams={hasExtraParams}",
                    _thinkingOptions.EnableThinking, _thinkingOptions.ThinkingBudget, hasExtraParams);

                // 添加额外参数
                if (hasExtraParams)
                {
                    foreach (var extraParam in _thinkingOptions.ExtraParameters)
                    {
                        modifiedJson[extraParam.Key] = extraParam.Value;
                        Console.WriteLine($"[ThinkingModeHttpHandler] 添加额外参数: {extraParam.Key} = {extraParam.Value}");
                        _logger?.LogInformation("[ThinkingModeHttpHandler] 添加额外参数: {key} = {value}", extraParam.Key, extraParam.Value);
                    }
                }

                // 只有在实际添加了参数时才重新序列化和更新请求内容
                if (hasThinkingParams || hasExtraParams)
                {
                    // 序列化修改后的JSON，确保中文字符不被转义
                    var modifiedContent = JsonSerializer.Serialize(modifiedJson, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        WriteIndented = false,
                        Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping // 防止中文字符被转义
                    });

                    // 打印修改后的请求体
                    Console.WriteLine($"[ThinkingModeHttpHandler] 修改后请求体: {modifiedContent}");
                    _logger?.LogInformation("[ThinkingModeHttpHandler] 修改后请求体: {modifiedContent}", modifiedContent);

                    // 更新请求内容
                    var mediaType = request.Content.Headers.ContentType?.MediaType ?? "application/json";
                    request.Content = new StringContent(modifiedContent, Encoding.UTF8, mediaType);
                }
                else
                {
                    Console.WriteLine("[ThinkingModeHttpHandler] 没有需要添加的额外参数，保持原始请求体不变");
                    _logger?.LogInformation("[ThinkingModeHttpHandler] 没有需要添加的额外参数，保持原始请求体不变");
                }
            }
            catch (Exception ex)
            {
                // 如果修改失败，记录错误但不影响原始请求
                Console.WriteLine($"[ThinkingModeHttpHandler] Failed to modify request body: {ex.Message}");
                _logger?.LogError(ex, "[ThinkingModeHttpHandler] 修改请求体失败: {message}", ex.Message);
            }
        }

        private object GetJsonValue(JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.String:
                    return element.GetString();
                case JsonValueKind.Number:
                    if (element.TryGetInt32(out var intValue))
                        return intValue;
                    if (element.TryGetInt64(out var longValue))
                        return longValue;
                    return element.GetDouble();
                case JsonValueKind.True:
                    return true;
                case JsonValueKind.False:
                    return false;
                case JsonValueKind.Null:
                    return null;
                case JsonValueKind.Array:
                    var array = new object[element.GetArrayLength()];
                    var index = 0;
                    foreach (var item in element.EnumerateArray())
                    {
                        array[index++] = GetJsonValue(item);
                    }
                    return array;
                case JsonValueKind.Object:
                    var obj = new Dictionary<string, object>();
                    foreach (var property in element.EnumerateObject())
                    {
                        obj[property.Name] = GetJsonValue(property.Value);
                    }
                    return obj;
                default:
                    return element.ToString();
            }
        }
    }
}

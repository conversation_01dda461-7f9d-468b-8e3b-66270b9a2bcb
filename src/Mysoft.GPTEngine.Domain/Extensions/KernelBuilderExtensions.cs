using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Baidu;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Kimi;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Aliyun;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu;
using Mysoft.GPTEngine.SemanticKernel.Core.QianXun;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen;
using Mysoft.GPTEngine.SemanticKernel.Core.Xunfei;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// Provides extension methods for the <see cref="IKernelBuilder"/> class to configure Hugging Face connectors.
    /// </summary>
    public static class KernelBuilderExtensions
    {
        public static async Task AddChatCompletionServices(this IKernelBuilder kernelBuilder, List<ModelInstanceDto> modelInstances,bool useAgent = false)
        {
            await AddChatCompletionServices(kernelBuilder, modelInstances, useAgent, null);
        }

        public static async Task AddChatCompletionServices(this IKernelBuilder kernelBuilder, List<ModelInstanceDto> modelInstances, bool useAgent, ThinkingModeOptions thinkingOptions)
        {
            if (modelInstances?.Count == 0) return;

            var defaultModelInstances = modelInstances.FindAll(x => x.IsDefault);
            if (defaultModelInstances != null && defaultModelInstances.Count > 0)
            {
                foreach (var defaultModelInstance in defaultModelInstances)
                {
                    string chatCompletionType = null;
                    switch (defaultModelInstance.ServiceTypeEnum)
                    {
                        case ServiceTypeEnum.TextGeneration:
                            chatCompletionType = ChatCompletionTypeDto.TextGeneration;
                            break;
                        case ServiceTypeEnum.FileComprehend:
                            chatCompletionType = ChatCompletionTypeDto.FileComprehend;
                            break;
                        case ServiceTypeEnum.ImageComprehend:
                            chatCompletionType = ChatCompletionTypeDto.ImageComprehend;
                            break;
                    }

                    await kernelBuilder.AddChatCompletionService(defaultModelInstance, chatCompletionType, useAgent, thinkingOptions);
                }
            }
            foreach (var modelInstance in modelInstances)
            {
                await kernelBuilder.AddChatCompletionService(modelInstance, modelInstance.InstanceCode, useAgent, thinkingOptions);
            }
        }
        public static async Task AddChatCompletionService(this IKernelBuilder kernelBuilder, ModelInstanceDto modelInstance, string? serviceId, bool useAgent = false)
        {
            await AddChatCompletionService(kernelBuilder, modelInstance, serviceId, useAgent, null);
        }

        /// <summary>
        /// 添加聊天完成服务，支持自定义思考模式配置
        /// </summary>
        /// <param name="kernelBuilder">内核构建器</param>
        /// <param name="modelInstance">模型实例</param>
        /// <param name="serviceId">服务ID</param>
        /// <param name="useAgent">是否使用Agent模式</param>
        /// <param name="thinkingOptions">思考模式配置</param>
        public static async Task AddChatCompletionService(this IKernelBuilder kernelBuilder, ModelInstanceDto modelInstance, string? serviceId, bool useAgent, ThinkingModeOptions thinkingOptions)
        {
            var httpClient = CreateHttpClientWithThinkingMode(modelInstance, thinkingOptions);

            // 当useAgent为true时，统一使用AddOpenAIChatCompletion
            if (useAgent)
            {
#pragma warning disable SKEXP0010 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                Uri endpoint = GetOpenAICompatibleEndpoint(modelInstance);
                if (endpoint != null)
                {
                    kernelBuilder.AddOpenAIChatCompletion(modelId: modelInstance.ModelCode, endpoint, apiKey: modelInstance.ApiKey, serviceId: serviceId, httpClient: httpClient);
                }
                else
                {
                    kernelBuilder.AddOpenAIChatCompletion(modelId: modelInstance.ModelCode, apiKey: modelInstance.ApiKey, serviceId: serviceId, httpClient: httpClient);
                }
#pragma warning restore SKEXP0010 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
            }
            else
            {
                // 当useAgent为false时，使用原有的各种模型特定实现
                switch (modelInstance.ModelType)
                {
                    case ModelTypeEnum.AzureOpenAI:
                        kernelBuilder.AddAzureOpenAIChatCompletion(deploymentName: modelInstance.DeploymentName, modelId: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, apiKey: modelInstance.ApiKey, serviceId: serviceId);
                        break;
                    case ModelTypeEnum.OpenAI:
                        if (!string.IsNullOrEmpty(modelInstance.Endpoint))
                        {
#pragma warning disable SKEXP0010 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                            kernelBuilder.AddOpenAIChatCompletion(modelId: modelInstance.ModelCode, new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId, httpClient: httpClient);
#pragma warning restore SKEXP0010 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                        }
                        else
                        {
                            kernelBuilder.AddOpenAIChatCompletion(modelId: modelInstance.ModelCode, apiKey: modelInstance.ApiKey, serviceId: serviceId, httpClient: httpClient);
                        }
                        break;
                    case ModelTypeEnum.QianXun:
                        kernelBuilder.AddQianXunChatCompletion(model: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                        break;
                    case ModelTypeEnum.Ali:
#pragma warning disable SKEXP0010 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                        // 当不使用Agent时，根据IsSupportTool决定使用模式
                        if (modelInstance.IsSupportTool == 1)
                        {
                            // 支持工具调用时，使用OpenAI兼容模式
                            string compatibleEndpoint = CombineUrlPath(modelInstance.Endpoint, "/compatible-mode/v1");
                            kernelBuilder.AddOpenAIChatCompletion(modelId: modelInstance.ModelCode, new Uri(compatibleEndpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId, httpClient: httpClient);
                        }
                        else
                        {
                            // 默认使用Qwen原生模式
                            kernelBuilder.AddQwenChatCompletion(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                        }
#pragma warning restore SKEXP0010 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                        break;
                    case ModelTypeEnum.Kimi:
                        kernelBuilder.AddKimiChatCompletion(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                        break;
                    case ModelTypeEnum.Xunfei:
                        kernelBuilder.AddXunfeiChatCompletion(model: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                        break;
                    case ModelTypeEnum.Baidu:
                        kernelBuilder.AddBaiduChatCompletion(model: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, clientID: modelInstance.ClientID, serviceId: serviceId);
                        break;
                    default:
                        string modelInstanceString = JsonConvert.SerializeObject(modelInstance);
                        CustomRedirectHttpDto customRedirectHttpDto = JsonConvert.DeserializeObject<CustomRedirectHttpDto>(modelInstanceString);
                        kernelBuilder.AddMysoftChatCompletion(deploymentName: modelInstance.DeploymentName, modelId: modelInstance.ModelCode, apiKey: modelInstance.ApiKey, apiSecret: modelInstance.ClientID, endpoint: new Uri(modelInstance.Endpoint), vendor: modelInstance.Vendor, serviceId: serviceId, httpClient: httpClient);
                        break;
                }
            }
            await Task.CompletedTask;
        }
        public static async Task AddOcrRecognizeServices(this IKernelBuilder kernelBuilder, List<ModelInstanceDto> modelInstances)
        {
            if (modelInstances?.Count == 0) return;

            var defaultModelInstance = modelInstances.FirstOrDefault(x => x.IsDefault);
            if (defaultModelInstance != null)
            {
                string chatCompletionType = null;
                await kernelBuilder.AddOcrRecognizeService(defaultModelInstance, chatCompletionType);
            }
            modelInstances.ForEach(async modelInstance =>
            {
                await kernelBuilder.AddOcrRecognizeService(modelInstance, modelInstance.InstanceCode);
            });

            await Task.CompletedTask;
        }
        public static async Task AddOcrRecognizeService(this IKernelBuilder kernelBuilder, ModelInstanceDto modelInstance, string? serviceId)
        {
            switch (modelInstance.ModelType)
            {
                case ModelTypeEnum.Ali:
                    kernelBuilder.AddAliyunOcrRecognize(model: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, accessKeyId: modelInstance.ClientID, accessSecret: modelInstance.ApiKey, serviceId: serviceId);
                    break;
                case ModelTypeEnum.Baidu:
                    kernelBuilder.AddBaiduOcrRecognize(model: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, accessKeyId: modelInstance.ClientID, accessSecret: modelInstance.ApiKey, serviceId: serviceId);
                    break;
                default:
                    break;
            }
            await Task.CompletedTask;
        }
        
        /**
         * 安全审查
         */
        public static async Task AddContentReviewServices(this IKernelBuilder kernelBuilder, List<ModelInstanceDto> modelInstances)
        {
            if (modelInstances?.Count == 0) return;

            var defaultModelInstance = modelInstances.FirstOrDefault(x => x.IsDefault);
            if (defaultModelInstance != null)
            {
                string chatCompletionType = null;
                await kernelBuilder.AddContentReviewService(defaultModelInstance, chatCompletionType);
            }
            modelInstances.ForEach(async modelInstance =>
            {
                await kernelBuilder.AddContentReviewService(modelInstance, modelInstance.InstanceCode);
            });

            await Task.CompletedTask;
        }
        public static async Task AddContentReviewService(this IKernelBuilder kernelBuilder, ModelInstanceDto modelInstance, string? serviceId)
        {
            switch (modelInstance.ModelType)
            {
                case ModelTypeEnum.Ali:
                    kernelBuilder.AddContentReviewRecognize(model: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, accessKeyId: modelInstance.ClientID, accessSecret: modelInstance.ApiKey, serviceId: serviceId, strategyId: modelInstance.StrategyId);
                    break;
                case ModelTypeEnum.Baidu:
                    kernelBuilder.AddContentReviewRecognize(model: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, accessKeyId: modelInstance.ClientID, accessSecret: modelInstance.ApiKey, serviceId: serviceId, strategyId: modelInstance.StrategyId);
                    break;
                default:
                    break;
            }
            await Task.CompletedTask;
        }

        /// <summary>
        /// 获取OpenAI兼容的端点地址
        /// </summary>
        /// <param name="modelInstance">模型实例</param>
        /// <returns>OpenAI兼容的端点URI，如果不需要端点则返回null</returns>
        private static Uri GetOpenAICompatibleEndpoint(ModelInstanceDto modelInstance)
        {
            if (string.IsNullOrEmpty(modelInstance.Endpoint))
            {
                return null;
            }

            switch (modelInstance.ModelType)
            {
                case ModelTypeEnum.AzureOpenAI:
                    // AzureOpenAI使用特殊的AddAzureOpenAIChatCompletion，这里返回null让其使用默认OpenAI方式
                    return new Uri(modelInstance.Endpoint);
                case ModelTypeEnum.OpenAI:
                    return new Uri(modelInstance.Endpoint);
                case ModelTypeEnum.Ali:
                    // 阿里云模型使用兼容模式端点
                    string compatibleEndpoint = CombineUrlPath(modelInstance.Endpoint, "/compatible-mode/v1");
                    return new Uri(compatibleEndpoint);
                case ModelTypeEnum.QianXun:
                case ModelTypeEnum.Kimi:
                case ModelTypeEnum.Xunfei:
                case ModelTypeEnum.Baidu:
                    // 其他模型直接使用原端点，假设它们支持OpenAI兼容接口
                    return new Uri(modelInstance.Endpoint);
                default:
                    // 自定义模型使用原端点
                    return new Uri(modelInstance.Endpoint);
            }
        }

        /// <summary>
        /// 安全地组合URL路径，避免双斜杠问题
        /// </summary>
        /// <param name="baseUrl">基础URL</param>
        /// <param name="path">要添加的路径</param>
        /// <returns>正确组合的URL</returns>
        private static string CombineUrlPath(string baseUrl, string path)
        {
            if (string.IsNullOrEmpty(baseUrl))
                return path;

            if (string.IsNullOrEmpty(path))
                return baseUrl;

            // 移除baseUrl末尾的斜杠
            baseUrl = baseUrl.TrimEnd('/');

            // 确保path以斜杠开头
            if (!path.StartsWith("/"))
                path = "/" + path;

            return baseUrl + path;
        }

        /// <summary>
        /// 创建支持额外请求参数的HttpClient（包括思考模式）
        /// </summary>
        /// <param name="modelInstance">模型实例</param>
        /// <param name="thinkingOptions">思考模式配置，如果为null则使用默认配置</param>
        /// <returns>配置好的HttpClient</returns>
        private static HttpClient CreateHttpClientWithThinkingMode(ModelInstanceDto modelInstance, ThinkingModeOptions thinkingOptions = null)
        {
            var loggingOptions = new LoggingHttpHandlerOptions();

            // 确保日志记录功能启用
            loggingOptions.EnableRequestLogging = true;
            loggingOptions.EnableResponseLogging = true;
            loggingOptions.EnableResponseBodyLogging = true;
            loggingOptions.EnableStreamLogging = false; // 保持流式日志关闭以避免性能问题
            loggingOptions.EnableMultimodalSupport = true; // 启用多模态消息格式转换

            // 总是设置ThinkingMode配置，即使不启用思考模式也要创建处理器
            if (thinkingOptions != null)
            {
                loggingOptions.ThinkingMode = thinkingOptions;
            }
            else
            {
                // 创建默认的思考模式配置对象（默认不启用）
                loggingOptions.ThinkingMode = new ThinkingModeOptions
                {
                    EnableThinking = false, // 默认不启用，由Agent配置决定
                    ThinkingBudget = null
                };
            }

            // 如果模型支持深度思考且没有传入thinkingOptions，则启用思考模式
            if (modelInstance?.SupportDeepThink == 1 && thinkingOptions == null)
            {
                loggingOptions.ThinkingMode.EnableThinking = true;
                loggingOptions.ThinkingMode.ThinkingBudget = 50;
            }

            var loggingHandler = new LoggingHttpHandler(null, loggingOptions);
            return new HttpClient(loggingHandler);
        }

    }
}

using System;
using System.Collections.Generic;
using System.Text;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// 工具名称处理帮助类
    /// </summary>
    public static class ToolNameHelper
    {
        /// <summary>
        /// 截断工具名称以符合长度限制，考虑插件名和函数名的组合
        /// </summary>
        /// <param name="functionName">函数名称</param>
        /// <param name="pluginName">插件名称</param>
        /// <param name="maxTotalLength">最大总长度，默认64</param>
        /// <returns>截断后的函数名称</returns>
        public static string TruncateFunctionName(string functionName, string pluginName, int maxTotalLength = 64)
        {
            if (string.IsNullOrEmpty(functionName))
                return functionName;

            const int separatorLength = 1; // 插件名和函数名之间的分隔符长度（"-"）
            
            // 计算函数名的最大允许长度（考虑插件名）
            var maxFunctionNameLength = maxTotalLength - (pluginName?.Length ?? 0) - separatorLength;
            
            // 确保函数名至少有3个字符
            maxFunctionNameLength = Math.Max(maxFunctionNameLength, 3);

            if (functionName.Length <= maxFunctionNameLength)
            {
                return functionName;
            }

            // 智能截断：保留重要部分
            return SmartTruncate(functionName, maxFunctionNameLength);
        }

        /// <summary>
        /// 截断工具名称以符合长度限制（兼容旧版本，不考虑插件名）
        /// </summary>
        /// <param name="toolName">工具名称</param>
        /// <param name="maxLength">最大长度，默认64</param>
        /// <returns>截断后的工具名称</returns>
        [Obsolete("请使用 TruncateFunctionName 方法，该方法会考虑插件名长度")]
        public static string TruncateToolName(string toolName, int maxLength = 64)
        {
            if (string.IsNullOrEmpty(toolName) || toolName.Length <= maxLength)
                return toolName;

            // 如果名称太长，截断并添加哈希后缀以确保唯一性
            var truncatedLength = maxLength - 8; // 为哈希后缀预留8个字符
            var truncated = toolName.Substring(0, truncatedLength);
            var hash = Math.Abs(toolName.GetHashCode()).ToString("X").Substring(0, 7); // 7位十六进制哈希
            return $"{truncated}_{hash}";
        }

        /// <summary>
        /// 智能截断工具名称，尽量保留有意义的部分
        /// </summary>
        /// <param name="toolName">原始工具名称</param>
        /// <param name="maxLength">最大长度</param>
        /// <returns>截断后的工具名称</returns>
        private static string SmartTruncate(string toolName, int maxLength)
        {
            if (string.IsNullOrEmpty(toolName) || toolName.Length <= maxLength)
            {
                return toolName;
            }

            // 策略1: 如果包含下划线或连字符，尝试保留最后几个有意义的部分
            var separators = new[] { '_', '-', '.', '/' };
            foreach (var separator in separators)
            {
                if (toolName.Contains(separator))
                {
                    var parts = toolName.Split(separator);
                    if (parts.Length > 1)
                    {
                        // 从后往前组合，直到超过长度限制
                        var result = "";
                        for (int i = parts.Length - 1; i >= 0; i--)
                        {
                            var candidate = i == parts.Length - 1 ? parts[i] : parts[i] + separator + result;
                            if (candidate.Length <= maxLength)
                            {
                                result = candidate;
                            }
                            else
                            {
                                break;
                            }
                        }

                        if (!string.IsNullOrEmpty(result) && result.Length <= maxLength)
                        {
                            return result;
                        }
                    }
                }
            }

            // 策略2: 如果是驼峰命名，尝试保留重要的单词
            if (HasCamelCase(toolName))
            {
                var words = SplitCamelCase(toolName);
                if (words.Count > 1)
                {
                    // 优先保留动词（通常在前面）和最后的名词
                    var importantWords = new List<string>();

                    // 保留第一个单词（通常是动词）
                    if (words.Count > 0) importantWords.Add(words[0]);

                    // 保留最后一个单词（通常是主要名词）
                    if (words.Count > 1 && words[0] != words[words.Count - 1])
                        importantWords.Add(words[words.Count - 1]);

                    // 如果还有空间，添加中间的重要单词
                    for (int i = 1; i < words.Count - 1; i++)
                    {
                        var candidate = string.Join("", importantWords) + words[i];
                        if (candidate.Length <= maxLength)
                        {
                            importantWords.Insert(importantWords.Count - 1, words[i]);
                        }
                        else
                        {
                            break;
                        }
                    }

                    var result = string.Join("", importantWords);
                    if (result.Length <= maxLength)
                    {
                        return result;
                    }
                }
            }

            // 策略3: 简单截断并添加哈希后缀
            var truncatedLength = maxLength - 8; // 为哈希后缀预留8个字符
            if (truncatedLength <= 0)
            {
                // 如果连哈希都放不下，只能强制截断
                return toolName.Substring(0, Math.Max(1, maxLength));
            }

            var truncated = toolName.Substring(0, truncatedLength);
            var hash = Math.Abs(toolName.GetHashCode()).ToString("X").Substring(0, Math.Min(7, maxLength - truncatedLength - 1));
            return $"{truncated}_{hash}";
        }

        /// <summary>
        /// 检查字符串是否包含驼峰命名
        /// </summary>
        private static bool HasCamelCase(string input)
        {
            if (string.IsNullOrEmpty(input) || input.Length < 2)
                return false;

            for (int i = 1; i < input.Length; i++)
            {
                if (char.IsUpper(input[i]) && char.IsLower(input[i - 1]))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 将驼峰命名的字符串分割成单词
        /// </summary>
        private static List<string> SplitCamelCase(string input)
        {
            var words = new List<string>();
            if (string.IsNullOrEmpty(input))
                return words;

            var currentWord = new StringBuilder();
            currentWord.Append(input[0]);

            for (int i = 1; i < input.Length; i++)
            {
                if (char.IsUpper(input[i]) && char.IsLower(input[i - 1]))
                {
                    // 新单词开始
                    words.Add(currentWord.ToString());
                    currentWord.Clear();
                    currentWord.Append(input[i]);
                }
                else
                {
                    currentWord.Append(input[i]);
                }
            }

            if (currentWord.Length > 0)
            {
                words.Add(currentWord.ToString());
            }

            return words;
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;

namespace Mysoft.GPTEngine.Domain.Extensions
{
    /// <summary>
    /// OpenAI多模态消息转换器，用于将Semantic Kernel的消息格式转换为OpenAI标准格式
    /// 这是方案1的核心实现：扩展现有的OpenAI兼容模式
    /// </summary>
    public static class OpenAIMultimodalMessageConverter
    {
        /// <summary>
        /// 将ChatMessageContent转换为OpenAI标准的content格式
        /// 支持 messages=[{"role": "user","content": [{"type": "image_url","image_url": {"url": "..."}},{"type": "text", "text": "..."}]}] 格式
        /// </summary>
        /// <param name="message">Semantic Kernel的ChatMessageContent</param>
        /// <returns>OpenAI标准格式的content对象</returns>
        public static object ConvertToOpenAIFormat(ChatMessageContent message)
        {
            if (message?.Items == null || message.Items.Count == 0)
            {
                return string.Empty;
            }

            // 如果只有一个文本内容，直接返回字符串
            if (message.Items.Count == 1 && message.Items.First() is TextContent singleText)
            {
                return singleText.Text ?? string.Empty;
            }

            // 多模态内容，转换为content数组格式
            var contentArray = new List<object>();
            
            foreach (var item in message.Items)
            {
                if (item is TextContent text)
                {
                    contentArray.Add(new 
                    { 
                        type = "text", 
                        text = text.Text ?? string.Empty 
                    });
                }
                else if (item is ImageContent image)
                {
                    var imageUrl = GetImageUrl(image);
                    if (!string.IsNullOrEmpty(imageUrl))
                    {
                        contentArray.Add(new 
                        { 
                            type = "image_url", 
                            image_url = new { url = imageUrl } 
                        });
                    }
                }
                // 可以在这里添加对其他内容类型的支持，如音频、视频等
            }

            return contentArray.Count > 0 ? contentArray : string.Empty;
        }

        /// <summary>
        /// 从ImageContent中提取图片URL
        /// </summary>
        /// <param name="image">ImageContent对象</param>
        /// <returns>图片URL或base64数据URI</returns>
        private static string GetImageUrl(ImageContent image)
        {
            // 优先使用Uri
            if (image.Uri != null)
            {
                return image.Uri.ToString();
            }

            // 如果有DataUri，使用DataUri
            if (!string.IsNullOrEmpty(image.DataUri))
            {
                return image.DataUri;
            }

            // 如果有Data，转换为base64 DataUri
            if (image.Data.HasValue && image.Data.Value.Length > 0)
            {
                var base64Data = Convert.ToBase64String(image.Data.Value.ToArray());
                var mimeType = image.MimeType ?? "image/jpeg"; // 默认MIME类型
                return $"data:{mimeType};base64,{base64Data}";
            }

            // 检查Metadata中是否有文件内容
            if (image.Metadata != null)
            {
                if (image.Metadata.TryGetValue("FileContent", out var fileContentObj) && 
                    fileContentObj is byte[] fileContent && fileContent.Length > 0)
                {
                    var base64Data = Convert.ToBase64String(fileContent);
                    var mimeType = image.MimeType ?? "image/jpeg";
                    return $"data:{mimeType};base64,{base64Data}";
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// 检查ChatHistory中是否包含多模态内容
        /// </summary>
        /// <param name="chatHistory">聊天历史</param>
        /// <returns>是否包含多模态内容</returns>
        public static bool HasMultimodalContent(ChatHistory chatHistory)
        {
            return chatHistory.Any(message => 
                message.Items != null && 
                message.Items.Count > 1 || 
                message.Items.Any(item => item is ImageContent));
        }

        /// <summary>
        /// 将整个ChatHistory转换为OpenAI格式的消息数组
        /// </summary>
        /// <param name="chatHistory">Semantic Kernel的ChatHistory</param>
        /// <returns>OpenAI格式的消息数组</returns>
        public static List<object> ConvertChatHistoryToOpenAIFormat(ChatHistory chatHistory)
        {
            var messages = new List<object>();

            foreach (var message in chatHistory)
            {
                var openAIMessage = new
                {
                    role = message.Role.Label.ToLowerInvariant(),
                    content = ConvertToOpenAIFormat(message),
                    name = message.AuthorName // 可选的name字段
                };

                messages.Add(openAIMessage);
            }

            return messages;
        }

        /// <summary>
        /// 创建一个示例，展示如何使用转换器
        /// </summary>
        /// <returns>使用示例的说明文本</returns>
        public static string GetUsageExample()
        {
            return @"
使用示例：

// 创建包含文本和图片的多模态消息
var messageCollection = new ChatMessageContentItemCollection() 
{ 
    new TextContent { Text = ""这是什么"" },
    new ImageContent { Uri = new Uri(""https://example.com/image.jpg"") }
};

var chatHistory = new ChatHistory();
chatHistory.AddUserMessage(messageCollection);

// 转换为OpenAI格式
var openAIMessages = OpenAIMultimodalMessageConverter.ConvertChatHistoryToOpenAIFormat(chatHistory);

// 结果将是：
// [
//   {
//     ""role"": ""user"",
//     ""content"": [
//       {""type"": ""text"", ""text"": ""这是什么""},
//       {""type"": ""image_url"", ""image_url"": {""url"": ""https://example.com/image.jpg""}}
//     ]
//   }
// ]
";
        }
    }
}

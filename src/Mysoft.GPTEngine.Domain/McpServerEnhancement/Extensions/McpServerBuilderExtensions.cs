using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ModelContextProtocol.Protocol;
using ModelContextProtocol.Server;
using Mysoft.GPTEngine.Domain.McpServerEnhancement.Models;
using Mysoft.GPTEngine.Domain.McpServerEnhancement.Services;
using Serilog;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Extensions
{
    /// <summary>
    /// MCP服务器构建器扩展方法
    /// </summary>
    public static class McpServerBuilderExtensions
    {
        /// <summary>
        /// 添加动态工具支持
        /// </summary>
        public static IMcpServerBuilder WithDynamicTools(this IMcpServerBuilder builder)
        {
            // 注册动态工具相关服务
            builder.Services.AddSingleton<IDynamicToolProvider, DynamicToolProvider>();

            // 使用 IConfigureOptions 来正确配置 MCP 服务器选项
            builder.Services.AddSingleton<IConfigureOptions<McpServerOptions>>(serviceProvider =>
                new ConfigureOptions<McpServerOptions>(options =>
                {
                    // 设置工具能力，使用动态处理器
                    options.Capabilities.Tools = new ToolsCapability
                    {
                        ListToolsHandler = async (request, cancellationToken) =>
                        {
                            var dynamicToolProvider = serviceProvider.GetRequiredService<IDynamicToolProvider>();
                            var apiClientService = serviceProvider.GetRequiredService<ApiClientService>();
                            var mcpContextService = serviceProvider.GetRequiredService<IMcpContextService>();

                            // 从 HTTP 上下文获取 headers 并设置到 API 客户端
                            var contextHeaders = mcpContextService.GetCurrentRequestHeaders();
                            if (contextHeaders.Count > 0)
                            {
                                Log.Information("[MCP ListTools] 从 HTTP 上下文获取到 {Count} 个 headers", contextHeaders.Count);
                                apiClientService.SetContextHeaders(contextHeaders);
                            }
                            else
                            {
                                Log.Warning("[MCP ListTools] 未从 HTTP 上下文获取到 headers，使用默认配置");
                            }

                            var tools = await dynamicToolProvider.GetToolsAsync();
                            return new ListToolsResult { Tools = tools.ToList() };
                        },
                        CallToolHandler = async (request, cancellationToken) =>
                        {
                            var dynamicToolProvider = serviceProvider.GetRequiredService<IDynamicToolProvider>();
                            var apiClientService = serviceProvider.GetRequiredService<ApiClientService>();
                            var mcpContextService = serviceProvider.GetRequiredService<IMcpContextService>();

                            if (request.Params?.Name == null)
                            {
                                throw new Exception("工具名称不能为空");
                            }

                            var toolName = request.Params.Name;

                            // 转换参数格式
                            var arguments = new Dictionary<string, object>();
                            if (request.Params.Arguments != null)
                            {
                                foreach (var arg in request.Params.Arguments)
                                {
                                    arguments[arg.Key] = arg.Value.ValueKind == JsonValueKind.String
                                        ? arg.Value.GetString()
                                        : arg.Value.ToString();
                                }
                            }

                            // 优先从 HTTP 上下文获取 headers
                            var contextHeaders = mcpContextService.GetCurrentRequestHeaders();
                            if (contextHeaders.Count > 0)
                            {
                                Log.Information("[MCP CallTool] 从 HTTP 上下文获取到 {Count} 个 headers，工具: {ToolName}",
                                    contextHeaders.Count, toolName);
                                apiClientService.SetContextHeaders(contextHeaders);
                            }
                            else
                            {
                                // 如果 HTTP 上下文没有 headers，尝试从参数中提取
                                var paramHeaders = ExtractContextHeaders(arguments);
                                if (paramHeaders.Count > 0)
                                {
                                    Log.Information("[MCP CallTool] 从工具参数中提取到 {Count} 个 headers，工具: {ToolName}",
                                        paramHeaders.Count, toolName);
                                    apiClientService.SetContextHeaders(paramHeaders);
                                }
                                else
                                {
                                    Log.Warning("[MCP CallTool] 未获取到任何上下文 headers，使用默认配置，工具: {ToolName}", toolName);
                                }
                            }

                            var result = await dynamicToolProvider.CallToolAsync(toolName, arguments);

                            return new CallToolResult
                            {
                                Content = new[]
                                {
                                    new TextContentBlock
                                    {
                                        Type = "text",
                                        Text = result.ToString() ?? ""
                                    }
                                }
                            };
                        }
                    };
                }));

            return builder;
        }

        /// <summary>
        /// 从工具参数中提取上下文 headers
        /// </summary>
        private static Dictionary<string, string> ExtractContextHeaders(Dictionary<string, object> arguments)
        {
            var contextHeaders = new Dictionary<string, string>();

            // 检查是否有特殊的上下文参数
            if (arguments.TryGetValue("_context_headers", out var headersObj) && headersObj is string headersJson)
            {
                try
                {
                    var headers = JsonSerializer.Deserialize<Dictionary<string, string>>(headersJson);
                    if (headers != null)
                    {
                        foreach (var header in headers)
                        {
                            contextHeaders[header.Key] = header.Value;
                        }
                    }
                }
                catch (JsonException)
                {
                    // 忽略JSON解析错误
                }
            }

            // 检查常见的上下文字段
            var commonHeaders = new[] { "tenantCode", "auth_user_info", "my-api-authorization" };
            foreach (var headerName in commonHeaders)
            {
                if (arguments.TryGetValue(headerName, out var headerValue) && headerValue != null)
                {
                    contextHeaders[headerName] = headerValue.ToString();
                }
            }

            return contextHeaders;
        }

        /// <summary>
        /// 从工具定义创建MCP工具
        /// </summary>
        private static Tool CreateToolFromDefinition(ToolDefinition toolDef)
        {
            var inputSchema = new Dictionary<string, object>
            {
                ["type"] = "object",
                ["properties"] = toolDef.InputSchema.Properties.ToDictionary(
                    p => p.Key,
                    p => new Dictionary<string, object>
                    {
                        ["type"] = p.Value.Type,
                        ["description"] = p.Value.Description
                    }
                ),
                ["required"] = toolDef.InputSchema.Required
            };

            return new Tool
            {
                Name = toolDef.Name,
                Description = toolDef.Description,
                InputSchema = JsonSerializer.Deserialize<JsonElement>(JsonSerializer.Serialize(inputSchema))
            };
        }
    }
}

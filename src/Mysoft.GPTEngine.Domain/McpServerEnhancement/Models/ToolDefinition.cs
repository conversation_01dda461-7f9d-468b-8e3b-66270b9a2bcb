using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Models
{
    /// <summary>
    /// 工具定义的根对象
    /// </summary>
    public class ToolDefinitionResponse
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public List<ToolDefinition> Data { get; set; } = new();
    }

    /// <summary>
    /// 单个工具定义
    /// </summary>
    public class ToolDefinition
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("input_schema")]
        public InputSchema InputSchema { get; set; } = new();
    }

    /// <summary>
    /// 输入参数架构
    /// </summary>
    public class InputSchema
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "object";

        [JsonPropertyName("properties")]
        public Dictionary<string, PropertyDefinition> Properties { get; set; } = new();

        [JsonPropertyName("required")]
        public List<string> Required { get; set; } = new();
    }

    /// <summary>
    /// 属性定义
    /// </summary>
    public class PropertyDefinition
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;
    }
}

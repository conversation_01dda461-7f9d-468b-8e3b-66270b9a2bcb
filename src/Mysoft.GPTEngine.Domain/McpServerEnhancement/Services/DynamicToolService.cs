using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.McpServerEnhancement.Models;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Services
{
    /// <summary>
    /// 动态工具服务 - 负责从API加载工具定义并创建动态工具
    /// </summary>
    public class DynamicToolService
    {
        private readonly ILogger<DynamicToolService> _logger;
        private readonly BaseToolService _baseToolService;
        private readonly ApiClientService _apiClientService;
        private List<ToolDefinition> _toolDefinitions = new();

        public DynamicToolService(ILogger<DynamicToolService> logger, BaseToolService baseToolService, ApiClientService apiClientService)
        {
            _logger = logger;
            _baseToolService = baseToolService;
            _apiClientService = apiClientService;
        }

        /// <summary>
        /// 从API加载工具定义
        /// </summary>
        public async Task LoadToolDefinitionsAsync()
        {
            try
            {
                _logger.LogInformation("开始从API加载工具定义...");
                var response = await _apiClientService.GetToolListAsync();

                if (response?.Success == true && response.Data != null)
                {
                    _toolDefinitions = response.Data;
                    _logger.LogInformation("成功从API加载 {Count} 个工具定义", _toolDefinitions.Count);

                    foreach (var tool in _toolDefinitions)
                    {
                        _logger.LogDebug("加载工具: {Name} - {Title}", tool.Name, tool.Title);
                    }
                }
                else
                {
                    _logger.LogWarning("从API获取工具定义失败或数据格式无效");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从API加载工具定义时发生错误");
            }
        }

        /// <summary>
        /// 获取所有工具定义
        /// </summary>
        public IReadOnlyList<ToolDefinition> GetToolDefinitions()
        {
            return _toolDefinitions.AsReadOnly();
        }

        /// <summary>
        /// 根据名称获取工具定义
        /// </summary>
        public ToolDefinition? GetToolDefinition(string toolName)
        {
            return _toolDefinitions.FirstOrDefault(t => t.Name == toolName);
        }

        /// <summary>
        /// 执行动态工具
        /// </summary>
        public async Task<string> ExecuteDynamicToolAsync(string toolName, Dictionary<string, object?> parameters)
        {
            var toolDefinition = GetToolDefinition(toolName);
            if (toolDefinition == null)
            {
                var errorMessage = $"未找到工具定义: {toolName}";
                _logger.LogWarning(errorMessage);
                return JsonSerializer.Serialize(new { error = errorMessage });
            }

            _logger.LogInformation("执行动态工具: {ToolName} ({Title})", toolName, toolDefinition.Title);

            // 验证必需参数
            var validationResult = ValidateParameters(toolDefinition, parameters);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("参数验证失败: {ToolName}, 错误: {Error}", toolName, validationResult.ErrorMessage);
                return JsonSerializer.Serialize(new { error = validationResult.ErrorMessage });
            }

            // 调用基础工具服务执行实际逻辑
            return await _baseToolService.ExecuteToolAsync(toolDefinition.Id, toolName, parameters);
        }

        /// <summary>
        /// 验证工具参数
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateParameters(ToolDefinition toolDefinition, Dictionary<string, object?> parameters)
        {
            // 检查必需参数
            foreach (var requiredParam in toolDefinition.InputSchema.Required)
            {
                if (!parameters.ContainsKey(requiredParam) || parameters[requiredParam] == null)
                {
                    return (false, $"缺少必需参数: {requiredParam}");
                }
            }

            // 检查参数类型（简单验证）
            foreach (var param in parameters)
            {
                if (toolDefinition.InputSchema.Properties.TryGetValue(param.Key, out var propDef))
                {
                    if (!ValidateParameterType(param.Value, propDef.Type))
                    {
                        return (false, $"参数 {param.Key} 类型不匹配，期望: {propDef.Type}");
                    }
                }
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证参数类型
        /// </summary>
        private bool ValidateParameterType(object? value, string expectedType)
        {
            if (value == null) return true; // null值在必需参数检查中处理

            return expectedType.ToLower() switch
            {
                "string" => value is string,
                "number" or "integer" => value is int or long or float or double or decimal,
                "boolean" => value is bool,
                "object" => value is Dictionary<string, object?> or object,
                "array" => value is Array or IEnumerable,
                _ => true // 未知类型默认通过
            };
        }
    }
}

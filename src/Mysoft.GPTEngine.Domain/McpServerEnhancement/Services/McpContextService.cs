using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Services
{
    /// <summary>
    /// MCP 上下文服务 - 用于在 HTTP 请求和 MCP 处理器之间传递上下文信息
    /// </summary>
    public interface IMcpContextService
    {
        /// <summary>
        /// 设置当前请求的上下文 headers
        /// </summary>
        void SetCurrentRequestHeaders(Dictionary<string, string> headers);
        
        /// <summary>
        /// 获取当前请求的上下文 headers
        /// </summary>
        Dictionary<string, string> GetCurrentRequestHeaders();
        
        /// <summary>
        /// 清除当前请求的上下文
        /// </summary>
        void ClearCurrentRequestHeaders();
    }

    /// <summary>
    /// MCP 上下文服务实现
    /// </summary>
    public class McpContextService : IMcpContextService
    {
        private readonly ILogger<McpContextService> _logger;
        private readonly Dictionary<string, string> _currentHeaders = new Dictionary<string, string>();
        private readonly object _lock = new object();

        public McpContextService(ILogger<McpContextService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 设置当前请求的上下文 headers
        /// </summary>
        public void SetCurrentRequestHeaders(Dictionary<string, string> headers)
        {
            lock (_lock)
            {
                _currentHeaders.Clear();
                if (headers != null)
                {
                    foreach (var header in headers)
                    {
                        _currentHeaders[header.Key] = header.Value;
                    }
                }
                
                _logger.LogInformation("MCP 上下文已设置，共 {Count} 个 headers", _currentHeaders.Count);
                foreach (var header in _currentHeaders)
                {
                    _logger.LogDebug("MCP 上下文 header: {Key}={Value}", header.Key,
                        header.Key.Contains("authorization", StringComparison.OrdinalIgnoreCase) ? "***" : header.Value);
                }
            }
        }

        /// <summary>
        /// 获取当前请求的上下文 headers
        /// </summary>
        public Dictionary<string, string> GetCurrentRequestHeaders()
        {
            lock (_lock)
            {
                var result = new Dictionary<string, string>(_currentHeaders);
                _logger.LogDebug("获取 MCP 上下文 headers，共 {Count} 个", result.Count);
                return result;
            }
        }

        /// <summary>
        /// 清除当前请求的上下文
        /// </summary>
        public void ClearCurrentRequestHeaders()
        {
            lock (_lock)
            {
                var count = _currentHeaders.Count;
                _currentHeaders.Clear();
                _logger.LogDebug("已清除 MCP 上下文，原有 {Count} 个 headers", count);
            }
        }
    }
}

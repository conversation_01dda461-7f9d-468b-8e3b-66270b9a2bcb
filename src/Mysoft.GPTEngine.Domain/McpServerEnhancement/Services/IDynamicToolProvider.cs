using System.Collections.Generic;
using System.Threading.Tasks;
using ModelContextProtocol.Protocol;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Services
{
    /// <summary>
    /// 动态工具提供者接口
    /// </summary>
    public interface IDynamicToolProvider
    {
        /// <summary>
        /// 获取所有动态工具
        /// </summary>
        Task<IEnumerable<Tool>> GetToolsAsync();

        /// <summary>
        /// 调用指定的工具
        /// </summary>
        Task<object> CallToolAsync(string toolName, Dictionary<string, object?> arguments);
    }
}

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.Domain.McpServerEnhancement.Models;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Services
{
    /// <summary>
    /// API客户端服务 - 负责与外部API进行通信
    /// 不依赖于 HTTP 上下文，通过 MCP 请求传递的 headers 实现上下文传递
    /// </summary>
    public class ApiClientService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ApiClientService> _logger;
        private readonly IConfigurationService _configurationService;
        private readonly string _baseUrl;

        // 存储从 MCP 请求中传递过来的上下文信息
        private readonly Dictionary<string, string> _contextHeaders = new Dictionary<string, string>();
        private readonly object _contextLock = new object();

        public ApiClientService(HttpClient httpClient, ILogger<ApiClientService> logger, IConfigurationService configurationService)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configurationService = configurationService;

            // 从配置服务获取 DpUrl
            _baseUrl = _configurationService.GetConfigurationItemByKey(EMCConfigConst.DpUrl)?.TrimEnd('/') ;

            _logger.LogInformation("ApiClientService 初始化，BaseUrl: {BaseUrl}", _baseUrl);

            // 设置默认的测试 headers
            // SetDefaultTestHeaders();
        }

        /// <summary>
        /// 设置默认的测试 headers
        /// </summary>
        private void SetDefaultTestHeaders()
        {
            lock (_contextLock)
            {
                _contextHeaders.Clear();
                _contextHeaders["tenantCode"] = "ompdmdqlyz";
                _contextHeaders["auth_user_info"] = "admin";
                _contextHeaders["my-api-authorization"] = "eyJrdHkiOiJvY3QiLCJ0eXAiOiJKV1QiLCJhbGciOiJTTTMiLCJraWQiOiIxNzMwMzYzNjIxIn0.eyJzdWIiOiJ3b3JrYmVuY2giLCJuYmYiOjE3NTA5MjIxNzgsImV4cCI6MTc1MDkyOTM3OCwianRpIjoiODBiZThmNGYtYThlZC0zNGE4LThlMWUtOWEyNzBiOWZhYjAxIn0.AH7oa5BsSKxTgKRD8GbynTwxs-N7Kn_Y-hzNZJ4BmbY";

                _logger.LogInformation("已设置默认测试 headers: tenantCode={TenantCode}, auth_user_info={AuthUserInfo}",
                    _contextHeaders["tenantCode"], _contextHeaders["auth_user_info"]);
            }
        }

        /// <summary>
        /// 设置上下文 headers（从 MCP 请求中传递过来）
        /// </summary>
        /// <param name="headers">上下文 headers</param>
        public void SetContextHeaders(Dictionary<string, string> headers)
        {
            lock (_contextLock)
            {
                _contextHeaders.Clear();
                if (headers != null)
                {
                    foreach (var header in headers)
                    {
                        _contextHeaders[header.Key] = header.Value;
                    }
                }
                _logger.LogDebug("设置上下文 headers，共 {Count} 个", _contextHeaders.Count);
            }
        }

        /// <summary>
        /// 配置 HTTP 请求头，包括上下文信息
        /// </summary>
        private void ConfigureRequestHeaders(HttpRequestMessage request)
        {
            lock (_contextLock)
            {
                _logger.LogDebug("开始配置请求头，当前上下文 headers 数量: {Count}", _contextHeaders.Count);

                // 添加从 MCP 请求传递过来的上下文 headers
                foreach (var header in _contextHeaders)
                {
                    try
                    {
                        // 避免重复添加
                        if (!request.Headers.Contains(header.Key))
                        {
                            request.Headers.Add(header.Key, header.Value);
                            _logger.LogDebug("已添加请求头: {Key}={Value}", header.Key,
                                header.Key.Contains("authorization", StringComparison.OrdinalIgnoreCase) ? "***" : header.Value);
                        }
                        else
                        {
                            _logger.LogDebug("请求头已存在，跳过: {Key}", header.Key);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "添加请求头失败: {Key}={Value}", header.Key, header.Value);
                    }
                }

                _logger.LogInformation("请求头配置完成，共添加 {Count} 个 headers", _contextHeaders.Count);
            }
        }

        /// <summary>
        /// 获取工具列表
        /// </summary>
        /// <returns>工具定义响应</returns>
        public async Task<ToolDefinitionResponse> GetToolListAsync()
        {
            try
            {
                var url = $"{_baseUrl}/openapi/ai/get_tool_list";
                _logger.LogInformation("正在获取工具列表: {Url}", url);

                // 创建 HTTP 请求
                using var request = new HttpRequestMessage(HttpMethod.Post, url);
                request.Content = new StringContent("{}", Encoding.UTF8, "application/json");

                // 配置请求头，包括上下文信息
                ConfigureRequestHeaders(request);

                // 发送请求
                using var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("API响应: {Response}", responseContent);

                    var result = JsonSerializer.Deserialize<ToolDefinitionResponse>(responseContent);
                    _logger.LogInformation("成功获取工具列表，共 {Count} 个工具", result?.Data?.Count ?? 0);
                    return result;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("获取工具列表失败，状态码: {StatusCode}, 响应: {Response}",
                        response.StatusCode, errorContent);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工具列表时发生异常");
                return null;
            }
        }

        /// <summary>
        /// 获取工具执行数据
        /// </summary>
        /// <param name="sceneId">场景ID</param>
        /// <param name="parameters">参数字典</param>
        /// <returns>执行结果</returns>
        public async Task<string> GetDataAsync(string sceneId, Dictionary<string, object> parameters)
        {
            try
            {
                var url = $"{_baseUrl}/openapi/ai/get_data";
                _logger.LogInformation("正在获取数据: {Url}, SceneId: {SceneId}", url, sceneId);

                // 构建请求体
                var requestBody = new
                {
                    scene_id = sceneId,
                    @params = JsonSerializer.Serialize(parameters)
                };

                var jsonContent = JsonSerializer.Serialize(requestBody);
                _logger.LogDebug("请求体: {RequestBody}", jsonContent);

                // 创建 HTTP 请求
                using var request = new HttpRequestMessage(HttpMethod.Post, url);
                request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // 配置请求头，包括上下文信息
                ConfigureRequestHeaders(request);

                // 发送请求
                using var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("成功获取数据，SceneId: {SceneId}", sceneId);
                    _logger.LogDebug("API响应: {Response}", responseContent);
                    return responseContent;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("获取数据失败，状态码: {StatusCode}, 响应: {Response}",
                        response.StatusCode, errorContent);

                    // 返回错误信息的JSON格式
                    return JsonSerializer.Serialize(new
                    {
                        error = $"API调用失败: {response.StatusCode}",
                        details = errorContent
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据时发生异常，SceneId: {SceneId}", sceneId);
                return JsonSerializer.Serialize(new
                {
                    error = "API调用异常",
                    details = ex.Message
                });
            }
        }
    }
}

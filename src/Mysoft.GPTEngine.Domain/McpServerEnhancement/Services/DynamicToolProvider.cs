using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ModelContextProtocol.Protocol;
using Mysoft.GPTEngine.Domain.McpServerEnhancement.Models;

namespace Mysoft.GPTEngine.Domain.McpServerEnhancement.Services
{
    /// <summary>
    /// 动态工具提供者实现
    /// </summary>
    public class DynamicToolProvider : IDynamicToolProvider
    {
        private readonly ILogger<DynamicToolProvider> _logger;
        private readonly DynamicToolService _dynamicToolService;

        public DynamicToolProvider(ILogger<DynamicToolProvider> logger, DynamicToolService dynamicToolService)
        {
            _logger = logger;
            _dynamicToolService = dynamicToolService;
        }

        /// <summary>
        /// 获取所有动态工具 - 每次都从API重新获取
        /// </summary>
        public async Task<IEnumerable<Tool>> GetToolsAsync()
        {
            _logger.LogInformation("开始从API重新获取工具列表...");

            // 每次都重新从API加载工具定义
            await _dynamicToolService.LoadToolDefinitionsAsync();

            var toolDefinitions = _dynamicToolService.GetToolDefinitions();
            var tools = new List<Tool>();

            foreach (var toolDef in toolDefinitions)
            {
                var tool = CreateToolFromDefinition(toolDef);
                tools.Add(tool);
            }

            _logger.LogInformation("动态提供了 {Count} 个工具", tools.Count);
            return tools;
        }

        /// <summary>
        /// 调用指定的工具
        /// </summary>
        public async Task<object> CallToolAsync(string toolName, Dictionary<string, object?> arguments)
        {
            try
            {
                var result = await _dynamicToolService.ExecuteDynamicToolAsync(toolName, arguments);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用动态工具 {ToolName} 时发生错误", toolName);
                return JsonSerializer.Serialize(new { error = $"执行工具 '{toolName}' 时发生错误: {ex.Message}" });
            }
        }

        /// <summary>
        /// 从工具定义创建MCP工具
        /// </summary>
        private Tool CreateToolFromDefinition(ToolDefinition toolDef)
        {
            var inputSchema = new Dictionary<string, object>
            {
                ["type"] = "object",
                ["properties"] = toolDef.InputSchema.Properties.ToDictionary(
                    p => p.Key,
                    p => new Dictionary<string, object>
                    {
                        ["type"] = p.Value.Type,
                        ["description"] = p.Value.Description
                    }
                ),
                ["required"] = toolDef.InputSchema.Required
            };

            return new Tool
            {
                Name = toolDef.Name,
                Description = toolDef.Description,
                InputSchema = JsonSerializer.Deserialize<JsonElement>(JsonSerializer.Serialize(inputSchema))
            };
        }
    }
}

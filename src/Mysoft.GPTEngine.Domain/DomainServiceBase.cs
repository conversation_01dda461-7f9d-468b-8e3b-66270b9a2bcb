using AutoMapper;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain
{
    public class DomainServiceBase
    {
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly IMapper _mapper;
        public readonly IHttpContextAccessor _httpContextAccessor;
        public DomainServiceBase(IMysoftContextFactory mysoftContextFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper)
        {
            _mapper = mapper;
            _mysoftContextFactory = mysoftContextFactory;
            _httpContextAccessor = httpContextAccessor;
        }
    }
}

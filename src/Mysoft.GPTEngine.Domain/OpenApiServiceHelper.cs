using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Plugins.OpenApi;
using Mysoft.GPTEngine.Domain.ApiAuthorization;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared;

namespace Mysoft.GPTEngine.Domain
{
    public class OpenApiServiceHelper
    {
        public delegate Task Authenticate(HttpRequestMessage request, OpenApiRequestOptions openApiRequestOptions, CancellationToken cancellationToken);

        public static List<string> allMethod = new List<string>(){"post","get","put","head"};
        public static async Task<FunctionResult> RequestApi(OpenApiRequestOptions openApiRequestOptions, MysoftMemoryCache mysoftMemoryCache, IConfigurationService configurationService, IHttpContextAccessor httpContextAccessor)
        {
            Kernel kernel = new Kernel();
            IAuthorization authorization = ApiAuthorizationFactory.GetAuthorization(openApiRequestOptions.AuthMode, kernel, mysoftMemoryCache, configurationService, httpContextAccessor);
            Authenticate authenticate = new Authenticate(authorization.AuthenticateRequestAsync);
            var func = await CreateKernelPlugin(openApiRequestOptions, authenticate, kernel);

            KernelFunction kf = null;
            if (openApiRequestOptions.OperationId == null || !func.TryGetFunction(openApiRequestOptions.OperationId, out kf))
            {
                for (int i = 0; i < allMethod.Count; i++)
                {
                    var functionName = GetFunctionName(openApiRequestOptions.Path, allMethod[i]);
                    if (func.TryGetFunction(functionName, out kf))
                    {
                        break;
                    }
                }
            }
            if (kf == null)
            {
                foreach (var item in func)
                {
                    if (item.Metadata.AdditionalProperties.TryGetValue("operation", out var operation))
                    {
#pragma warning disable SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                        if (operation is RestApiOperation openApiOperation && openApiOperation.Path == openApiRequestOptions.Path)
                        {
                            kf = item;
                            break;
                        }
#pragma warning disable SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                    }
                }
            }
            var result = await kernel.InvokeAsync(kf, openApiRequestOptions.Arguments);
            return await Task.FromResult(result);
        }
        
        public static async Task<OpenApiRequestOptions> GetAgentOpenApiRequestOptions(PluginEntity pluginEntity, PluginMetadataEntity pluginMetadataEntity,
            IMysoftContextFactory _mysoftContextFactory, MysoftConfigurationDomain _mysoftConfigurationDomain)
        {
            var openApiRequestOptions = await GetOpenApiRequestOptions(pluginEntity, pluginMetadataEntity, _mysoftContextFactory);
            if(pluginEntity.Source == 0)//明源
            {

                openApiRequestOptions.ServerUrlOverride = new Uri(_mysoftContextFactory.GetMysoftContext().GptBuilderUrl);
                openApiRequestOptions.AuthMode = 2;
            }
            else if(pluginEntity.Source == 1) //MIP
            {
                var mipInfo = _mysoftConfigurationDomain.GetMipInfo();
                openApiRequestOptions.ServerUrlOverride = new Uri(mipInfo.ServiceUrl);
                openApiRequestOptions.MipInfoDto = mipInfo;
                openApiRequestOptions.AuthMode = 3;;
            }
            else
            {
                // 如果 Source 不是 0 或 1，尝试使用插件自身的 PluginUrl
                if (!string.IsNullOrEmpty(pluginEntity.PluginUrl))
                {
                    openApiRequestOptions.ServerUrlOverride = new Uri(pluginEntity.PluginUrl);
                }
                else
                {
                    // 默认使用明源的配置
                    openApiRequestOptions.ServerUrlOverride = new Uri(_mysoftContextFactory.GetMysoftContext().GptBuilderUrl);
                    openApiRequestOptions.AuthMode = 2;
                }
            }
            return openApiRequestOptions;
        }
        
        public static async Task<OpenApiRequestOptions> GetOpenApiRequestOptions(PluginEntity pluginEntity, PluginMetadataEntity pluginMetadataEntity, IMysoftContextFactory _mysoftContextFactory)
        {
            if (pluginEntity == null || pluginMetadataEntity == null) return await Task.FromResult(new OpenApiRequestOptions());
            var arguments = new KernelArguments();
            OpenApiRequestOptions openApiRequestOptions = new OpenApiRequestOptions()
            {
                Metadata = pluginMetadataEntity.Metadata,
                CreateTokenUrl = pluginEntity.CreateTokenUrl,
                AuthMode = pluginEntity.AuthMode,
                Arguments = arguments,
                ParamListStr = pluginEntity.ParamList,
                MysoftContextDto = _mysoftContextFactory.GetMysoftContext()
            };
            return await Task.FromResult(openApiRequestOptions);
        }
        
        public static async Task<KernelPlugin> CreateKernelPlugin(OpenApiRequestOptions openApiRequestOptions, Authenticate authenticate, Kernel kernel)
        {
            byte[] byteArray = Encoding.UTF8.GetBytes(openApiRequestOptions.Metadata);

#pragma warning disable SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
            var executionParameters = new OpenApiFunctionExecutionParameters(
                serverUrlOverride: openApiRequestOptions.ServerUrlOverride,
                ignoreNonCompliantErrors: true,
                authCallback: async (request, cancellationToken) =>
                {
                    await authenticate(request, openApiRequestOptions, cancellationToken);
                }
            ) { HttpResponseContentReader = ReadHttpResponseContentAsync };
#pragma warning restore SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。


            using (MemoryStream stream = new MemoryStream(byteArray))
            {
                stream.Position = 0;
#pragma warning disable SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                return await kernel.ImportPluginFromOpenApiAsync("OpenApiService", stream, executionParameters);
#pragma warning restore SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
            }
        }

#pragma warning disable SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
        private static async Task<object?> ReadHttpResponseContentAsync(HttpResponseContentReaderContext context, CancellationToken cancellationToken)
#pragma warning restore SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
        {
            if (context.Response.Content.Headers.ContentType?.MediaType == "text/event-stream")
            {
                return await context.Response.Content.ReadAsStreamAsync();
            }

            if (context.Request.Headers.Contains("x-stream"))
            {
                return await context.Response.Content.ReadAsStreamAsync();
            }

            return null;
        }
        
        
        private static Regex RemoveInvalidCharsRegex() => s_removeInvalidCharsRegex;
        private static readonly Regex s_removeInvalidCharsRegex = new Regex("[^0-9A-Za-z_./-/{/}]", RegexOptions.Compiled);

        private static string GetFunctionName(string path, string method)
        {
            string[] tokens = path.Split('/', '\\');
            StringBuilder result = new StringBuilder();
            result.Append(CultureInfo.CurrentCulture.TextInfo.ToTitleCase(method));

            foreach (string token in tokens)
            {
                // Removes all characters that are not ASCII letters, digits, and underscores.
                string formattedToken = RemoveInvalidCharsRegex().Replace(token, "");
                result.Append(CultureInfo.CurrentCulture.TextInfo.ToTitleCase(formattedToken.ToLower(CultureInfo.CurrentCulture)));
            }

            return result.ToString();
        }
    }
}
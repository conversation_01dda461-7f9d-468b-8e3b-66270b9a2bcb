using Microsoft.AspNetCore.Hosting;
using Mysoft.GPTEngine.Diagnostics.Extend;

[assembly: HostingStartup(typeof(DiagnosticsExtendHostingStartup))]

namespace Mysoft.GPTEngine.Diagnostics.Extend
{
    public class DiagnosticsExtendHostingStartup : IHostingStartup
    {
        public void Configure(IWebHostBuilder builder)
        {
            builder.ConfigureServices(services => services.AddFastTrackerDiagnosticProcessor());
        }
    }
}

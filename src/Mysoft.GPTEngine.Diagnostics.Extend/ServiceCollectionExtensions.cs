using FastTracker;
using Microsoft.Extensions.DependencyInjection;

namespace Mysoft.GPTEngine.Diagnostics.Extend
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddFastTrackerDiagnosticProcessor(this IServiceCollection services)
        {

            // 兼容后续HTTP Client 诊断事件源 
            // services.AddSingleton<ITracingDiagnosticProcessor, HttpClientDiagnosticDiagnosticProcessor>();
            services.AddSingleton<ITracingDiagnosticProcessor, SemanticKernelActivityDiagnosticProcessor>();

            return services;
        }
    }
}

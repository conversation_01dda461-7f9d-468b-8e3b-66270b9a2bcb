using System;
using System.Collections.Concurrent;
using FastTracker;
using FastTracker.Config;
using FastTracker.Diagnostics;
using FastTracker.Tracing;
using FastTracker.Tracing.Segments;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Diagnostics.Extend
{
    public class SemanticKernelActivityDiagnosticProcessor : ITracingDiagnosticProcessor
    {
        public string ListenerName { get; } = "SemanticKernelActivityDiagnosticListener";

        public bool AllowSubscribe { get; private set; }

        private readonly ITracingContext _tracingContext;
        private const string ComponentName = "SemanticKernelActivity";

        private readonly ConcurrentDictionary<Guid, SegmentContext> _processingDiagnostiDic = new ConcurrentDictionary<Guid, SegmentContext>();
        public SemanticKernelActivityDiagnosticProcessor(ITrackerConfigAccessor configAccessor, ITracingContext tracingContext)
        {
            AllowSubscribe = configAccessor.Config.CollectLayer.Local.Enable;
            _tracingContext = tracingContext;
        }

        [DiagnosticName("Mysoft.GPTEngine.Plugin.SemanticKernelActivity.StartExecute")]
        public void StartExecute([Property(Name = "OperationId")] Guid operationId, [Property(Name = "StepName")] string stepName, [Property(Name = "FlowNode")] FlowNode flowNode, [Property(Name = "StartTime")] long startTime)
        {
            if (!_tracingContext.CreateLocalSegmentContext(stepName, out var context)) return;
            if (!_processingDiagnostiDic.TryAdd(operationId, context)) return;
            context.Span.StartTime = startTime;
            context.Span.SpanLayer = SpanLayer.LOCAL;
            context.Span.Component = ComponentName;
            addFlowNodeTags(context, flowNode);

        }

        [DiagnosticName("Mysoft.GPTEngine.Plugin.SemanticKernelActivity.EndExecute")]
        public void EndExecute([Property(Name = "OperationId")] Guid operationId, [Property(Name = "EndTime")] long endTime)
        {
            if (_processingDiagnostiDic.TryRemove(operationId, out var context))
            {
                context.Span.EndTime = endTime;
                _tracingContext.Release(context);
            }
        }

        [DiagnosticName("Mysoft.GPTEngine.Plugin.SemanticKernelActivity.SetFlowNode")]
        public void SetFlowNode([Property(Name = "OperationId")] Guid operationId, [Property(Name = "FlowNode")] FlowNode flowNode)
        {
            if (_processingDiagnostiDic.TryGetValue(operationId, out var context))
            {
                addFlowNodeTags(context, flowNode);
            }
        }

        private void addFlowNodeTags(SegmentContext context, FlowNode flowNode)
        {
            if (flowNode != null)
            {
                context.Span.AddTag(nameof(flowNode.Id), flowNode.Id);
                context.Span.AddTag(nameof(flowNode.Name), flowNode.Name);
                context.Span.AddTag(nameof(flowNode.Code), flowNode.Code);
                context.Span.AddTag(nameof(flowNode.Type), flowNode.Type);
                context.Span.AddTag(nameof(flowNode.Description), flowNode.Description);
                // TODO: 按需添加更多需要跟踪的属性
            }
        }
    }
}

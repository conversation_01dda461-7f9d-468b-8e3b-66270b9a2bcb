using System;

namespace Mysoft.GPTEngine.Application.Contracts
{
    public class EventStreamDto
    {
        public EventStreamDto() { Id = Guid.NewGuid().ToString(); }
        public EventStreamDto(String data)
        {
            Id = Guid.NewGuid().ToString();
            Event = "message";
            Content = data;
            Retry = 1000;
        }
        public String GetMessage()
        {
            return $"0:{Content}\n";

        }
        public String Id { get; set; }
        public String Event { get; set; }
        public Int32 Retry { get; set; }
        public String Content { get; set; }
    }
}

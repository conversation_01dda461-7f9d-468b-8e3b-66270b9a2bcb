using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Consts;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Newtonsoft.Json;
using Serilog.Context;

namespace Mysoft.GPTEngine.Application
{
    public class AppServiceBase
    {
        public readonly IHttpContextAccessor _httpContextAccessor;
        public readonly MysoftContext MysoftContext;
        public readonly IMapper Mapper;
        public readonly ILogger _logger;
        public AppServiceBase(IHttpContextAccessor httpContextAccessor,IMysoftContextFactory mysoftContextFactory, IMapper mapper, ILogger logger)
        {
            _httpContextAccessor = httpContextAccessor;
            MysoftContext = mysoftContextFactory.GetMysoftContext();
            Mapper = mapper;
            _logger = logger;
        }

        public async Task<ActionResultDto> Succeed()
        {
            return await Task.FromResult(new ActionResultDto { Success = true });
        }
        public async Task<ActionResultDto<T>> Succeed<T>(T data)
        {
            return await Task.FromResult(new ActionResultDto<T> { Success = true, Data = data });
        }
        public async Task<ActionResultDto> Failed(String message)
        {
            return await Task.FromResult(new ActionResultDto { Success = false, Message = message });
        }
        public void UpdateArguments(KernelArguments arguments)
        {
            if (arguments == null) return;

            var key = nameof(KernelArguments);

            if (_httpContextAccessor.HttpContext.Items.ContainsKey(key))
            {
                var currArguments = _httpContextAccessor.HttpContext.Items[key] as KernelArguments;
                foreach (var arg in arguments)
                {
                    if (currArguments.ContainsName(arg.Key))
                    {
                        currArguments[arg.Key] = arg.Value;
                    }
                    else
                    {
                        currArguments.Add(arg.Key, arg.Value);
                    }
                }
            }
            else
            {
                _httpContextAccessor.HttpContext.Items.Add(key, arguments);
            }
        }


        public async Task ProcessEvent(string process,bool isStream)
        {
            if (isStream == false) return;

            _logger.LogDebug("AppServiceBase.ProcessEvent:process:{0}", process);
            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ProcessEvent, $"{{\"text\": \"{process}\" }}"));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
        }
        public async Task DoneEvent(Guid? messageGuid, bool isStream)
        {
            if (isStream == false) return;
            
            _logger.LogDebug("AppServiceBase.DoneEvent:process:{0}", messageGuid);
            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.DoneEvent, messageGuid == null ? string.Empty : messageGuid.ToString()));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
        }
        public async Task<string> ErrorEvent(string message, bool isStream,ChatRunDto chatRunDto, Exception ex, ILogger<AgentAppService> _logger)
        {
            ErrorResultDto errorResultDto = new ErrorResultDto();
            (string, string, string, int) errInfo = ErrorCodeInitHelper.ErrorCodeInit(ex);
            errorResultDto.ErrorCode = errInfo.Item1;
            errorResultDto.ErrorMessage = message;
            errorResultDto.UserMessage = errInfo.Item2;
            // 系统异常记录天眼日志
            if (errInfo.Item3 == "exception")
            {
                var logId = Guid.NewGuid().ToString().Replace("-", "");
                using (LogContext.PushProperty("LogId", logId))
                {
                    _logger.LogError(ex, "执行技能失败");
                }
                errorResultDto.UserMessage = $"系统发生错误，错误编号[{logId}]，请联系系统管理员。";
            }
            errorResultDto.Type = errInfo.Item3;
            errorResultDto.Link = ErrorCodeLinkInit(errInfo.Item4);
            string errorMsg = JsonConvert.SerializeObject(errorResultDto);
            if (isStream == false) return errorMsg;
            //记录错误信息到消息，直接记录真实的报错信息
            var index = chatRunDto.ChatMessages.Count;

            _logger.LogDebug("AppServiceBase.ErrorEvent:process: {0}", message);

            var dto = new ChatMessageDto
            {
                ChatGUID = chatRunDto.Chat?.ChatGUID ?? Guid.Empty,
                NodeGUID = chatRunDto.Chat?.CurrentNodeGUID ?? Guid.Empty,
                Role = ChatRoleConstant.Assistant,
                Content = errorResultDto.UserMessage,
                Index = index,
                Error = errorMsg
            };

            chatRunDto.ChatMessages.Add(dto);

            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ErrorEvent, errorMsg));

            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            return errorMsg;
        }

        private string ErrorCodeLinkInit(int modelInstanceTypeEnum)
        {
            string res = string.Empty;
            switch (modelInstanceTypeEnum)
            {
                case (int)ModelInstanceTypeEnum.AliTextGeneration:
                    res = LLMErrCodeLinkConst.Ali;
                    break;
                case (int)ModelInstanceTypeEnum.AliOCR:
                    res = LLMErrCodeLinkConst.AliOCR;
                    break;
                case (int)ModelInstanceTypeEnum.BaiduOCR:
                    res = LLMErrCodeLinkConst.BaiduOCR;
                    break;
                default:
                    break;
            }
            return res;
        }

        public async Task<string> ExecAIChat(string systemPromptName, string input, Kernel kernel, ModelInstanceDto modelInstance, CancellationToken cancellationToken = default)
        {
            if (kernel == null) throw new ArgumentNullException(nameof(kernel));

            var promptTemplateText = EmbeddedResource.Read(systemPromptName);
            var promptTemplateFactory = new KernelPromptTemplateFactory();
            //var config = new PromptTemplateConfig(promptTemplateText);
            var promptTemplate = promptTemplateFactory.Create(new PromptTemplateConfig(promptTemplateText));
            var arguments = new KernelArguments
            {
                { nameof(input), input }
            };
            var systemPrompt = await promptTemplate.RenderAsync(kernel, arguments);

            var chatHistory = new ChatHistory(systemPrompt);
            string chatCompletionType = null;
            switch (modelInstance.ServiceTypeEnum)
            {
                case ServiceTypeEnum.TextGeneration:
                    chatCompletionType = ChatCompletionTypeDto.TextGeneration;
                    break;
                case ServiceTypeEnum.FileComprehend:
                    chatCompletionType = ChatCompletionTypeDto.FileComprehend;
                    break;
                case ServiceTypeEnum.ImageComprehend:
                    chatCompletionType = ChatCompletionTypeDto.ImageComprehend;
                    break;
            }
            var chatCompletionService = modelInstance.IsDefault ? kernel.GetRequiredService<IChatCompletionService>(chatCompletionType) :
                kernel.GetRequiredService<IChatCompletionService>(modelInstance.InstanceCode);

            var content = string.Empty;

            await foreach (var item in chatCompletionService.GetStreamingChatMessageContentsAsync(chatHistory: chatHistory, cancellationToken: cancellationToken))
            {
                if (string.IsNullOrEmpty(item.Content) == false)
                {
                    content += item.Content;
                }
            }


            return await Task.FromResult(content);
        }
    }
}

using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Application.Strategies
{
    /// <summary>
    /// 技能执行策略工厂（静态类）
    /// </summary>
    public static class SkillExecutionStrategyFactory
    {
        /// <summary>
        /// 创建技能执行策略
        /// </summary>
        /// <param name="mode">技能模式</param>
        /// <returns>技能执行策略实例</returns>
        public static ISkillExecutionStrategy Create(string mode)
        {
            if (mode == SkillModeConstant.Agent)
            {
                return new AgentSkillExecutionStrategy();
            }
            else
            {
                return new WorkflowSkillExecutionStrategy();
            }
        }
        
        /// <summary>
        /// 执行技能策略
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="kernel">内核</param>
        /// <param name="chatRunDto">聊天运行DTO</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<string> ExecuteAsync(
            IServiceProvider serviceProvider,
            Kernel kernel,
            ChatRunDto chatRunDto,
            IHttpContextAccessor httpContextAccessor,
            IMysoftContextFactory mysoftContextFactory,
            IMapper mapper,
            CancellationToken cancellationToken)
        {
            // 创建适当的策略并执行
            ISkillExecutionStrategy strategy = Create(chatRunDto.Mode);
            return await strategy.ExecuteAsync(serviceProvider, kernel, chatRunDto, httpContextAccessor, mysoftContextFactory, mapper, cancellationToken);
        }
    }
} 
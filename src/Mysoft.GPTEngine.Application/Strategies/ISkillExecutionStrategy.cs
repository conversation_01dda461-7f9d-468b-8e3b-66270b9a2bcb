using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Application.Strategies
{
    /// <summary>
    /// 技能执行策略接口
    /// </summary>
    public interface ISkillExecutionStrategy
    {
        
        
        /// <summary>
        /// 执行技能
        /// </summary>
        /// <param name="kernel">语义内核</param>
        /// <param name="chatRunDto">聊天运行数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        Task<string> ExecuteAsync(IServiceProvider serviceProvider, Kernel kernel, ChatRunDto chatRunDto, IHttpContextAccessor httpContextAccessor,
            IMysoftContextFactory mysoftContextFactory, IMapper mapper, CancellationToken cancellationToken);
    }
} 
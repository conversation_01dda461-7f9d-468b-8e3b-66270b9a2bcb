using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Plugins.OpenApi;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application
{
    public class PluginAppService : AppServiceBase
    {
        private readonly IMysoftApiService _mysoftApiService;
        private readonly IMysoftIPassApiService _mysoftIPassApiService;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly IMysoftCustomerServiceApiService _mysoftCustomerServiceApiService;
        private readonly ILogger<ApiPlugin> _logger;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;
        private Kernel _kernel;
        private readonly PluginMetadataRepostory _pluginMetadataRepostory;
        private readonly PluginRepostory _pluginRepostory;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        private readonly IConfigurationService _configurationService;

        public PluginAppService(IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, IMapper mapper, IConfigurationService configurationService,
            IMysoftApiService mysoftApiService, Kernel kernel, PluginMetadataRepostory pluginMetadataRepostory, MysoftMemoryCache mysoftMemoryCache, PluginRepostory pluginRepostory, IMysoftIPassApiService mysoftIPassApiService, MysoftConfigurationDomain mysoftConfigurationDomain,
            IMysoftCustomerServiceApiService mysoftCustomerServiceApiService, ILogger<ApiPlugin> logger)
            : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            _mysoftApiService = mysoftApiService;
            _mysoftIPassApiService = mysoftIPassApiService;
            _mysoftContextFactory = mysoftContextFactory;
            _mysoftCustomerServiceApiService = mysoftCustomerServiceApiService;
            _logger = logger;
            _mysoftConfigurationDomain = mysoftConfigurationDomain;
            _kernel = kernel;
            _pluginMetadataRepostory = pluginMetadataRepostory;
            _pluginRepostory = pluginRepostory;
            _mysoftMemoryCache = mysoftMemoryCache;
            _configurationService = configurationService;
        }
        
        public async Task<ActionResultDto> MysoftIPassApi(ApiRequestDto requestDto)
        {
            Verify.NotNull(requestDto);
            var context = _mysoftContextFactory.GetMysoftContext();
            var mipInfo = _mysoftConfigurationDomain.GetMipInfo();
            _logger.LogInformation("准备请求了：路径：{0},参数:{1}", mipInfo.ServiceUrl + requestDto.uri, JsonConvert.SerializeObject(requestDto.jsonBody));
            var result = await _mysoftIPassApiService.PostAsync(mipInfo.ServiceUrl + requestDto.uri, requestDto.jsonBody);
            _logger.LogInformation("拿到返回结果了：{0}", JsonConvert.SerializeObject(result));
            var data = JsonConvert.DeserializeObject(result);
            if (data == null) return null;
            return await Succeed(JsonConvert.SerializeObject(data));
        }

        public async Task<ActionResultDto> YkfInit(ThirdApiRequestDto thirdApiRequestDto)
        {
            PluginEntity pluginEntity = await _pluginRepostory.GetAsync(f => f.PluginGUID == thirdApiRequestDto.PluginGUID);
            PluginMetadataEntity pluginMetadataEntity = await _pluginMetadataRepostory.GetAsync(f => f.PluginGUID == thirdApiRequestDto.PluginGUID);
            if (pluginEntity == null || pluginMetadataEntity == null) return null;
            var arguments = new KernelArguments();
            thirdApiRequestDto.Arguments.ForEach(item => { arguments[item.Key.Replace("System_Keyword_", "")] = item.Value; });
            arguments["CustomerId"] = thirdApiRequestDto.Arguments.Find(f=>f.Key == "System_Keyword_CustomerGUID").Value;
            arguments["CustomerName"] = thirdApiRequestDto.Arguments.Find(f => f.Key == "System_Keyword_CustomerName").Value;

            OpenApiRequestOptions openApiRequestOptions = new OpenApiRequestOptions()
            {
                Metadata = pluginMetadataEntity.Metadata,
                AuthMode = pluginEntity.AuthMode,
                CreateTokenUrl = pluginEntity.CreateTokenUrl,
                ParamListStr = pluginEntity.ParamList,
                // Path = "tianjiInit",
                Path = "/v20/app/agent/messageinfo/tianjiInit.svc",
                Arguments = arguments
            };
            
            var result = await OpenApiServiceHelper.RequestApi(openApiRequestOptions, _mysoftMemoryCache, _configurationService, _httpContextAccessor);
            object data = ReadValue(result);
            return await Succeed(JsonConvert.SerializeObject(data));
        }

        private object ReadValue(FunctionResult result)
        {
            object obj = result.GetValue<RestApiOperationResponse>()!.Content!;
            var data = obj == null ? "" : JsonConvert.DeserializeObject(obj.ToString());
            return data;
        }
        

        public async Task<List<PluginApiInfoDto>> ApiInfoParse(string metadata, MemoryStream stream)
        {
            List<PluginApiInfoDto> pluginApiInfoDtos = new List<PluginApiInfoDto>();
            var plugin = await this._kernel.CreatePluginFromOpenApiAsync("OpenApiService", stream);
            foreach (var item in plugin.GetFunctionsMetadata())
            {
                PluginApiInfoDto pluginApiInfoDto = new PluginApiInfoDto();
                pluginApiInfoDto.MetaData = metadata;
                pluginApiInfoDto.Name = item.Description;
                pluginApiInfoDto.Describe = item.Description;
                pluginApiInfoDto.OperationId = item.Name;
                pluginApiInfoDto.Method = TryGetFromAdditionalProperties<string>(item.AdditionalProperties, "method");
#pragma warning disable SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                RestApiOperation operation = TryGetFromAdditionalProperties<RestApiOperation>(item.AdditionalProperties, "operation");
#pragma warning restore SKEXP0040 // 类型仅用于评估，在将来的更新中可能会被更改或删除。取消此诊断以继续。
                pluginApiInfoDto.Path = operation.Path;
                pluginApiInfoDto.Code = operation.Path + ":" + pluginApiInfoDto.Method;
                foreach (var parameter in item.Parameters)
                {
                    ApiParam apiParam = new ApiParam();
                    apiParam.Name = parameter.Name;
                    apiParam.Describe = parameter.Description;
                    apiParam.Required = parameter.IsRequired;
                    apiParam.Id = parameter.Name;
                    parameter.Schema.RootElement.TryGetProperty("type", out JsonElement type);
                    apiParam.Type = type.GetString();
                    apiParam.Show = true;
                    var operationParam = operation.Parameters.FirstOrDefault(p => p.Name == parameter.Name);
                    if (operationParam != null)
                    {
                        apiParam.ParamType = operationParam.Location.ToString();
                    }
                    else
                    {
                        apiParam.ParamType = "body";
                    }

                    pluginApiInfoDto.Params.Add(apiParam);
                }

                // todo 对象类型的返回值 没有名称
                foreach (var res in operation.Responses)
                {
                    var restApiExpectedResponse = res.Value;
                    var root = restApiExpectedResponse.Schema.RootElement;
                    var scheme = ParseJsonSchema(root);
                    if (scheme.Count > 0)
                    {
                        pluginApiInfoDto.Results.AddRange(scheme);
                    }
                }
                // 如果返回对象类型是text/event-stream，会解析不到返回参数，手动新增一条
                if (pluginApiInfoDto.Results.Count == 0)
                {
                    ApiParam outputParam = new ApiParam();
                    outputParam.Name = "output";
                    outputParam.Type = "string";
                    outputParam.Id = Guid.NewGuid().ToString();
                    pluginApiInfoDto.Results.Add(outputParam);
                }
                pluginApiInfoDtos.Add(pluginApiInfoDto);
            }
            return pluginApiInfoDtos;
        }

        public async Task<ActionResultDto> GetApiInfoByMetadata(string pluginGUID)
        {
            List<PluginApiInfoDto> pluginApiInfoDtos = new List<PluginApiInfoDto>();
            try
            {
                PluginMetadataEntity pluginMetadataEntity = await _pluginMetadataRepostory.GetAsync(f => f.PluginGUID == pluginGUID);
                if(pluginMetadataEntity == null) return await Succeed(pluginApiInfoDtos);
                byte[] byteArray = Encoding.UTF8.GetBytes(pluginMetadataEntity.Metadata);
                using (MemoryStream stream = new MemoryStream(byteArray))
                {
                    stream.Position = 0;
                    pluginApiInfoDtos = await ApiInfoParse(pluginMetadataEntity.Metadata, stream);
                }
                return await Succeed(pluginApiInfoDtos);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return await Failed("接口文件解析失败");
            }
        }

        public async Task<ActionResultDto> GetFunctionsMetadata(string downloadUrl)
        {
            List<PluginApiInfoDto> pluginApiInfoDtos = new List<PluginApiInfoDto>();
            try
            {
                byte[] decodedBytes = Convert.FromBase64String(downloadUrl);
                // 将字节数组转换回原始字符串
                string url = Encoding.UTF8.GetString(decodedBytes);
                using (WebClient client = new WebClient())
                {
                    byte[] fileBytes = client.DownloadData(url); // 下载文件
                    using (MemoryStream stream = new MemoryStream(fileBytes)) // 将文件转换为文件流
                    {
                        // 确保流的位置是在开始处
                        stream.Position = 0;
                        using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                        {
                            string content = reader.ReadToEnd();
                            Console.WriteLine(content);
                            stream.Position = 0;
                            pluginApiInfoDtos = await ApiInfoParse(content, stream);
                        }
                        return await Succeed(pluginApiInfoDtos);
                    }
                }
            }
            catch(Exception ex)
            {
                Console.WriteLine(ex.Message);
                return await Failed("接口文件解析失败");
            }
        }

        public static List<ApiParam> ParseJsonSchema(JsonElement schema)
        {
            var apiParams = new List<ApiParam>();

            if (schema.ValueKind == JsonValueKind.Object)
            {
                // Get required fields list if available
                HashSet<string> requiredFields = null;
                if (schema.TryGetProperty("required", out JsonElement required))
                {
                    requiredFields = new HashSet<string>();
                    foreach (var req in required.EnumerateArray())
                    {
                        requiredFields.Add(req.GetString());
                    }
                }

                // Check if this is an object with properties
                if (schema.TryGetProperty("properties", out JsonElement properties))
                {
                    // Iterate through each property and create ApiParam instances
                    foreach (JsonProperty property in properties.EnumerateObject())
                    {
                        var param = CreateApiParam(property.Value, property.Name, requiredFields);
                        apiParams.Add(param);

                        // Recursively parse child properties if the type is "object" or "array"
                        if (property.Value.TryGetProperty("type", out JsonElement type) && type.GetString() == "object")
                        {
                            param.Child = ParseJsonSchema(property.Value);
                        }
                        else if (type.GetString() == "array" && property.Value.TryGetProperty("items", out JsonElement items))
                        {
                            param.Child = ParseJsonSchema(items);
                        }
                    }
                }
                else if (schema.TryGetProperty("type", out JsonElement typeElement) && typeElement.GetString() == "array")
                {
                    // Handle arrays by parsing the items schema
                    if (schema.TryGetProperty("items", out JsonElement items))
                    {
                        var itemParams = ParseJsonSchema(items);
                        foreach (var itemParam in itemParams)
                        {
                            apiParams.Add(itemParam);
                        }
                    }
                }
            }

            return apiParams;
        }

        private static ApiParam CreateApiParam(JsonElement propertySchema, string propertyName, HashSet<string> requiredFields)
        {
            var param = new ApiParam
            {
                Name = propertyName,
                Describe = propertySchema.TryGetProperty("description", out JsonElement desc) ? desc.GetString() : "",
                Type = propertySchema.TryGetProperty("type", out JsonElement type) ? type.GetString() : "unknown",
                Required = requiredFields != null && requiredFields.Contains(propertyName),
                IsShow = true,
                Id = Guid.NewGuid().ToString(),
                Child = new List<ApiParam>()
            };

            // Handle readOnly attribute if present
            if (propertySchema.TryGetProperty("readOnly", out JsonElement readOnly) && readOnly.GetBoolean())
            {
                param.IsShow = false;
            }

            return param;
        }

        private T TryGetFromAdditionalProperties<T>(ReadOnlyDictionary<string,object?> additionalProperties,string key)
        {
            if (additionalProperties.TryGetValue(key, out var methodValue))
            {
                Type type = typeof(T);
                if (type == typeof(string))
                {
                    return (T)(object)(methodValue?.ToString() ?? string.Empty);
                }
                else
                {
                    return (T)(object)methodValue;
                }
            }
            return default(T);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Aspose.Cells;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Entity.eval;
using Mysoft.GPTEngine.Domain.Repositories.eval;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Newtonsoft.Json;
using SqlSugar.Extensions;

namespace Mysoft.GPTEngine.Application
{
    /// <summary>
    /// 测评
    /// </summary>
    public class EvalAppService : AppServiceBase
    {

        private readonly EvalTaskRepostory _evalTaskRepostory;
        
        private readonly EvalTaskRecordRepostory _evalTaskRecordRepostory;
        
        private readonly PlanEvalResultRepostory _planEvalResultRepostory;

        private readonly ILogger<EvalAppService> _logger;
        
        private AgentAppService _service;
        
        private static readonly Guid DefaultEvalPlanSkillGUID = Guid.Parse("b199b3b2-ea9c-41a5-9298-4dd77c7c289f");

        public EvalAppService(IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, IMapper mapper
            , ILogger<EvalAppService> logger , EvalTaskRepostory evalTaskRepostory
            , EvalTaskRecordRepostory evalTaskRecordRepostory, PlanEvalResultRepostory planEvalResultRepostory
            , AgentAppService service
            ) : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            _evalTaskRepostory = evalTaskRepostory;
            _evalTaskRecordRepostory = evalTaskRecordRepostory;
            _planEvalResultRepostory = planEvalResultRepostory;
            _service = service;
            _logger = logger;
        }

        public async Task<ActionResultDto> ExecutePlan(EvalTaskRecordDto<Dictionary<string, RuleDto>> evalTaskRecordDto,
            CancellationToken cancellationToken)
        {
            var errorMsg = "";
            var execStatus = (int)ApprovalPlanStatusEnum.Success;
            List<PlanEvalResultDto> list = GetPlanResultByExcel(evalTaskRecordDto);
            var total = 0;
            var successCount = 0;
            var conclusionCount = 0;
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                for (var i = 1; i <= evalTaskRecordDto.ExecCount; i++)
                {
                    foreach (var item in list)
                    {
                        total += 1;
                        var entity = await ExecutePlanRule(item, i, evalTaskRecordDto, cancellationToken);
                        if (entity.EvalConclusion == "通过") successCount += 1;
                        if (entity.EvalConclusion != "结论不通过") conclusionCount += 1;
                    }
                }

            }
            catch (Exception e)
            {
                _logger.LogError(e, "测评任务 [{0}] 执行失败", evalTaskRecordDto.EvalTaskRecordGUID);
                execStatus = (int)ApprovalPlanStatusEnum.Fail;
                errorMsg = e.Message;
            }
            finally
            {
                stopwatch.Stop();
                var executionTime = stopwatch.ElapsedMilliseconds.ObjToInt();
                await _evalTaskRecordRepostory.UpdateAsync(e => new EvalTaskRecordEntity
                    {
                        ExecStatus = execStatus,
                        ErrorMsg = errorMsg,
                        ModifiedTime = TimeZoneUtility.LocalNow(),
                        SuccessCount = successCount,
                        Total = total,
                        ConclusionCount = conclusionCount,
                        ExecutionTime = executionTime
                    }, 
                    f => f.EvalTaskRecordGUID == evalTaskRecordDto.EvalTaskRecordGUID);

                await _evalTaskRepostory.UpdateAsync(e => new EvalTaskEntity
                {
                    Status = execStatus
                }, f => f.LastRecordGUID == evalTaskRecordDto.EvalTaskRecordGUID);
            }
            
            return await Task.FromResult(new ActionResultDto() { Success = true, Message = "" });
        }

        private async Task<PlanEvalResultEntity> ExecutePlanRule(PlanEvalResultDto item, int i, EvalTaskRecordDto<Dictionary<string, RuleDto>> evalTaskRecordDto, CancellationToken cancellationToken)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            PlanEvalResultEntity entity = CreatePlanEvalResult(item, i);
            try
            {

                var chatInput = CreateChatInput(item);
                entity.ChatGUID = chatInput.ChatGUID.ToString();

                var actionResultDto = await _service.ChatCompletionAsync(chatInput, false, cancellationToken)
                    .ConfigureAwait(false);
                if (!actionResultDto.Success)
                {
                    throw new Exception(actionResultDto.Message);
                }

                var actionResultStr = "";
                if (actionResultDto is ActionResultDto<string>)
                {
                    actionResultStr = ((ActionResultDto<string>)actionResultDto).Data;
                }

                Dictionary<string, string> result = JsonConvert.DeserializeObject<Dictionary<string, string>>(actionResultStr);

                var ruleDto = FormatResult(result["output"]);
                CompEvalConclusion(ruleDto, entity);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "测评方案 [{0}][{1}] 执行失败", entity.PlanName, entity.RuleName);
                entity.EvalConclusion = "结论不通过";
                entity.ActualNotPassResult = e.Message;
                entity.ActualResultStatus = "不通过";
            }
            finally
            {
                stopwatch.Stop();
                entity.EvalTaskRecordGUID = evalTaskRecordDto.EvalTaskRecordGUID;
                entity.EvalTaskGUID = evalTaskRecordDto.EvalTaskGUID;
                entity.ExecutionTime = stopwatch.ElapsedMilliseconds.ObjToInt();
                // entity.ChatGUID = "";
                await _planEvalResultRepostory.InsertAsync(entity);
            }

            return await Task.FromResult(entity);
        }

        private void CompEvalConclusion(RuleDto ruleDto, PlanEvalResultEntity entity)
        {
            entity.ActualResultStatus = ruleDto.Result == 1 ? "通过" : "不通过";
            entity.ActualNotPassResult = ruleDto.Details;
            if ((entity.ActualResultStatus == entity.ExpectedResultAmount && ruleDto.Result == 1)
                ||(entity.ActualResultStatus == entity.ExpectedResultAmount
                   && entity.ActualNotPassResult == entity.ExpectedFailResult))
            {
                entity.EvalConclusion = "通过";
                return;
            }
            
            if (entity.ActualResultStatus != entity.ExpectedResultAmount)
            {
                entity.EvalConclusion = "结论不通过";
                return;
            }

            var actual = FormatMultipleResult(entity.ActualNotPassResult);
            var expected = FormatMultipleResult(entity.ExpectedFailResult);
            entity.EvalConclusion = actual.SetEquals(expected) ? "通过" : "详情不通过";
        }

        private HashSet<string> FormatMultipleResult(string result)
        {
            var list = new HashSet<string>();
            try
            {
                list = JsonConvert.DeserializeObject<HashSet<string>>(result);
                if (list == null) return new HashSet<string>();
            }
            catch (Exception e)
            {
                _logger.LogError(e,"结果格式化异常FormatMultipleResult：{0}", result);
                list.Add(result);
            }
            return list;
        }

        public PlanEvalResultEntity CreatePlanEvalResult(PlanEvalResultDto planEvalResultDto, int batchNo)
        {
            return new PlanEvalResultEntity()
            {
                PlanEvalResultGUID = Guid.NewGuid().ToString(),
                PlanName = planEvalResultDto.PlanName,
                PlanCode = planEvalResultDto.PlanCode,
                RuleName = planEvalResultDto.RuleName,
                TaskName = planEvalResultDto.TaskName,
                ExpectedResultAmount = planEvalResultDto.ExpectedResultAmount,
                ExpectedFailResult = planEvalResultDto.ExpectedFailResult,
                EvalTaskRecordGUID = planEvalResultDto.EvalTaskRecordGUID,
                EvalTaskGUID = planEvalResultDto.EvalTaskGUID,
                BatchNo = batchNo
            };
        }

        private ChatInputDto CreateChatInput(PlanEvalResultDto dto)
        {
            
            return new ChatInputDto()
            {
                Input = "",
                ChatGUID = Guid.NewGuid(),
                AssistanGUID = Guid.Parse(MysoftConstant.DefaultAssistant),
                SkillGUID = DefaultEvalPlanSkillGUID,
                Arguments = new List<KeyValueDto>()
                {
                    new KeyValueDto()
                    {
                        Key = "ruleCheckContent",
                        Value = dto.RuleContent
                    },
                    new KeyValueDto()
                    {
                        Key = "ruleCheckData",
                        Value = dto.RuleData
                    },
                    new KeyValueDto()
                    {
                        Key = "ruleCheckFile",
                        Value = dto.RuleDataFile
                    }
                }
            };
        } 

        public List<PlanEvalResultDto> GetPlanResultByExcel(EvalTaskRecordDto<Dictionary<string, RuleDto>> evalTaskRecordDto)
        {
            MemoryStream stream = GetFileByUrl(evalTaskRecordDto.DocumentUrl);

            var result = new List<PlanEvalResultDto>();
            
            AsposeHelper.InitAspose();
            using (stream)
            {
                Workbook workbook = new Workbook(stream);
                Worksheet worksheet = workbook.Worksheets[0];
                
                var maxRow = worksheet.Cells.MaxDataRow;
                var maxCol = worksheet.Cells.MaxDataColumn;
                if (maxCol < 6)
                {
                    throw new BusinessException("文档格式错误");
                }
                for (var i = 1; i <= maxRow; i++)
                {
                    var row = worksheet.Cells.GetRow(i);
                    if (row.IsBlank) continue;
                    var ruleName = row[2].StringValue;
                    var rule = evalTaskRecordDto.Data[ruleName];
                    PlanEvalResultDto planEvalResultDto = new PlanEvalResultDto();
                    planEvalResultDto.TaskName = evalTaskRecordDto.TaskName;
                    planEvalResultDto.PlanName = row[0].StringValue;
                    planEvalResultDto.PlanCode = row[1].StringValue;
                    planEvalResultDto.RuleName = row[2].StringValue;
                    planEvalResultDto.RuleContent = rule != null ? rule.RuleCheckContent : "";
                    planEvalResultDto.RuleData = row[3].StringValue;
                    planEvalResultDto.RuleDataFile = row[4].StringValue;
                    planEvalResultDto.ExpectedResultAmount = row[5].StringValue;
                    planEvalResultDto.ExpectedFailResult = row[6].StringValue;
                    result.Add(planEvalResultDto);
                    
                }
            }

            return result;

        }
        
        public RuleDto FormatResult(string result)
        {
            if (JsonUtility.TryDeserializeJsonStringObjectList<List<ExcuteResultDto>>(result, out var excuteResultDtos))
            {
                RuleDto rule = new RuleDto();
                rule.Result = (int)ApprovalPlanConformityResultEnum.Conform;
                var resultList = excuteResultDtos.Where(dto => dto.Status != null).ToList();
                if (resultList.Any(dto => !dto.Status ?? true ))
                {
                    rule.Result = (int)ApprovalPlanConformityResultEnum.NonConform;
                    rule.Details = JsonConvert.SerializeObject(resultList
                        .Where(dto => !dto.Status ?? true)
                        .Select(dto => dto.Message));
                }
                else
                {
                    rule.Result = (int)ApprovalPlanConformityResultEnum.Conform;
                }
                return rule;
            }
            throw new BusinessException("大模型返回数据格式化异常：" + result);
        }
        
        
        private MemoryStream GetFileByUrl(string downloadUrl)
        {
            //解析一下文本
            using (WebClient client = new WebClient())
            {
                byte[] fileBytes = client.DownloadData(downloadUrl); // 下载文件

                MemoryStream stream = new MemoryStream(fileBytes);

                return stream;
            }
        } 
        
    }
}
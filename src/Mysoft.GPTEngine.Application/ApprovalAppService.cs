using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Entity.Approval;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Repositories.Approval;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog.Context;
using Tea.Utils;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Mysoft.GPTEngine.Application
{
    /// <summary>
    /// 智能审批
    /// </summary>
    public class ApprovalAppService : AppServiceBase
    {
        private readonly Kernel _kernel;
        private readonly ApprovalDomainService _approvalDomainService;
        private readonly IServiceProvider _serviceProvider;
        private readonly PlanInstanceRepostory _planInstanceRepostory;
        private readonly PlanDatasourceInstanceRepostory _planDatasourceInstanceRepostory;
        private readonly ChatMessageNodeLogEntityRepostory _chatMessageNodeLogEntityRepostory;
        private readonly ILogger<ApprovalAppService> _logger;
        
        public ApprovalAppService(IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, IMapper mapper
            , IServiceProvider serviceProvider, ILogger<ApprovalAppService> logger
            , Kernel kernel, PlanInstanceRepostory planInstanceRepostory, PlanDatasourceInstanceRepostory planDatasourceInstanceRepostory
            , ChatMessageNodeLogEntityRepostory chatMessageNodeLogEntityRepostory
            , ApprovalDomainService approvalDomainService) : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            _approvalDomainService = approvalDomainService;
            _kernel = kernel;
            _serviceProvider = serviceProvider;
            _planInstanceRepostory = planInstanceRepostory;
            _planDatasourceInstanceRepostory = planDatasourceInstanceRepostory;
            _chatMessageNodeLogEntityRepostory = chatMessageNodeLogEntityRepostory;
            _logger = logger;
        }

        /// <summary>
        /// 执行审批方案
        /// </summary>
        /// <param name="executePlanJobDto"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> ExecutePlan(ExecutePlanJobDto executePlanJobDto, CancellationToken cancellationToken)
        {
            try
            {
                Verify.NotNull(executePlanJobDto);
                executePlanJobDto.PlanInstanceInfoDto.Status = (int)ApprovalPlanStatusEnum.Running;
                await UpdatePlanStatusDto(executePlanJobDto.PlanInstanceInfoDto);

                FormatBusinessData(executePlanJobDto.PlanInstanceInfoDto);
                InitParamsValueMapping(executePlanJobDto.PlanInstanceInfoDto);

                await _approvalDomainService.ExecutePlan(executePlanJobDto, _kernel);

                executePlanJobDto.PlanInstanceInfoDto.Status = (int)ApprovalPlanStatusEnum.Success;
                
                UpdateResult(executePlanJobDto.PlanInstanceInfoDto);
            }
            catch (Exception e)
            {
                var logId = Guid.NewGuid().ToString().Replace("-","");
                executePlanJobDto.PlanInstanceInfoDto.Status = (int)ApprovalPlanStatusEnum.Fail;
                executePlanJobDto.PlanInstanceInfoDto.ErrorMessage = logId;
                using (LogContext.PushProperty("LogId", logId))
                {
                    _logger.LogError(e, "执行审批方案失败");
                }
            }
            finally
            {
                await UpdatePlanStatusDto(executePlanJobDto.PlanInstanceInfoDto);
                await SavePlanActivityNodeLog(executePlanJobDto);
            }
            
            return await Task.FromResult(new ActionResultDto() { Success = true, Message = "" });
        }
        

        public void InitParamsValueMapping(PlanInstanceInfoDto planInstanceInfoDto)
        {
            if (planInstanceInfoDto.ParamsValue != null)
            {
                using (var doc = JsonDocument.Parse(JsonSerializer.Serialize(planInstanceInfoDto.ParamsValue)))
                {
                    planInstanceInfoDto.ParamsValue = JsonConvert.DeserializeObject<Dictionary<string, object>>(doc.RootElement.GetRawText());
                }
            }

            if (planInstanceInfoDto.ParamsValue == null)
            {
                planInstanceInfoDto.ParamsValue = new Dictionary<string, object>();
            }
            planInstanceInfoDto.ParamsValueMapping = new Dictionary<string, ParamsValueDto>();
            if (planInstanceInfoDto.ParamsMapping != null && planInstanceInfoDto.ParamsValueMapping != null)
            {
                foreach (var dto in planInstanceInfoDto.ParamsMapping)
                {
                
                    var paramsValueDto = new ParamsValueDto()
                    {
                        ParamsId = dto.ParamsId,
                        ParamsName = dto.ParamsName,
                        Value = planInstanceInfoDto.ParamsValue.Get(dto.ParamsId),
                        ParamsType = dto.ParamsType
                    };

                    SetFlowData(paramsValueDto, planInstanceInfoDto);
                    if (!string.IsNullOrEmpty(dto.MappingDatasource))
                    {
                        var ms = dto.MappingDatasource.Split(",");
                        foreach (var str in ms)
                        {
                            planInstanceInfoDto.ParamsValueMapping.TryAdd(str, paramsValueDto);
                        }
                    }
                    
                }
            }
        }

        private void SetFlowData(ParamsValueDto paramsValueDto, PlanInstanceInfoDto planInstanceInfoDto)
        {
            if (planInstanceInfoDto.UseFlow == 1 && (paramsValueDto.Value==null || string.IsNullOrEmpty(paramsValueDto.Value.ToString())))
            {
                if (paramsValueDto.ParamsId == "flow_data" && !string.IsNullOrEmpty(planInstanceInfoDto.BusinessFormatData))
                {
                    paramsValueDto.Value = planInstanceInfoDto.BusinessFormatData;
                }
                if (paramsValueDto.ParamsId == "flow_attachments" && planInstanceInfoDto.Attachments != null && planInstanceInfoDto.Attachments.Count > 0)
                {
                    paramsValueDto.Value = planInstanceInfoDto.Attachments;
                }
                if (paramsValueDto.ParamsId == "flow_business_oid" && !string.IsNullOrEmpty(planInstanceInfoDto.BusinessData))
                {
                    paramsValueDto.Value = GetFlowOid(planInstanceInfoDto.BusinessData);
                }
            }
        }

        private string GetFlowOid(string data)
        {
            try
            {
                var jObject = JObject.Parse(data);
                ((JObject)jObject["main"]).TryGetValue("business_id", out var businessId);
                return businessId.ToString();
            }
            catch (Exception e)
            {
                _logger.LogError("从流程数据中获取业务数据ID失败", e);
            }

            return "";
        }

        public void UpdateResult(PlanInstanceInfoDto planInstanceInfoDto)
        {
            bool isFail = true;
            if (planInstanceInfoDto.PlanMode == (int)ApprovalPlanModeEnum.Conformity)
            {
                isFail = planInstanceInfoDto.RuleGroups.Any(x => x.Rules.Any(y => y.Result != (int)ApprovalPlanConformityResultEnum.Conform));
            }
            else if (planInstanceInfoDto.PlanMode == (int)ApprovalPlanModeEnum.RiskLevel)
            {
                isFail = planInstanceInfoDto.RuleGroups.Any(x => x.Rules.Any(y => y.Result != (int)ApprovalPlanRiskLevelResultEnum.Pass));
            }

            if (isFail)
            {
                planInstanceInfoDto.Result = 0;
            }
            else
            {
                planInstanceInfoDto.Result = 1;
            }
        }

        public void FormatBusinessData(PlanInstanceInfoDto planInstanceInfoDto)
        {
            if (string.IsNullOrEmpty(planInstanceInfoDto.BusinessData) ||
                string.IsNullOrEmpty(planInstanceInfoDto.BusinessDataSchema))
            {
                planInstanceInfoDto.BusinessFormatData = planInstanceInfoDto.BusinessData;
                return;
            }

            var result = JsonSchemaHelper.ReplaceJsonDataDescriptions(planInstanceInfoDto.BusinessDataSchema, planInstanceInfoDto.BusinessData);

            planInstanceInfoDto.BusinessFormatData = result;
        }
        
        public async Task UpdatePlanStatusDto(PlanInstanceInfoDto planInstanceInfoDto)
        {
            await _planInstanceRepostory.UpdateAsync(e => new PlanInstanceEntity
                {
                    Status = planInstanceInfoDto.Status,
                    ErrorMessage = planInstanceInfoDto.ErrorMessage,
                    ResultSummary = planInstanceInfoDto.ResultSummary,
                    Result = planInstanceInfoDto.Result,
                    BusinessId = planInstanceInfoDto.BusinessId,
                    BusinessFormatData = planInstanceInfoDto.BusinessFormatData,
                    ModifiedTime = TimeZoneUtility.LocalNow()
                }, 
                f => f.PlanInstanceGUID == planInstanceInfoDto.PlanInstanceGUID);
        }
        
        private async Task SavePlanActivityNodeLog(ExecutePlanJobDto executePlanJobDto)
        {
            if (executePlanJobDto.PlanMessageNodeLogDto == null || executePlanJobDto.PlanMessageNodeLogDto.BatchGUID == Guid.Empty) return;
            
            List<ChatMessageNodeLogEntity> chatMessageNodeLogEntities = await _chatMessageNodeLogEntityRepostory.GetListAsync(x =>
                x.BatchGUID == executePlanJobDto.PlanMessageNodeLogDto.BatchGUID
                && x.NodeGUID == executePlanJobDto.PlanMessageNodeLogDto.NodeGUID
                && x.Name == executePlanJobDto.PlanMessageNodeLogDto.Name);

            if (chatMessageNodeLogEntities != null && chatMessageNodeLogEntities.Count > 0)
            {
                Dictionary<string, object> map = new Dictionary<string, object>();
                map.Add("result", executePlanJobDto.PlanInstanceInfoDto.Result);
                map.Add("ruleGroups", executePlanJobDto.PlanInstanceInfoDto.RuleGroups);
                ChatMessageNodeLogEntity entity = chatMessageNodeLogEntities[0];
                entity.EndTime = TimeZoneUtility.LocalNow();
                entity.Outputs = JsonConvert.SerializeObject(map);
                await _chatMessageNodeLogEntityRepostory.UpdateAsync(entity);
            } 
        }
        
    }
}

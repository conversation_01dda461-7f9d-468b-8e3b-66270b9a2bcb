namespace Mysoft.GPTEngine.Application.Extensions
{
    public static class ChatHistoriesExtension
    {
        //public static ChatHistory CreateChatHistor(this ChatDto chat, string systemMessage = null)
        //{
        //    var chatHistory = systemMessage == null ? [] : new ChatHistory(systemMessage);
        //    foreach (var item in chat.ChatHistories)
        //    {
        //        switch (item.Role)
        //        {
        //            case ChatRole.System:
        //                chatHistory.AddSystemMessage(item.Content);
        //                break;
        //            case ChatRole.User:
        //                chatHistory.AddUserMessage(item.Content);
        //                break;
        //            case ChatRole.Assistant:
        //                chatHistory.AddAssistantMessage(item.Content);
        //                break;
        //            default: break;
        //        }

        //    }
        //    return chatHistory;
        //}
    }
}

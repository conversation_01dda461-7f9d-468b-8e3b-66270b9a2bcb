using Microsoft.Extensions.DependencyInjection;
using Mysoft.GPTEngine.Application.Heartbeat;

namespace Mysoft.GPTEngine.Application.Extensions
{
    public static class HeartbeatServiceExtensions
    {
        public static IServiceCollection AddHeartbeatService(this IServiceCollection services)
        {
            services.AddScoped<IHeartbeatStrategy, SSEHeartbeatStrategy>();
            services.AddScoped<HeartbeatService>();
            return services;
        }
    }
}
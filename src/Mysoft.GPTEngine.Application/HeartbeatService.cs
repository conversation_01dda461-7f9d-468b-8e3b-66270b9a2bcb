using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application.Heartbeat;

namespace Mysoft.GPTEngine.Application
{
    public class HeartbeatService
    {
        private readonly IHeartbeatStrategy _strategy;
        private readonly ILogger<HeartbeatService> _logger;

        public HeartbeatService(IHeartbeatStrategy strategy, ILogger<HeartbeatService> logger)
        {
            _strategy = strategy;
            _logger = logger;
        }

        public async Task StartHeartbeat(HttpResponse response, CancellationToken token)
        {
            while (!token.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(_strategy.GetHeartbeatInterval(), token);
                    await _strategy.SendHeartbeat(response, token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "心跳服务异常");
                    break;
                }
            }
        }
    }
}
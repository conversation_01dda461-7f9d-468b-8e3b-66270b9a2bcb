<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
      <NoWarn>$(NoWarn);SKEXP0001,SKEXP0060</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Dao\**" />
    <EmbeddedResource Remove="Dao\**" />
    <None Remove="Dao\**" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Milvus" Version="1.60.0-alpha" />
    <PackageReference Include="Microsoft.SemanticKernel.Planners.Handlebars" Version="1.47.0-preview" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.Core" Version="1.60.0-preview" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.OpenApi" Version="1.60.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mysoft.GPTEngine.Application.Contracts\Mysoft.GPTEngine.Application.Contracts.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Domain.Shared\Mysoft.GPTEngine.Domain.Shared.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Domain\Mysoft.GPTEngine.Domain.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Plugin\Mysoft.GPTEngine.Plugin.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.SemanticKernel.Core\Mysoft.GPTEngine.SemanticKernel.Core.csproj" />
  </ItemGroup>

</Project>

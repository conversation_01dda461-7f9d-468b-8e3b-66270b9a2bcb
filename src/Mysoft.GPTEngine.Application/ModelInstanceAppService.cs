using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.SemanticKernel.Embeddings;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Mysoft.GPTEngine.SemanticKernel.Core.Baidu;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Extensions;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen;
using Mysoft.GPTEngine.SemanticKernel.Core.Xunfei;
using Newtonsoft.Json;
using BinaryContent = Mysoft.GPTEngine.SemanticKernel.Core.Contents.BinaryContent;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Mysoft.GPTEngine.Application
{
    /// <summary>
    /// 模型实例应付服务
    /// </summary>
    public class ModelInstanceAppService : AppServiceBase
    {
        // private readonly Kernel _kernel;
        private readonly ModelRepostory _modelRepostory;
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        private readonly IKernelBuilder _kernelBuilder;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        private readonly ISqlSugarProviderFactory _sqlSugarProviderFactory;
        private readonly SensitiveWordsRepostory _senitiveWordsRepostory;
        public ModelInstanceAppService(IHttpContextAccessor httpContextAccessor, SensitiveWordsRepostory sensitiveWordsRepostory, MysoftMemoryCache mysoftMemoryCache, ModelRepostory modelRepostory, ModelInstanceRepostory modelInstanceRepostory, ISqlSugarProviderFactory sqlSugarProviderFactory, IMysoftContextFactory mysoftContextFactory, MysoftApiService mysoftApiDomainService,  IMapper mapper, ILogger<ModelInstanceAppService> logger) : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            // _kernel = kernel;
            _modelRepostory = modelRepostory;
            _modelInstanceRepostory = modelInstanceRepostory;
            _sqlSugarProviderFactory = sqlSugarProviderFactory;
            _kernelBuilder = Kernel.CreateBuilder();
            _mysoftContextFactory = mysoftContextFactory;
            _mysoftApiDomainService = mysoftApiDomainService;
            _mysoftMemoryCache = mysoftMemoryCache;
            _senitiveWordsRepostory = sensitiveWordsRepostory;
        }
        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="modelInstance"></param>
        /// <returns></returns>
        public async Task<ActionResultDto> TestConnection(MysoftActionDto actionDto, CancellationToken cancellationToken)
        {
            var modelInstanceDtos = actionDto.ModelInstanceDtos[0];
            var modelEntity = await _modelRepostory.GetFirstAsync((x => x.ModelGUID == modelInstanceDtos.ModelGUID));
            modelInstanceDtos.ModelType = modelEntity.ModelType;
            modelInstanceDtos.ServiceTypeEnum = modelEntity.ServiceTypeEnum;
            modelInstanceDtos.InstanceCode = modelEntity.ModelCode;
            modelInstanceDtos.ModelCode = !string.IsNullOrEmpty(modelInstanceDtos.CustomModelCode) ? modelInstanceDtos.CustomModelCode : modelEntity.ModelCode;
            if (modelInstanceDtos.InstanceGUID != null)
            {
                modelInstanceDtos.ApiKey = AesHelper.Decrypt(modelInstanceDtos.ApiKey);
            }

            switch (modelInstanceDtos.ServiceTypeEnum)
                {
                    case ServiceTypeEnum.Embedding:
                        return await EmbeddingTest(actionDto);
                        break;
                    case ServiceTypeEnum.OcrRecognizeService:
                        // 特殊处理
                        if (modelInstanceDtos.ModelCode == "ocr_recognize_vl")
                        {
                            return await MultiModalTest(actionDto, cancellationToken);
                        }
                        return await OcrRecognizeTest(actionDto);
                        break;
                    case ServiceTypeEnum.FileComprehend:
                        return await MultiFileModalTest(actionDto, cancellationToken);
                        break;
                    case ServiceTypeEnum.ContentReview:
                        return await ContentReviewModalTest(actionDto, cancellationToken);
                        break;
                    default: 
                        if (1 == actionDto.ModelInstanceDtos[0].isImg)
                            return await MultiModalTest(actionDto, cancellationToken);
                        if (actionDto.ModelInstanceDtos[0].IsSupportTool == 1)
                            return await FunctionCallTest(actionDto);
                        return await TextTest(actionDto);
                        break;
                }
        }

        public async Task<ActionResultDto> TextTest(MysoftActionDto actionDto)
        {
            await _kernelBuilder.AddChatCompletionServices(actionDto.ModelInstanceDtos);

            var kernel = _kernelBuilder.Build();
            var function = kernel.CreateFunctionFromPrompt(new PromptTemplateConfig()
            {
                Template = @"Hello",
                ExecutionSettings =
                {
                    {
                        actionDto.ModelInstanceDtos[0].InstanceCode,
                        new OpenAIPromptExecutionSettings()
                        {
                            MaxTokens = 10,
                            Temperature = 0
                        }
                    }
                }
            });
            try
            {
                var result = await kernel.InvokeAsync(function, actionDto.CreateKernelArguments());
            }
            catch (HttpOperationException ex)
            {
                return await Failed("模型连接测试失败:" + ex.ResponseContent);
            }
            catch (Exception ex)
            {
                return await Failed("模型连接测试失败:" + ex.Message);
            }
            return await Succeed();
        }
        
        public async Task<ActionResultDto> FunctionCallTest(MysoftActionDto actionDto)
        {
            await _kernelBuilder.AddChatCompletionServices(actionDto.ModelInstanceDtos);
            var kernel = _kernelBuilder.Build();
            int functionCallCount = 0;

            var function1 = KernelFunctionFactory.CreateFromMethod(new Func<string, string>((string location) =>
            {
                functionCallCount = functionCallCount + 1;
                return "Some weather";
            }), functionName: "GetCurrentWeather");

            var function2 = KernelFunctionFactory.CreateFromMethod(new Func<string, object>((string argument) =>
            {
                functionCallCount = functionCallCount + 1;
                throw new ArgumentException("Some exception");
            }), functionName: "FunctionWithException");

            kernel.Plugins.Add(KernelPluginFactory.CreateFromFunctions(pluginName: "MyPlugin", functions: new[] { function1, function2 }));

            var settings = new OpenAIPromptExecutionSettings() { ToolCallBehavior = ToolCallBehavior.AutoInvokeKernelFunctions };
            var function = kernel.CreateFunctionFromPrompt(new PromptTemplateConfig()
            {
                Template = @"今天的天气怎么样",
                ExecutionSettings =
                {
                    {
                        actionDto.ModelInstanceDtos[0].InstanceCode,
                        settings
                    }
                }
            });
            try
            {
                var result = await kernel.InvokeAsync(function, actionDto.CreateKernelArguments());
            }
            catch (HttpOperationException ex)
            {
                return await Failed("模型连接测试失败:" + ex.ResponseContent);
            }
            catch (Exception ex)
            {
                return await Failed("模型连接测试失败:" + ex.Message);
            }
            if (functionCallCount == 0)
            {
                return await Failed("模型连接测试失败:函数工具未被调用");
            }
            return await Succeed();
        }
        
        public async Task<ActionResultDto> OcrRecognizeTest(MysoftActionDto actionDto)
        {
            List<Guid> fileIds = new List<Guid>();
            fileIds.Add(Guid.Parse(actionDto.ModelInstanceDtos[0].fileId));
            var documents = await GetDocumentInfo(fileIds, 0);
            await _kernelBuilder.AddOcrRecognizeService( actionDto.ModelInstanceDtos[0], actionDto.ModelInstanceDtos[0].InstanceCode);
            var kernel = _kernelBuilder.Build();
            var ocrRecognizeService = kernel.GetRequiredService<IOcrRecognizeService>(actionDto.ModelInstanceDtos[0].InstanceCode);
            var imageContent = "";
            try
            {
                var imgContent = await ocrRecognizeService.Execute(new OcrRequest { Body = documents[0].FileContent,ocrCode = actionDto.ModelInstanceDtos[0].InstanceCode });
                if (!imgContent.Success)
                {
                    return await Failed("OCR模型连接测试失败:" + imgContent.Content);
                }
            }
            catch (HttpOperationException ex)
            {
                return await Failed("模型连接测试失败:" + ex.ResponseContent);
            }
            catch (Exception ex)
            {
                return await Failed("模型连接测试失败:" + ex.Message);
            }
            
            return await Succeed();
        }
        
        private async Task<ActionResultDto> MultiModalTest(MysoftActionDto actionDto, CancellationToken cancellationToken)
        {
            List<Guid> fileIds = new List<Guid>();
            fileIds.Add(Guid.Parse(actionDto.ModelInstanceDtos[0].fileId));
            var documents = await GetDocumentInfo(fileIds, 0);
            await _kernelBuilder.AddChatCompletionServices(actionDto.ModelInstanceDtos);
            var kernel = _kernelBuilder.Build();
            var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>(actionDto.ModelInstanceDtos[0].InstanceCode);
            try
            {
                PromptExecutionSettings? executionSettings = null;
                var content = string.Empty;
                var competionTokes = 0;
            
                var messageCollection = new ChatMessageContentItemCollection() { new TextContent { Text = "帮我分析这个图片!" } };
                foreach (var document in documents)
                {
                    var image = new ImageContent()
                    {
                        Metadata = new Dictionary<string, object?>()
                        {
                            { nameof(document.FileName), document.FileName },
                            { nameof(document.FileContent), document.FileContent }
                        }
                    };
                    messageCollection.Add(image);
                }

                var chatHistory = new ChatHistory();
                chatHistory.AddUserMessage(messageCollection);
                await foreach (var item in chatCompletionService.GetStreamingChatMessageContentsAsync(chatHistory: chatHistory, executionSettings: executionSettings, cancellationToken: cancellationToken))
                {
                    if (string.IsNullOrEmpty(item.Content) == false)
                    {
                        content += item.Content;
                    }
                }
            }
            catch (Exception ex)
            {
                return await Failed("模型连接测试失败:" +ex.Message);
            }
            return await Succeed();
        }

        private async Task<ActionResultDto> MultiFileModalTest(MysoftActionDto actionDto, CancellationToken cancellationToken)
        {
            List<Guid> fileIds = new List<Guid>();
            fileIds.Add(Guid.Parse(actionDto.ModelInstanceDtos[0].fileId));
            var documents = await GetDocumentInfo(fileIds, 0);
            actionDto.ModelInstanceDtos[0].IsDefault = true;
            await _kernelBuilder.AddChatCompletionServices(actionDto.ModelInstanceDtos);
            var kernel = _kernelBuilder.Build();
            var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>(ChatCompletionTypeDto.FileComprehend);
            try
            {
                PromptExecutionSettings? executionSettings = null;
                var content = string.Empty;
                var competionTokes = 0;

                var messageCollection = new ChatMessageContentItemCollection() {};
                foreach (var document in documents)
                {
                    var binaryContent = new BinaryContent()
                    {
                        Data = new ReadOnlyMemory<byte>(document.FileContent)
                    };
                    messageCollection.Add(binaryContent);
                }

                var chatHistory = new ChatHistory();
                chatHistory.Add(new ChatMessageContent(AuthorRole.System, messageCollection));
                chatHistory.AddUserMessage("帮我识别文档内容！");
                await foreach (var item in chatCompletionService.GetStreamingChatMessageContentsAsync(chatHistory: chatHistory, executionSettings: executionSettings, cancellationToken: cancellationToken))
                {
                    if (string.IsNullOrEmpty(item.Content) == false)
                    {
                        content += item.Content;
                    }
                }
            }
            catch (Exception ex)
            {
                return await Failed("模型连接测试失败:" + ex.Message);
            }
            return await Succeed();
        }
        private async Task<ActionResultDto> ContentReviewModalTest(MysoftActionDto actionDto, CancellationToken cancellationToken)
        {
            actionDto.ModelInstanceDtos[0].IsDefault = true;
            await _kernelBuilder.AddContentReviewServices(actionDto.ModelInstanceDtos);
            var kernel = _kernelBuilder.Build();
            var contentReview = kernel.GetRequiredService<IContentReview>(actionDto.ModelInstanceDtos[0].InstanceCode);
            try
            {
                var contentReviewResult = contentReview.Check("我是一个测试文本");
                if (!contentReviewResult.IsSuccess)
                {
                    return await Failed("模型连接测试失败:" + contentReviewResult.Message);
                }
            }
            catch (Exception ex)
            {
                return await Failed("模型连接测试失败:" + ex.Message);
            }
            return await Succeed();
        }


        public async Task<ActionResultDto> EmbeddingTest(MysoftActionDto actionDto)
        {
            
            var modelInstance = actionDto.ModelInstanceDtos[0];
            var serviceId = modelInstance.InstanceCode;
            switch (modelInstance.ModelType) {
                case ModelTypeEnum.AzureOpenAI:
                    _kernelBuilder.AddAzureOpenAIEmbedding(deploymentName: modelInstance.DeploymentName, modelId: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, apiKey: modelInstance.ApiKey, serviceId: serviceId); 
                    break;
                case ModelTypeEnum.OpenAI:
                    _kernelBuilder.AddOpenAIEmbedding(modelId: modelInstance.ModelCode, apiKey: modelInstance.ApiKey, serviceId: serviceId); 
                    break;
                case ModelTypeEnum.Ali:
                    _kernelBuilder.AddQwenEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                    break;
                case ModelTypeEnum.Xunfei:
                    _kernelBuilder.AddXunfeiEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                    break;
                case ModelTypeEnum.Baidu:
                    BaiduKernelBuilderExtensions.AddBaiduEmbedding(_kernelBuilder, modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey,clientId: modelInstance.ClientID, serviceId: serviceId);
                    break;
                default:
                    string modelInstanceString = JsonConvert.SerializeObject(modelInstance);
                    CustomRedirectHttpDto customRedirectHttpDto = JsonConvert.DeserializeObject<CustomRedirectHttpDto>(modelInstanceString);
                    MysoftKernelBuilderExtensions.AddMysoftEmbedding(_kernelBuilder, deploymentName:modelInstance.DeploymentName, modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, clientId: modelInstance.ClientID, vendor: modelInstance.Vendor, serviceId: serviceId, httpClient : new HttpClient(new CustomRedirectingHandler(customRedirectHttpDto)));
                    break;
            }
            var kernel = _kernelBuilder.Build();
            var _embeddingGenerationService = kernel.GetRequiredService<ITextEmbeddingGenerationService>(serviceId);
            
            try
            {
                var embedding = await _embeddingGenerationService.GenerateEmbeddingAsync("Hello", kernel).ConfigureAwait(false);
            }
            catch (HttpOperationException ex)
            {
                return await Failed("模型连接测试失败 " + ex.ResponseContent);
            }
            catch (Exception ex)
            {
                return await Failed("模型连接测试失败 " + ex.Message);
            }
            return await Succeed();
        }

        public async Task<List<ModelEntity>> GetModelEntityAsync()
        {
            return await _modelRepostory.GetListAsync();
        }

        public async Task<List<ModelInstanceDto>> GetModelInstancesAsync()
        {
            var _sqlSugarProvider = (await _sqlSugarProviderFactory.GetSqlSugarProvider()).CopyNew();

            return await Task.FromResult(_sqlSugarProvider.Queryable<ModelInstanceEntity>().InnerJoin<ModelEntity>((instance, model) => instance.ModelGUID == model.ModelGUID)
                .Where((instance, model) => !string.IsNullOrEmpty(instance.ApiKey))  
                .Select((instance, model) => new ModelInstanceDto
                  {
                      ModelGUID = model.ModelGUID,
                      ModelCode = !string.IsNullOrEmpty(instance.CustomModelCode) ? instance.CustomModelCode : model.ModelCode,
                      InstanceGUID = instance.InstanceGUID,
                      InstanceCode = instance.InstanceCode,
                      InstanceName = instance.InstanceName,
                      ModelType = model.ModelType,
                      Endpoint = instance.Endpoint,
                      ApiKey = instance.ApiKey,
                      ClientID = instance.ClientID,
                      DeploymentName = instance.DeploymentName,
                      IsDefault = instance.IsDefault,
                      ServiceTypeEnum = model.ServiceTypeEnum,
                      Vendor = instance.Vendor,
                      StrategyId = instance.StrategyId,
                      EnableCustomModel = instance.EnableCustomModel,
                      CustomModelCode = instance.CustomModelCode,
                      IsAvailable = instance.IsAvailable
                  })
                  .ToList());
        }

        public async Task<ActionResultDto> UpdateSensitiveWordsCache(string spaceGUID)
        {
            try
            {
                List<SensitiveWordsEntity> sensitiveWordsEntities = await _senitiveWordsRepostory.GetListAsync(f => f.SpaceGUID == new Guid(spaceGUID) && f.IsEnabled == 1);
                _mysoftMemoryCache.SetSensitiveWordsCache(sensitiveWordsEntities, spaceGUID);
                return await Succeed();
            }
            catch (Exception ex)
            {
                return await Failed(ex.Message);
            }
        }

        public async Task<List<DocumentInfoBaseDto>> GetDocumentInfo(List<Guid> documentGUIDs, int isReturnUrl = 1)
        {
            string gptBuilderUrl = _mysoftContextFactory.GetMysoftContext().GptBuilderUrl + GPTBuilderRequestPathConst.GetDocumentBaseInfo;
            DocumentQueryDto documentQueryDto = new DocumentQueryDto();
            documentQueryDto.IsReturnUrl = isReturnUrl;
            documentQueryDto.DocumentGUIDs = documentGUIDs;
            var res = await _mysoftApiDomainService.PostAsync(gptBuilderUrl, JsonConvert.SerializeObject(documentQueryDto));
            return await Task.FromResult(JsonConvert.DeserializeObject<ReturnDto<List<DocumentInfoBaseDto>>>(res).Data);
        }
        
        public async Task<List<ModelInstanceDto>> GetContentReviewModelInstancesAsync()
        {
            var _sqlSugarProvider = (await _sqlSugarProviderFactory.GetSqlSugarProvider()).CopyNew();

            return await Task.FromResult(_sqlSugarProvider.Queryable<ModelInstanceEntity>()
                .InnerJoin<ModelEntity>((instance, model) => instance.ModelGUID == model.ModelGUID)
                .Where((instance, model) => !string.IsNullOrEmpty(instance.ApiKey) && model.ServiceTypeEnum == ServiceTypeEnum.ContentReview && instance.IsDefault)  // 添加 ServiceType 等于 5 的条件
                .Select((instance, model) => new ModelInstanceDto
                {
                    ModelGUID = model.ModelGUID,
                    ModelCode = model.ModelCode,
                    InstanceGUID = instance.InstanceGUID,
                    InstanceCode = instance.InstanceCode,
                    InstanceName = instance.InstanceName,
                    ModelType = model.ModelType,
                    Endpoint = instance.Endpoint,
                    ApiKey = instance.ApiKey,
                    ClientID = instance.ClientID,
                    DeploymentName = instance.DeploymentName,
                    IsDefault = instance.IsDefault,
                    ServiceTypeEnum = model.ServiceTypeEnum,
                    Vendor = instance.Vendor,
                    StrategyId = instance.StrategyId
                })
                .ToList());
        }
    }
    public sealed class FunctionCallTestPlugin
    {
        [KernelFunction, Description("Provides a list of specials from the menu.")]
        [SuppressMessage("Design", "CA1024:Use properties where appropriate", Justification = "Too smart")]
        public string GetSpecials()
        {
            return
                "Special Soup: Clam Chowder /n Special Salad: Cobb Salad/nSpecial Drink: Chai Tea";
        }

        [KernelFunction, Description("Provides the price of the requested menu item.")]
        public string GetItemPrice(
            [Description("The name of the menu item.")]
            string menuItem)
        {
            return "$9.99";
        }
    }
}

using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.Shared.Constants;

namespace Mysoft.GPTEngine.Application.Heartbeat
{
    public class SSEHeartbeatStrategy : IHeartbeatStrategy
    {
        private readonly ILogger<SSEHeartbeatStrategy> _logger;
        private const int DefaultInterval = 15000; // 15秒

        public SSEHeartbeatStrategy(ILogger<SSEHeartbeatStrategy> logger)
        {
            _logger = logger;
        }

        public async Task SendHeartbeat(HttpResponse response, CancellationToken token)
        {
            try
            {
                var content = string.Format(EventDataConstant.HeartBeatEvent, "{\"heartbeat\": 1}");
                var bytes = Encoding.UTF8.GetBytes(content);
                await response.Body.WriteAsync(bytes);
                await response.Body.FlushAsync(token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送心跳消息失败");
                throw;
            }
        }

        public int GetHeartbeatInterval() => DefaultInterval;
    }

}
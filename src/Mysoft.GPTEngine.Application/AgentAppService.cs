using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Application.Strategies;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Services;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;

namespace Mysoft.GPTEngine.Application
{
#pragma warning disable CS0618 // 类型或成员已过时
    public class AgentAppService : AppServiceBase
    {
        private readonly Kernel _kernel;
        private readonly AgentDomainService _agentDomainService;
        private readonly DocumentDomainService _documentDomainService;
        private readonly IServiceProvider _serviceProvider;
        private readonly MemoryBuilder _memoryBuilder;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
        private readonly KnowledgeRepository _knowledgeRepository;
        private readonly KnowledgeFileRepository _knowledgeFileRepository;
        private readonly IKnowledgeDomainService _iKnowledgeDomainService;
        private readonly ContentReviewFactory _contentReviewFactory;
        private readonly ILogger<AgentAppService> _logger;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly ISqlSugarProviderFactory _sqlSugarProviderFactory;

        public AgentAppService(IServiceProvider serviceProvider, KnowledgeRepository knowledgeRepository, KnowledgeFileSectionRepository knowledgeFileSectionRepository, KnowledgeFileRepository knowledgeFileRepository, Kernel kernel, MemoryBuilder memoryBuilder, AgentDomainService agentDomainService, DocumentDomainService documentDomainService,
                MysoftApiService mysoftApiDomainService, IKnowledgeDomainService iKnowledgeDomainService, ISqlSugarProviderFactory sqlSugarProviderFactory, IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, IMapper mapper,
                ContentReviewFactory contentReviewFactory, ILogger<AgentAppService> logger) : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
            _kernel = kernel;
            _agentDomainService = agentDomainService;
            _documentDomainService = documentDomainService;
            _memoryBuilder = memoryBuilder;
            _mysoftApiDomainService = mysoftApiDomainService;
            _knowledgeFileSectionRepository = knowledgeFileSectionRepository;
            _knowledgeRepository = knowledgeRepository;
            _knowledgeFileRepository = knowledgeFileRepository;
            _iKnowledgeDomainService = iKnowledgeDomainService;
            _contentReviewFactory = contentReviewFactory;
            _serviceProvider = serviceProvider;
            _mysoftContextFactory = mysoftContextFactory;
            _logger = logger;
            _sqlSugarProviderFactory = sqlSugarProviderFactory;
        }


        /// <summary>
        /// 异步处理流式聊天完成的逻辑
        /// </summary>
        /// <param name="chatInput">包含聊天和助手GUID等信息的输入DTO</param>
        /// <param name="cancellationToken">用于取消任务的取消令牌</param>
        /// <returns>无返回值</returns>
        public async Task StreamingChatCompletionAsync(ChatInputDto chatInput, CancellationToken cancellationToken)
        {
            // 验证输入参数不为null
            Verify.NotNull(chatInput);
            Verify.NotNull(chatInput.ChatGUID);
            Verify.NotNull(chatInput.AssistanGUID);

            // 设置响应内容类型为text/event-stream，并设置Cache-Control为no-cache
            _httpContextAccessor.HttpContext.Response.ContentType = "text/event-stream";
            _httpContextAccessor.HttpContext.Response.Headers.Add("Cache-Control", "no-cache");

            using var heartbeatCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            var heartbeatService = _serviceProvider.GetRequiredService<HeartbeatService>();
            
            // 启动心跳任务
            var heartbeatTask = heartbeatService.StartHeartbeat(
                _httpContextAccessor.HttpContext.Response, 
                heartbeatCts.Token
            );

            try 
            {
                await ChatCompletionAsync(chatInput, true, cancellationToken).ConfigureAwait(false);
            }
            finally 
            {
                heartbeatCts.Cancel();
                try 
                {
                    await heartbeatTask;
                }
                catch (OperationCanceledException) 
                {
                    // 忽略取消异常
                }
            }
        }
        
        /// <summary>
        /// 调用记录对话完成
        /// </summary>
        /// <param name="chatInput">包含聊天和助手GUID等信息的输入DTO</param>
        /// <param name="cancellationToken">用于取消任务的取消令牌</param>
        /// <returns>无返回值</returns>
        public async Task<ActionResultDto> ChatCompletionAsync(ChatInputDto chatInput, bool isStream, CancellationToken cancellationToken)
        {
            // 打印chatInput参数
            _logger.LogInformation("[ChatCompletionAsync] 入参 chatInput: {@chatInput}", chatInput);
            // 验证输入参数不为null
            Verify.NotNull(chatInput);
            Verify.NotNull(chatInput.ChatGUID);
            if (isStream)
            {
                _contentReviewFactory.CheckInput(chatInput, cancellationToken, isStream).ConfigureAwait(false);
            }
            else
            {
                await _contentReviewFactory.CheckInput(chatInput, cancellationToken, isStream).ConfigureAwait(false);
            }

            var keyValue = chatInput.Arguments.FirstOrDefault(x => x.Key == KernelArgumentsConstant.KeywordDateTime);
            if (keyValue == null)
            {
                keyValue = new KeyValueDto { Key = KernelArgumentsConstant.KeywordDateTime, Value = TimeZoneUtility.LocalNowToString() };
                chatInput.Arguments.Add(keyValue);
            }
            else
            {
                keyValue.Value = TimeZoneUtility.LocalNowToString();
            }

            // 处理流式聊天完成的逻辑，并获取执行结果
            var chatRunDto = await _agentDomainService.StreamingChatCompletionProcessAsync(chatInput).ConfigureAwait(false);
            chatRunDto.IsStream = isStream;
            chatRunDto.CheckContent = chatInput.CheckContent ?? false;
            chatRunDto.ChatInput = chatInput;
            var chatState = ChatStateEnum.Running;
            var errorMessage = "";
            await _agentDomainService.SaveChatState(chatInput.ChatGUID, chatState);

            if (SkillModeConstant.Agent.Equals(chatRunDto.Mode))
            {
                //如果是agent的话 需要查询数据库查询
                var modelInstancesAsync = await GetModelInstancesAsync();
                var instanceCode = chatRunDto.ModelInstance.InstanceCode;
                var agent = chatRunDto.Agent;
                if (agent != null)
                {
                    instanceCode = agent.modelInstanceCode;
                }

                // 如果instanceCode为空，使用默认的文本生成模型
                if (string.IsNullOrEmpty(instanceCode))
                {
                    instanceCode = "default_text_generation";
                    _logger.LogInformation("[ChatCompletionAsync] agent.modelInstanceCode为空，使用默认模型: {instanceCode}", instanceCode);
                }

                var modelInstance = modelInstancesAsync.FirstOrDefault(x => x.InstanceCode == instanceCode);
                chatRunDto.ModelInstance = modelInstance;
                // 打印获取到的model参数
                _logger.LogInformation("[ChatCompletionAsync] 获取到的ModelInstance: {@modelInstance}", modelInstance);
            }
            
            // 添加上下文项
            AddContextItem(chatInput, chatRunDto);

            if (chatRunDto.ModelInstance == null)
            {
                string errMsg = string.IsNullOrEmpty(chatRunDto.SkillModelCode) ? ErrMsgConst.Model_InitDefaultErrMsg : string.Format(ErrMsgConst.Model_InitErrMsg, chatRunDto.SkillModelCode);
                throw new LLmCustomException("InitModelNoRegistered", errMsg);
            }
            
            try
            {
                // 使用静态工厂类直接调用方法
                string result = await SkillExecutionStrategyFactory.ExecuteAsync(_serviceProvider, _kernel, chatRunDto, _httpContextAccessor, _mysoftContextFactory, Mapper, cancellationToken);
                chatState = ChatStateEnum.Success;
                return await Succeed<string>(result);
            }
            catch (CancelException ex)
            {
                chatState = ChatStateEnum.Fail;
                errorMessage = ex.Message;
                // 跳出ex
                _logger.LogWarning(ex, "对话取消");
                return await Succeed<string>("");
            }
            catch (Exception ex)
            {
                chatState = ChatStateEnum.Fail;
                errorMessage = ex.Message;
                _logger.LogError(ex, "对话执行异常");
                await chatRunDto.AddfailedNodeLog(ex.Message);
                string error = await ErrorEvent(ex.Message, chatRunDto.IsStream, chatRunDto, ex, _logger);
                return await Failed(error);
            }
            finally
            {
                if (chatRunDto.IsStream)
                {
                    // 执行完成事件处理
                    await DoneEvent(chatRunDto.BatchGuid, chatRunDto.IsStream);
                }
                // 保存聊天参数和消息
                await _agentDomainService.SaveChatArguments();
                
                // 更新技能执行状态
                await _agentDomainService.SaveChatState(chatInput.ChatGUID, chatState, errorMessage);
            }
        }
        
        public async Task<List<ModelInstanceDto>> GetModelInstancesAsync()
        {
            var _sqlSugarProvider = (await _sqlSugarProviderFactory.GetSqlSugarProvider()).CopyNew();

            var modelInstances = await Task.FromResult(_sqlSugarProvider.Queryable<ModelInstanceEntity>().InnerJoin<ModelEntity>((instance, model) => instance.ModelGUID == model.ModelGUID)
                .Where((instance, model) => !string.IsNullOrEmpty(instance.ApiKey) && instance.IsAvailable)
                .Select((instance, model) => new ModelInstanceDto
                {
                    ModelGUID = model.ModelGUID,
                    ModelCode = !string.IsNullOrEmpty(instance.CustomModelCode) ? instance.CustomModelCode : model.ModelCode,  // 修复ModelCode赋值逻辑
                    InstanceGUID = instance.InstanceGUID,
                    InstanceCode = instance.InstanceCode,
                    InstanceName = instance.InstanceName,
                    ModelType = model.ModelType,
                    Endpoint = instance.Endpoint,
                    ApiKey = instance.ApiKey,
                    ClientID = instance.ClientID,
                    DeploymentName = instance.DeploymentName,
                    IsDefault = instance.IsDefault,
                    ServiceTypeEnum = model.ServiceTypeEnum,
                    Vendor = instance.Vendor,
                    StrategyId = instance.StrategyId,
                    EnableCustomModel = instance.EnableCustomModel,
                    CustomModelCode = instance.CustomModelCode,
                    IsAvailable = instance.IsAvailable,
                    IsSupportTool = instance.IsSupportTool,
                    SupportDeepThink = instance.SupportDeepThink  // 添加缺失的字段
                })
                .ToList());
            foreach (var modelInstance in modelInstances)
            {
                if (!String.IsNullOrEmpty(modelInstance.ApiKey))
                {
                    modelInstance.ApiKey = AesHelper.Decrypt(modelInstance.ApiKey);
                }
            }
            return modelInstances;
        }

        /// <summary>
        /// 获取消息日志
        /// </summary>
        /// <param name="messageGuid">消息GUID</param>
        /// <returns>dto对象</returns>
        public async Task<ActionResultDto> GetChatMessageLog(Guid messageGuid)
        {
            var messageNodeLogEntities = await _agentDomainService.GetChatMessageLog(messageGuid);
            messageNodeLogEntities = messageNodeLogEntities.OrderBy(x => x.Index)
                .ThenByDescending(x=>x.Name)
                .ThenByDescending(x => x.CreatedTime)
                .ToList();
            List<ChatMessageNodeLogDto> chatMessageNodeLogDtos = Mapper.Map<List<ChatMessageNodeLogDto>>(messageNodeLogEntities);
            var messageLogDto = new ChatMessageLogDto
            {
                ChatMessageNodeLogDtos = chatMessageNodeLogDtos
            };
            var firstRespDtos = chatMessageNodeLogDtos.Where(s => s.FirstRespDuration > 0).ToList();
            messageLogDto.FirstRespDuration = firstRespDtos.Count > 0 ? firstRespDtos.First().FirstRespDuration : messageLogDto.FirstRespDuration = messageLogDto.Duration;
            return await Succeed(messageLogDto);
        }

        /// <summary>
        /// 获取消息日志
        /// </summary>
        /// <param name="chatGuid">消息GUID</param>
        /// <returns>dto对象</returns>
        public async Task<ActionResultDto> GetChatMessageList(Guid chatGuid)
        {
            var messageEntities = await _agentDomainService.GetChatMessageList(chatGuid);
            messageEntities = messageEntities.OrderBy(x => x.Index).ToList();
            List<ChatMessageDto> result = new List<ChatMessageDto>();
            foreach (var message in messageEntities)
            {
                var dto = Mapper.Map<ChatMessageDto>(message);
                var suffix = EventDataConstant.GetMessageContentEvent(dto.Content);
                dto.ContentEventType = suffix[0];
                dto.Content = suffix[1];
                result.Add(dto);
            }

            return await Succeed(result);
        }

        /// <summary>
        /// 创建一个技能会话
        /// </summary>
        /// <param name="createChatInputDto">包含技能GUID等信息的输入DTO</param>
        /// <param name="cancellationToken">用于取消任务的取消令牌</param>
        /// <returns>会话GUID</returns>
        public async Task<ActionResultDto> CreateChat(CreateChatInputDto createChatInputDto, CancellationToken cancellationToken)
        {
            // 验证输入参数不为null
            Verify.NotNull(createChatInputDto);
            Verify.NotNull(createChatInputDto.SkillGUID);

            // 处理流式聊天完成的逻辑，并获取执行结果
            var chatGuid = await _agentDomainService.CreateChat(createChatInputDto);

            return await Succeed(chatGuid);
        }

        private void AddContextItem(ChatInputDto chatInput, ChatRunDto chatRunDto)
        {
            _httpContextAccessor.AddItem(nameof(ChatRunDto), chatRunDto);
        }

        /// <summary>
        /// 彻底清除用户会话记忆（包括缓存和数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        public async Task<ActionResultDto> ClearUserConversationMemoryCompletely(string userId)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                {
                    return await Failed("用户ID不能为空");
                }

                var conversationMemoryService = _serviceProvider.GetService<ConversationMemoryService>();
                if (conversationMemoryService == null)
                {
                    return await Failed("会话记忆服务未找到");
                }

                await conversationMemoryService.ClearConversationMemoryCompletelyAsync(userId);
                _logger.LogInformation("[ClearUserConversationMemoryCompletely] 已彻底清除用户 {userId} 的所有会话记忆", userId);

                return await Succeed<string>("用户会话记忆已彻底清除");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ClearUserConversationMemoryCompletely] 彻底清除用户 {userId} 会话记忆失败", userId);
                return await Failed($"彻底清除会话记忆失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 彻底清除用户指定会话的记忆（包括缓存和数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="chatGuid">会话ID</param>
        /// <returns></returns>
        public async Task<ActionResultDto> ClearUserChatConversationMemoryCompletely(string userId, string chatGuid)
        {
            try
            {
                if (string.IsNullOrEmpty(userId))
                {
                    return await Failed("用户ID不能为空");
                }

                if (string.IsNullOrEmpty(chatGuid))
                {
                    return await Failed("会话ID不能为空");
                }

                var conversationMemoryService = _serviceProvider.GetService<ConversationMemoryService>();
                if (conversationMemoryService == null)
                {
                    return await Failed("会话记忆服务未找到");
                }

                await conversationMemoryService.ClearConversationMemoryCompletelyAsync(userId, chatGuid);
                _logger.LogInformation("[ClearUserChatConversationMemoryCompletely] 已彻底清除用户 {userId} 会话 {chatGuid} 的会话记忆", userId, chatGuid);

                return await Succeed<string>("指定会话的记忆已彻底清除");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ClearUserChatConversationMemoryCompletely] 彻底清除用户 {userId} 会话 {chatGuid} 记忆失败", userId, chatGuid);
                return await Failed($"彻底清除会话记忆失败: {ex.Message}");
            }
        }
    }

    public class FunctionInvocationFilter : AppServiceBase, IFunctionInvocationFilter
    {
        public FunctionInvocationFilter(IHttpContextAccessor httpContextAccessor,
            IMysoftContextFactory mysoftContextFactory, IMapper mapper,
            ILogger<FunctionInvocationFilter> logger) : base(httpContextAccessor, mysoftContextFactory, mapper, logger)
        {
        }

        public async Task OnFunctionInvocationAsync(FunctionInvocationContext context, Func<FunctionInvocationContext, Task> next)
        {
            // 更新当前节点运行时
            var chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));
            if (chatRunDto == null)
            {
                return;
            }
            chatRunDto.Chat.CurrentOrchestrationGUID = Guid.Parse(context.Arguments[KernelArgumentsConstant.CurrentOrchestrationGUID].ToString());
            chatRunDto.Chat.CurrentNodeGUID = Guid.Parse(context.Arguments[KernelArgumentsConstant.CurrentNodeGUID].ToString());
            chatRunDto.Cancel = false;
            chatRunDto.CurrNodeIndex += 1;

            chatRunDto.NextNodeIndex = context.Arguments.TryGetValue(KernelArgumentsConstant.NextNodeGUID, out var nextNodeGuid) && chatRunDto.NextNodeIndex == 0 ? chatRunDto.CurrNodeIndex : chatRunDto.NextNodeIndex;

            // 解析输入参数：固定值、引用类型
            var flowNode = (chatRunDto.Nodes.First(x => x.Id == chatRunDto.Chat.CurrentNodeGUID.ToString()));
            
            // 交互卡片点击继续时，找到上次执行的断点，忽略前面已执行的步骤
            if (flowNode.Type != SkillNodeTypeConstant.Start)
            {
                if (chatRunDto.Next == false && chatRunDto.NextNodeIndex != 0 && chatRunDto.CurrNodeIndex >= chatRunDto.NextNodeIndex)
                {
                    chatRunDto.Cancel = true; return;
                }
                if (chatRunDto.Next && string.Equals(flowNode.Id, chatRunDto.NextId) == false)
                {
                    chatRunDto.Cancel = true; return;
                }
                if (chatRunDto.Next && string.Equals(flowNode.Id, chatRunDto.NextId))
                {
                    chatRunDto.Next = false;
                }
            }

            if (flowNode.Type != SkillNodeTypeConstant.Start && flowNode.Type != SkillNodeTypeConstant.End)
            {
                await ProcessEvent(string.IsNullOrEmpty(flowNode.Description) ? flowNode.Name : flowNode.Description, chatRunDto.IsStream);
            }

            await chatRunDto.InputsArgumentParser();

            await chatRunDto.AddNodeLog();

            await next(context);
        }
    }

}

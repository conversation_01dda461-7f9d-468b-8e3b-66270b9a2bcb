using System;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application
{
    public class MysoftContextFactory : IMysoftContextFactory
    {
        private readonly ILogger<MysoftContextFactory> _logger;
        private IHttpContextAccessor _httpContextAccessor;
        private MysoftContext _mysoftContext;
        private IConfigurationService _configurationService;
        private static readonly object _lock = new object();

        public MysoftContextFactory(IHttpContextAccessor httpContextAccessor, IConfigurationService configurationService, ILogger<MysoftContextFactory> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _configurationService = configurationService;
            _logger = logger;
        }

        public MysoftContext GetMysoftContext()
        {
            // if (_mysoftContext != null) return _mysoftContext;
            lock (_lock)
            {
                _mysoftContext = _httpContextAccessor.GetItem<MysoftContext>(nameof(MysoftContext));
                if (_mysoftContext != null && !String.IsNullOrWhiteSpace(_mysoftContext.TenantCode))
                {
                    InitConfigurationServiceInfo();
                    return _mysoftContext;
                }

                if (_httpContextAccessor.HttpContext != null &&
                    _httpContextAccessor.HttpContext.Request.Headers.ContainsKey(MysoftConstant.MysoftContext))
                {
                    var context = _httpContextAccessor.HttpContext.Request.Headers[MysoftConstant.MysoftContext];

                    context = AesHelper.Decrypt(context);

                    _mysoftContext = JsonConvert.DeserializeObject<MysoftContext>(context);
                    InitConfigurationServiceInfo();
                    return _mysoftContext;
                }

                return new MysoftContext();
            }
        }

        private void InitConfigurationServiceInfo()
        {
            _mysoftContext.GptBuilderUrl = _configurationService.GetBuilderUrl();
            VectordbInstancesItem vectordbInstancesItem = ConfigurationHelper.GetVectordbInstancesItem(_configurationService.GetIConfiguration());
            if (vectordbInstancesItem != null)
            {
                try
                {
                    _mysoftContext.MemoryStore = new MemoryStore()
                    {
                        Host = vectordbInstancesItem.Host,
                        Port = vectordbInstancesItem.Port,
                        UserName = vectordbInstancesItem.UserName,
                        Password = vectordbInstancesItem.Password
                    };
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "_mysoftContext.MemoryStore：【{0}】【{1}】", _mysoftContext, vectordbInstancesItem);
                    throw new BusinessException("", e);
                }
            }
        }

        public AccessTokenContent GetAccessTokenContext()
        {
            return _httpContextAccessor.GetItem<AccessTokenContent>(nameof(AccessTokenContent));
        }

        public void SetMysoftContext(MysoftContext mysoftContext)
        {
            _mysoftContext = mysoftContext;
        }
    }
}
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application.ApprovalDatasource
{
    public class FlowDatasourceService : IApprovalDatasource
    {
        
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly MysoftFlowApiService _mysoftFlowApiService;
        public readonly ILogger<PlatformDatasourceService> _logger;
        
        public FlowDatasourceService(IMysoftContextFactory mysoftContextFactory, MysoftFlowApiService mysoftFlowApiService, 
            ILogger<PlatformDatasourceService> logger)
        {
            _mysoftContextFactory = mysoftContextFactory;
            _logger = logger;
            _mysoftFlowApiService = mysoftFlowApiService;
        }
        
        
        public override async Task RequestBody(PlanDatasourceInstanceDto datasource, PlanInstanceInfoDto planInstanceInfoDto, CancellationToken cancellationToken = default)
        {
            var mysoftContext = _mysoftContextFactory.GetMysoftContext();
            string tenantCode = mysoftContext.TenantCode;
            var path = datasource.SourceId;
            Dictionary<string, object> requestParams = FormatGetRequestParams(datasource, planInstanceInfoDto);
            if (requestParams == null)
            {
                requestParams = new Dictionary<string, object>();
            }
            requestParams.Add("__app_tenant_id", tenantCode);
            datasource.RequestParams = JsonConvert.SerializeObject(requestParams);;
            datasource.RequestBody = await FlowDataApiExec(path, requestParams, planInstanceInfoDto.WorkSpaceCode);
        }
        
        protected async Task<string> FlowDataApiExec(string uri, Dictionary<string,  object> requestParams, string appCode)
        {
            var context = _mysoftContextFactory.GetMysoftContext();
            _logger.LogInformation("准备请求了：路径：{0},参数:{1}", context.GptBuilderUrl + uri, JsonConvert.SerializeObject(requestParams));
            var result = await _mysoftFlowApiService.GetAsync(context.GptBuilderUrl + uri, requestParams, appCode);
            _logger.LogInformation("拿到返回结果了：{0}", JsonConvert.SerializeObject(result));
            FlowRequestResultDto dto = JsonConvert.DeserializeObject<FlowRequestResultDto>(result);
            if (dto.Success != 1)
            {
                throw new BusinessException("获取数据失败："+result);
            }
            return await Task.FromResult(dto.Data);
        }
    }
    
    public class FlowRequestResultDto {

        public int Code { get; set; }

        public string Data { get; set; }

        public string Message { get; set; }

        public string DetailMessage { get; set; }

        public int Success { get; set; }
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Entity.Approval;
using Mysoft.GPTEngine.Domain.Repositories.Approval;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application.ApprovalDatasource
{
    public class ApprovalDatasourceService
    {
        private readonly ILogger<ApprovalDatasourceService> _logger;
        private readonly PlanDatasourceInstanceRepostory _planDatasourceInstanceRepostory;

        private readonly PlanRuleDataInstanceRepostory _planRuleDataInstanceRepostory;

        private readonly GptPluginDatasrouceService _gptPluginDatasrouceService;
        private readonly FlowDatasourceService _flowDatasourceService;
        private readonly PlatformDatasourceService _platformDatasourceService;
        private readonly GptSkillDatasourceService _gptSkillDatasourceService;
        
        public ApprovalDatasourceService(PlanDatasourceInstanceRepostory planDatasourceInstanceRepostory
            , PlanRuleDataInstanceRepostory planRuleDataInstanceRepostory, ILogger<ApprovalDatasourceService> logger
            , GptPluginDatasrouceService gptPluginDatasrouceService, FlowDatasourceService flowDatasourceService
            , PlatformDatasourceService platformDatasourceService, GptSkillDatasourceService gptSkillDatasourceService)
        {
            _logger = logger;
            _planDatasourceInstanceRepostory = planDatasourceInstanceRepostory;
            _planRuleDataInstanceRepostory = planRuleDataInstanceRepostory;
            _platformDatasourceService = platformDatasourceService;
            _flowDatasourceService = flowDatasourceService;
            _gptPluginDatasrouceService = gptPluginDatasrouceService;
            _gptSkillDatasourceService = gptSkillDatasourceService;
        }
        
        /// <summary>
        /// 查询且保存数据源数据
        /// </summary>
        public async Task LoadAndCacheDataSourcesAsync(HashSet<string> planRuleDataInstanceGUIDS, PlanInstanceInfoDto planInstanceInfoDto)
        {
            if (planRuleDataInstanceGUIDS == null || !planRuleDataInstanceGUIDS.Any())
            {
                return;
            }

            if (planInstanceInfoDto == null)
            {
                throw new ArgumentNullException(nameof(planInstanceInfoDto));
            }

            _logger.LogInformation("开始加载数据源，共 {Count} 个规则实例", planRuleDataInstanceGUIDS.Count);

            List<PlanRuleDataInstanceEntity> planRuleDataInstanceEntities = await _planRuleDataInstanceRepostory.GetListAsync(x =>
                planRuleDataInstanceGUIDS.Contains(x.PlanRuleInstanceGUID));

            if (planRuleDataInstanceEntities == null || !planRuleDataInstanceEntities.Any())
            {
                _logger.LogWarning("未找到与规则实例关联的数据源");
                return;
            }

            HashSet<string> planDatasourceInstanceGUIDS = planRuleDataInstanceEntities
                .Select(r => r.PlanDatasourceInstanceGUID)
                .ToHashSet();

            _logger.LogInformation("一共 {Count} 个数据源", planDatasourceInstanceGUIDS.Count);

            foreach (var guid in planDatasourceInstanceGUIDS)
            {
                await ProcessDatasourceAsync(guid, planInstanceInfoDto);
            }
        }


        private async Task ProcessDatasourceAsync(string planDatasourceInstanceGUID, PlanInstanceInfoDto planInstanceInfoDto)
        {
            PlanDatasourceInstanceDto datasource = GetDatasource(planDatasourceInstanceGUID, planInstanceInfoDto);
            _logger.LogInformation("开始加载数据源：{Name}", datasource.Name);
            try
            {
                datasource.RequestStatus = 1;
                await RequestDatasource(datasource, planInstanceInfoDto);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "数据源获取数据异常");
                datasource.RequestStatus = 2;
            }
            finally
            {
                await _planDatasourceInstanceRepostory.UpdateAsync(e => new PlanDatasourceInstanceEntity()
                {
                    RequestParams = datasource.RequestParams,
                    RequestStatus = datasource.RequestStatus,
                    RequestBody = datasource.RequestBody,
                    ModifiedTime = TimeZoneUtility.LocalNow()
                }, f => f.PlanDatasourceInstanceGUID == datasource.PlanDatasourceInstanceGUID);

            }
        }



        /// <summary>
        /// 获取数据源数据
        /// </summary>
        public async Task<SchemaData> GetData(RuleDto rule, PlanInstanceInfoDto planInstanceInfoDto)
        {
            var list = await _planRuleDataInstanceRepostory.GetListAsync(x =>
                x.PlanRuleInstanceGUID == rule.PlanRuleInstanceGUID);
            SchemaData schemaData = new SchemaData();
            if (list != null && list.Count > 0)
            {
                var result = new List<string>();
                List<AttachmentInfoDto> attachmentInfoList = new List<AttachmentInfoDto>();
                foreach (var planRuleData in list)
                {
                    PlanDatasourceInstanceDto datasource = GetDatasource(planRuleData.PlanDatasourceInstanceGUID, planInstanceInfoDto);
                    var body = await GetDataByDatasource(datasource);
                    var schema = JsonConvert.DeserializeObject<List<DatasourceFieldDto>>(planRuleData.Schema);
                    SchemaData simpleSchemaData = GenerateOutputValue(body, schema, datasource.Source);

                    result.Add(simpleSchemaData.Data);
                    if (datasource.AutoReadAttachment == 1)
                    {
                        MargeAttachmentInfoDto(attachmentInfoList, simpleSchemaData.AttachmentInfoList);
                    }
                    
                    planRuleData.Data = JsonConvert.SerializeObject(simpleSchemaData);
                    _planRuleDataInstanceRepostory.UpdateAsync(planRuleData);
                }

                schemaData.Data = JsonConvert.SerializeObject(result);
                schemaData.AttachmentInfoList = attachmentInfoList;
                return await Task.FromResult(schemaData);
            }

            // 如果没有绑定数据源，且BusinessFormatData，则默认是流程审批(兼容历史)
            if (!string.IsNullOrEmpty(planInstanceInfoDto.BusinessFormatData))
            {
                schemaData.Data = planInstanceInfoDto.BusinessFormatData;
                return await Task.FromResult(schemaData);
            }

            // 没有绑定数据源，则直接返回参数
            schemaData.Data = JsonConvert.SerializeObject(planInstanceInfoDto.ParamsValue);
            return await Task.FromResult(schemaData);

        }


        /// <summary>
        /// 获取数据源基本信息
        /// </summary>
        private PlanDatasourceInstanceDto GetDatasource(string planDatasourceInstanceGUID,
            PlanInstanceInfoDto planInstanceInfoDto)
        {
            PlanDatasourceInstanceDto datasource = planInstanceInfoDto.PlanDatasourceInstances.Find(x =>
                x.PlanDatasourceInstanceGUID == planDatasourceInstanceGUID);
            if (datasource == null)
            {
                throw new BusinessException("数据源缺失：" + planDatasourceInstanceGUID);
            }

            return datasource;
        }

        /// <summary>
        /// 获取数据源数据，优先从已经请求过的数据中获取
        /// </summary>
        private async Task<string> GetDataByDatasource(PlanDatasourceInstanceDto datasource)
        {
            if (datasource.RequestStatus == 2)
            {
                throw new BusinessException("数据源获取数据异常：" + datasource.RequestBody);
            }
            return await Task.FromResult(datasource.RequestBody);
        }

        /// <summary>
        /// 请求数据源
        /// </summary>
        private async Task RequestDatasource(PlanDatasourceInstanceDto datasource,
            PlanInstanceInfoDto planInstanceInfoDto)
        {
            if (datasource.Source == 0)
            {
                await _flowDatasourceService.RequestBody(datasource, planInstanceInfoDto);
                return;
            }

            if (datasource.Source == 1)
            {
                await _platformDatasourceService.RequestBody(datasource, planInstanceInfoDto);
                return;
            }

            if (datasource.Source == 2)
            {
                await _gptPluginDatasrouceService.RequestBody(datasource, planInstanceInfoDto);
                return;
            }

            if (datasource.Source == 3)
            {
                await _gptSkillDatasourceService.RequestBody(datasource, planInstanceInfoDto);
                return;
            }

            throw new BusinessException("数据源类型错误：" + datasource.Source);
        }

        private SchemaData GenerateOutputValue(string jsonBody, List<DatasourceFieldDto> schema, int source)
        {
            var schemaItems = new List<SchemaItem>();
            ConvertToSchemaItems(schema, schemaItems);

            return BeanSchemaHelper.ConvertJsonWithSchema(jsonBody, schemaItems, source);
        }
        
        /// <summary>
        /// 格式化流程是请求响应内容
        /// </summary>
        private SchemaData GenerateFlowOutputValue(PlanInstanceInfoDto planInstanceInfoDto, List<DatasourceFieldDto> schema)
        {
            SchemaData schemaData = new SchemaData();
            foreach (var datasourceFieldDto in schema)
            {
                if (datasourceFieldDto.Selected == 1)
                {
                    if ("flow_data" == datasourceFieldDto.ParamId)
                    {
                        schemaData.Data = planInstanceInfoDto.BusinessFormatData;
                        if (planInstanceInfoDto.ParamsValue.TryGetValue("flow_data", out var jsonBody))
                        {
                            schemaData.Data = JsonConvert.SerializeObject(jsonBody);
                        }
                    }
                    if ("flow_attachments" == datasourceFieldDto.ParamId)
                    {
                        List<AttachmentDto> old = planInstanceInfoDto.Attachments;
                        if (planInstanceInfoDto.ParamsValue.TryGetValue("flow_attachments", out var jsonBody))
                        {
                            old = JsonConvert.DeserializeObject<List<AttachmentDto>>(
                                JsonConvert.SerializeObject(jsonBody));
                        }
                        if (old != null && old.Count > 0)
                        {
                            List<AttachmentInfoDto> attachments = new List<AttachmentInfoDto>();
                            foreach (var dto in old)
                            {
                                attachments.Add(new AttachmentInfoDto()
                                {
                                    DocumentGuid = dto.Id,
                                    Name = dto.Title
                                });
                            }

                            schemaData.AttachmentInfoList = attachments;
                        }
                    }
                }
            }

            return schemaData;
        }
        
        /// <summary>
        /// DatasourceFieldDto转换成Schema
        /// </summary>
        public static int ConvertToSchemaItems(List<DatasourceFieldDto> dtos, List<SchemaItem> result)
        {
            var selected = 0;
            foreach (var dto in dtos)
            {
                var item = new SchemaItem
                {
                    Description = dto.ParamName,
                    Name = dto.ParamId,
                    Type = dto.ParamType,
                    Selected = dto.Selected,
                    Attachment = dto.Attachment,
                    Children = new List<SchemaItem>()
                };
                
                if (dto.Selected == 1) selected = 1;
                
                if (dto.Children != null && dto.Children.Count > 0)
                {
                    if (ConvertToSchemaItems(dto.Children, item.Children) == 1)
                    {
                        item.Selected = 1;
                        selected = 1;
                    }

                    
                }
                result.Add(item);
            }
            return selected;
        }
        
        /// <summary>
        /// 合并所有的文档
        /// </summary>
        private void MargeAttachmentInfoDto(List<AttachmentInfoDto> target, List<AttachmentInfoDto> source)
        {
            if (source != null && source.Count > 0)
            {
                foreach (var attachmentInfoDto in source)
                {
                    var  dto = target.Find(x => x.DocumentGuid == attachmentInfoDto.DocumentGuid);
                    if (dto == null)
                    {
                        target.Add(attachmentInfoDto);
                    }
                }
            }
        }
    }
}
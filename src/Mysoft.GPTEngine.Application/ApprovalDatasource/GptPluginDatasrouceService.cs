using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Application.ApprovalDatasource
{
    public class GptPluginDatasrouceService : IApprovalDatasource
    {
        
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly IMysoftApiService _mysoftApiService;
        public readonly IMysoftIPassApiService _mysoftIPassApiService;
        public readonly ILogger<GptPluginDatasrouceService> _logger;
        public readonly PluginRepostory _pluginRepostory;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;

        public GptPluginDatasrouceService(IMysoftContextFactory mysoftContextFactory, MysoftConfigurationDomain mysoftConfigurationDomain,
            IMysoftApiService mysoftApiService , IMysoftIPassApiService mysoftIPassApiService,
            PluginRepostory pluginRepostory,
            ILogger<GptPluginDatasrouceService> logger)
        {
            _mysoftContextFactory = mysoftContextFactory;
            _logger = logger;
            _mysoftApiService = mysoftApiService;
            _mysoftIPassApiService = mysoftIPassApiService;
            _pluginRepostory = pluginRepostory;
            _mysoftConfigurationDomain = mysoftConfigurationDomain;
        }
        
        public override async Task RequestBody(PlanDatasourceInstanceDto datasource, PlanInstanceInfoDto planInstanceInfoDto, CancellationToken cancellationToken = default)
        {
            var path = "";
            var sourceId = datasource.SourceId;
            var id = sourceId;
            int underscoreIndex = sourceId.IndexOf("_", StringComparison.InvariantCulture);

            if (underscoreIndex > 0)
            {
                path = sourceId.Substring(underscoreIndex + 1);
                id = sourceId.Substring(0, underscoreIndex);
            }
            PluginEntity plugin = await _pluginRepostory.GetFirstAsync(x => x.PluginGUID == id);
            if (plugin == null)
            {
                throw new BusinessException("插件不存在：" + datasource.SourceId);
            }

            string obj;
            var jsonBody = FormatRequestParams(datasource, planInstanceInfoDto);
            datasource.RequestParams = jsonBody;
            if (plugin.Source == 1)
            {
                obj = await MysoftIPassApiExec(path, jsonBody);
            }
            else
            {
                obj = await MysoftApiExec(path, jsonBody);
            }
            
            
            datasource.RequestBody = obj;
            
            await Task.CompletedTask;
        }
        
        
        /// <summary>
        /// GPT平台-插件数据源，IPASS数据源请求
        /// </summary>
        private async Task<string> MysoftIPassApiExec(string uri, String jsonBody)
        {
            var context = _mysoftContextFactory.GetMysoftContext();
            var mipInfo = _mysoftConfigurationDomain.GetMipInfo();
            _logger.LogInformation("准备请求了：路径：{0},参数:{1}", mipInfo.ServiceUrl + uri, JsonConvert.SerializeObject(jsonBody));
            var result = await _mysoftIPassApiService.PostAsync(mipInfo.ServiceUrl + uri, jsonBody);

            _logger.LogInformation("拿到返回结果了：{0}", JsonConvert.SerializeObject(result));

            return await Task.FromResult(result);
        }
        
        /// <summary>
        /// GPT平台-插件数据源，平台数据源请求
        /// </summary>
        private async Task<string> MysoftApiExec(string uri, String jsonBody)
        {
            var context = _mysoftContextFactory.GetMysoftContext();
            _logger.LogInformation("准备请求了：路径：{0},参数:{1}", context.GptBuilderUrl + uri, JsonConvert.SerializeObject(jsonBody));
            var result = await _mysoftApiService.PostAsync(context.GptBuilderUrl + uri, jsonBody);
            _logger.LogInformation("拿到返回结果了：{0}", JsonConvert.SerializeObject(result));
            var data = JsonConvert.DeserializeObject<MysoftApiResultDto>(result);

            if (data?.Success == false)
            {
                throw new BusinessException("获取数据失败："+result);
            }
            var map = new Dictionary<string, object>()
            {
                {"body", data?.Data}
            };

            return await Task.FromResult(JsonConvert.SerializeObject(map));
        }
    }
    
    public class MysoftApiResultDto
    {
        public string Code { get; set; }
        public string Message { get; set; }
        public bool Success { get; set; }
        public object Error { get; set; }
        public object Data { get; set; }
    }
}
namespace Mysoft.GPTEngine.Application.Configuration
{
    /// <summary>
    /// 文本切分配置
    /// </summary>
    public static class TextPartitioningOptions
    {
        /// <summary>
        /// The maximum number of tokens per paragraph.
        /// When partitioning a document, each partition usually contains one paragraph.
        /// </summary>
        public static int MaxTokensPerParagraph { get; set; } = 500;

        /// <summary>
        /// The maximum number of tokens per line, aka per sentence.
        /// When partitioning a block of text, the text will be split into sentences, that are then grouped into paragraphs.
        /// Note that this applies to any text format, including tables, code, chats, log files, etc.
        /// </summary>
        public static int MaxTokensPerLine { get; set; } = 500;

        /// <summary>
        /// The number of overlapping tokens between paragraphs.
        /// </summary>
        public static int OverlappingTokens { get; set; } = 20;
    }
}

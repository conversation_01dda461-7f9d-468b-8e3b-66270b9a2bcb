using System.Collections.Generic;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using RabbitMQ.Client;

namespace Mysoft.GPTEngine.Common.Rabbitmq
{
    public class RabbitmqTestService
	{
        //private readonly ILogger<RabbitmqTestService> _logger;

        private readonly MYRabbitmqClient _rabbitmqClient;


        public RabbitmqTestService(MYRabbitmqClient rabbitmqClient)
		{
            //_logger = logger;
            //初始化连接
            _rabbitmqClient = rabbitmqClient;

            //创建队列
            DeclareQueue();
            //创建交换机
            DeclareExchange();
            //创建Direct绑定关系
            QueueBindDirectExchange();
            //创建Fanout绑定关系
            QueueBindFanoutExchange();
            //创建Topic绑定关系
            QueueBindTopicExchange();
        }

        /// <summary>
        /// 定义交换机
        /// </summary>
        private void DeclareExchange()
        {
            _rabbitmqClient.CreateExchange(ExchangeConst.EXCHANGE_DIRECT, ExchangeType.Direct);
            _rabbitmqClient.CreateExchange(ExchangeConst.EXCHANGE_FANOUT, ExchangeType.Fanout);
            _rabbitmqClient.CreateExchange(ExchangeConst.EXCHANGE_TOPIC, ExchangeType.Topic);
        }

        /// <summary>
        /// 定义队列
        /// </summary>
        private void DeclareQueue()
        {
            _rabbitmqClient.CreateQueue(QueueConst.QUEUE_DIRECT1);
            _rabbitmqClient.CreateQueue(QueueConst.QUEUE_DIRECT2);
            _rabbitmqClient.CreateQueue(QueueConst.QUEUE_FANOUT1);
            _rabbitmqClient.CreateQueue(QueueConst.QUEUE_FANOUT2);
            _rabbitmqClient.CreateQueue(QueueConst.QUEUE_TOPIC1);
            _rabbitmqClient.CreateQueue(QueueConst.QUEUE_TOPIC2);
        }

        /// <summary>
        /// 队列绑定直连交换机
        /// </summary>
        private void QueueBindDirectExchange()
        {
            _rabbitmqClient.ExchangeBindQueue(QueueConst.QUEUE_DIRECT1, ExchangeConst.EXCHANGE_DIRECT, QueueConst.QUEUE_DIRECT1,
        new Dictionary<string, object>
            {
                { "prop",$"直连发送队列{QueueConst.QUEUE_DIRECT1}"}
            });
            _rabbitmqClient.ExchangeBindQueue(QueueConst.QUEUE_DIRECT2, ExchangeConst.EXCHANGE_DIRECT, QueueConst.QUEUE_DIRECT2, new Dictionary<string, object>
            {
                { "prop1",$"直连发送队列{QueueConst.QUEUE_DIRECT2}"}
            });
        }

        /// <summary>
        /// 队列绑定扇形交换机
        /// Fanout：会将所有发送到交换器的消息路由到所有与改交换器绑定的队列中。这种情况BingKey和RoutingKey相当于不起作用。
        /// </summary>
        private void QueueBindFanoutExchange()
        {
            _rabbitmqClient.ExchangeBindQueue(QueueConst.QUEUE_FANOUT1, ExchangeConst.EXCHANGE_FANOUT, string.Empty,
          new Dictionary<string, object>
            {
                        { "prop",$"发布订阅发送队列{QueueConst.QUEUE_FANOUT1}"}
            });
            _rabbitmqClient.ExchangeBindQueue(QueueConst.QUEUE_FANOUT2, ExchangeConst.EXCHANGE_FANOUT, string.Empty,
            new Dictionary<string, object>
            {
                      { "prop",$"发布订阅发送队列{QueueConst.QUEUE_FANOUT2}"}
            });
        }

        /// <summary>
        /// 队列绑定主题交换机
        /// Topic：会按照一定规则将BindingKey和RoutingKey相匹配的队列中。BindingKey可以存在两种特殊字符串"*"和"#"。“#”号用于匹配一个单词，“*”匹配多个单词。
        ///         topic.#那么这个队列会接收topic开头的消息，如topic.hello.world
        ///         topic.* 那么这个队列只接收topic开头后面一个的消息，如topic.hello
        ///         topic.*.queue那么这个队列会接收topic.qqqq.queue这样格式的消息，不接收能topic.qqqq.oooo.queue这样格式的消息
        /// </summary>
        private void QueueBindTopicExchange()
        {
            _rabbitmqClient.ExchangeBindQueue(QueueConst.QUEUE_TOPIC1, ExchangeConst.EXCHANGE_TOPIC, "mysoft.gpt.*",
        new Dictionary<string, object>
            {
                           { "prop",$"topic路由mysoft.gpt.*"}
            });

            _rabbitmqClient.ExchangeBindQueue(QueueConst.QUEUE_TOPIC2, ExchangeConst.EXCHANGE_TOPIC, "mysoft.gpt.#",
       new Dictionary<string, object>
            {
                      { "prop",$"topic路由mysoft.gpt.#"}
            });
           
        }

        
        public void SendMessage()
        {
            Message message = new Message();
            message.msg = "测试消息";
            _rabbitmqClient.SendMessage<Message>(ExchangeConst.EXCHANGE_DIRECT, message, null, new string[] { "mysoft.gtp.queue.direct1" });

            _rabbitmqClient.SendMessageTofanout<Message>(ExchangeConst.EXCHANGE_FANOUT, message, null);

            _rabbitmqClient.SendMessage<Message>(ExchangeConst.EXCHANGE_TOPIC, message, null, new string[] { "mysoft.gpt.A.A" });


        }

    }
}


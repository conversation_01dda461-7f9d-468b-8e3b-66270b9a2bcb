using Microsoft.Extensions.Configuration;

namespace Mysoft.GPTEngine.Common.Rabbitmq.Config
{
    sealed internal class RabbitMqConfig
	{
		public RabbitMqConfig()
		{
		}

        private const string RABBITMQ_NODE = "RabbitMq";

        public string HostName { private set; get; }

        public int Port { private set; get; }

        public string UserName { private set; get; }

        public string Password { private set; get; }

        public string VirtualHost { private set; get; }

        /*
         * 获取appsettings.json中 raabitmq 中的配置
         */
        public static RabbitMqConfig GetOption()
        {
            RabbitMqConfig option = new RabbitMqConfig();
            
            IConfigurationRoot config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json").Build();
            option.HostName = config["RabbitMq:HostName"];
            option.Port = int.Parse(config["RabbitMq:Port"]);
            option.UserName = config["RabbitMq:UserName"];
            option.Password = config["RabbitMq:Password"];
            option.VirtualHost = config["RabbitMq:VirtualHost"];
            return option;
        }
    }
}


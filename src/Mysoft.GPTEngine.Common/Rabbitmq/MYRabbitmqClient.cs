using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Mysoft.GPTEngine.Common.Rabbitmq.Config;
using Newtonsoft.Json;
using RabbitMQ.Client;

namespace Mysoft.GPTEngine.Common.Rabbitmq
{
    public class MYRabbitmqClient
    {
        private readonly ConnectionFactory _connectionFactory;
        private IConnection _connection;
        private IModel _model;

        public const string DIRECT = "direct";

        public const string FANOUT = "fanout";

        public const string TOPIC = "topic";

        public MYRabbitmqClient()
		{
            RabbitMqConfig option = RabbitMqConfig.GetOption();

            _connectionFactory = new ConnectionFactory()
            {
                HostName = option.HostName,
                Port = option.Port,
                UserName = option.UserName,
                Password = option.Password,
                VirtualHost = option.VirtualHost
            };

            Init();

            void Init()
            {
                CreateConnection();
                CreateChannle();
            }

        }

        /// <summary>
        /// 获取连接连接
        /// </summary>
        /// <returns></returns>
        public IConnection CreateConnection()
        {
            if (_connection == null || !_connection.IsOpen)
                _connection = _connectionFactory.CreateConnection();

            return _connection;
        }

        /// <summary>
        /// 创建信道
        /// </summary>
        public IModel CreateChannle()
        {
            if (_model == null)
                _model = CreateConnection().CreateModel();

            return _model;
        }

        /// <summary>
        /// 创建交换机
        /// </summary>
        /// <param name="name"></param>
        /// <param name="type"></param>
        /// <param name="durable"></param>
        /// <param name="autoDelete"></param>
        public void CreateExchange(string name, string type, bool durable = true, bool autoDelete = false)
        {
            _model.ExchangeDeclare(name, type, durable, autoDelete);
        }

        /// <summary>
        /// 创建队列
        /// </summary>
        /// <param name="name"></param>
        /// <param name="exclusive"></param>
        /// <param name="durable"></param>
        /// <param name="autoDelete"></param>
        public void CreateQueue(string name, bool exclusive = false, bool durable = true, bool autoDelete = false, IDictionary<string, object> arguments = null)
        {
            _model.QueueDeclare(name, durable, exclusive, autoDelete, arguments);
        }

        /// <summary>
        /// 队列绑定交换机
        /// </summary>
        /// <param name="queue"></param>
        /// <param name="exchange"></param>
        /// <param name="routingKey"></param>
        public void ExchangeBindQueue(string queue, string exchange, string routingKey = null, IDictionary<string, object> arguments = null)
        {
            _model.QueueBind(queue, exchange, routingKey, arguments);
        }

        /// <summary>
        /// 发送消息（全）
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="exchange"></param>
        /// <param name="message"></param>
        /// <param name="routingKeys"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public void SendMessage<T>(string exchange, T message, IBasicProperties basicProperties = null, params string[] routingKeys)
            where T : Message
        {
            /**
             *  direct的routingKeys其实就是队列的名称  指定那个往那个队列发
             *  fanout的routingKeys不要传，指定交换机后会发给所有的绑定的队列
             *  topic的routingKeys按照绑定是的路由规则发送消息到指定的队列
             * 
             */
            if (routingKeys == null || routingKeys.Length == 0)
                throw new ArgumentNullException($"{nameof(routingKeys)}不能为空");

            string dataStr = JsonConvert.SerializeObject(message);
            byte[] bytes = Encoding.UTF8.GetBytes(dataStr);

            routingKeys.ToList().ForEach(routingKey => _model.BasicPublish(exchange, routingKey, basicProperties, bytes));
        }

        /// <summary>
        /// 发送消息 ：fanout专用发送消息队列
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="exchange"></param>
        /// <param name="message"></param>
        /// <param name="routingKeys"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public void SendMessageTofanout<T>(string exchange, T message, IBasicProperties basicProperties = null, params string[] routingKeys)
            where T : Message
        {
            string dataStr = JsonConvert.SerializeObject(message);
            byte[] bytes = Encoding.UTF8.GetBytes(dataStr);
            _model.BasicPublish(exchange, exchange, basicProperties, bytes);
        }

        /// <summary>
        /// 释放对象
        /// </summary>
        public void Dispose()
        {
            _connection.Close();
            _connection.Dispose();

            _model.Close();
            _model.Dispose();

            GC.Collect();
            GC.SuppressFinalize(this);
        }



    }
}


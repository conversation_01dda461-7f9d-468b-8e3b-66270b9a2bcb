using System;

namespace Mysoft.GPTEngine.Common.CustomerException
{
    public class ValidateException : Exception
    {
        // 默认构造函数
        public ValidateException() : base() { }

        // 带有消息的构造函数
        public ValidateException(string message) : base(message) { }

        // 带有消息和内部异常的构造函数
        public ValidateException(string message, Exception innerException)
            : base(message, innerException) { }
    }
}

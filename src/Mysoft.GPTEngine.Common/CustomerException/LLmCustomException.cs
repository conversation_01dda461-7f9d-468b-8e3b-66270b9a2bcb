using System;

namespace Mysoft.GPTEngine.Common.CustomerException
{
    public class LLmCustomException : Exception
    {
        // 错误编码
        public string StatusCode { get; private set; }

        public int ModelInstanceTypeEnum {  get; private set; }

        // 默认构造函数
        public LLmCustomException() : base() { }

        // 带有消息的构造函数
        public LLmCustomException(string statusCode, string message, int modelInstanceTypeEnum = 0) : base(message)
        {
            this.StatusCode = statusCode;
            this.ModelInstanceTypeEnum = modelInstanceTypeEnum;
        }

        // 带有消息和内部异常的构造函数
        public LLmCustomException(string statusCode, string message, Exception innerException)
            : base(message, innerException) {
            this.StatusCode = statusCode;
        }
    }
}

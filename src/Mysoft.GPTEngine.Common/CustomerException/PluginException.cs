using System;

namespace Mysoft.GPTEngine.Common.CustomerException
{
    public class PluginException : Exception
    {

        // 带有消息和内部异常的构造函数
        public PluginException(string message, Exception innerException)
            : base(message, innerException)
        {
        }
        
        // 带有消息和内部异常的构造函数
        public PluginException(string message)
            : base(message)
        {
        }
    }
}

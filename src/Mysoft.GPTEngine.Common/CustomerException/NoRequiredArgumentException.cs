using System;

namespace Mysoft.GPTEngine.Common.CustomerException
{
    public class NoRequiredArgumentException : Exception
    {
        // 默认构造函数
        public NoRequiredArgumentException() : base() { }

        // 带有消息的构造函数
        public NoRequiredArgumentException(string message) : base(message) { }

        // 带有消息和内部异常的构造函数
        public NoRequiredArgumentException(string message, Exception innerException)
            : base(message, innerException) { }
    }
}

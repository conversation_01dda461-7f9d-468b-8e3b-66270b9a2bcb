using System;
using System.IO;

namespace Mysoft.GPTEngine.Common.Helper
{
    /// <summary>
    /// 文件类型转换帮助类
    /// </summary>
    public static class FileTypeConvertHelper
    {
        /// <summary>
        /// base64 转文件流
        /// </summary>
        /// <param name="base64String"></param>
        /// <returns></returns>
        public static Stream Base64ToStream(string base64String)
        {
            // 将Base64字符串转换为byte数组
            byte[] bytes = Convert.FromBase64String(base64String);

            // 创建MemoryStream，并将byte数组写入其中
            MemoryStream stream = new MemoryStream(bytes);

            // 将当前位置设置为流的起始位置
            stream.Position = 0;

            return stream;
        }

        public static string GetFileType(string fileName)
        {
            string extension = Path.GetExtension(fileName);

            switch (extension.ToLower())
            {
                case ".doc":
                case ".docx":
                    return "word";
                case ".pdf":
                    return "pdf";
                case ".txt":
                    return "txt";
                case ".md":
                    return "markdown";
                case ".html":
                    return "html";
                case ".xls":
                case ".xlsx":
                    return "excel";
                case ".jpg":
                case ".jpeg":
                case ".bpm":
                case ".gif":
                case ".png":
                case ".tif":
                case ".webp":
                    return "image";
                default:
                    return "暂不支持该类型";
            }
        }
    }
}

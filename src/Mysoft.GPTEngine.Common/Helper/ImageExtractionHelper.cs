using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading;
using Serilog;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class ImageExtractionHelper
    {
        /// <summary>
        /// 提取文本中的图片占位符
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static List<Guid> ImageExtraction(string str)
        {
            List<Guid> guids = new List<Guid>();
            string pattern = @"\{\{image:([A-Fa-f\d]{8}-[A-Fa-f\d]{4}-[A-Fa-f\d]{4}-[A-Fa-f\d]{4}-[A-Fa-f\d]{12})\}\}";

            MatchCollection matches = Regex.Matches(str, pattern);
            foreach (Match match in matches)
            {
                string documentGUID = match.Groups[1].Value;
                Guid.TryParse(documentGUID, out Guid guid);
                guids.Add(guid);
            }

            return guids;
        }

        public static List<Guid> HyperLinkExtraction(string str)
        {
            List<Guid> guids = new List<Guid>();
            string pattern = @"\{\{hyperlink:([A-Fa-f\d]{8}-[A-Fa-f\d]{4}-[A-Fa-f\d]{4}-[A-Fa-f\d]{4}-[A-Fa-f\d]{12})\}\}";

            MatchCollection matches = Regex.Matches(str, pattern);
            foreach (Match match in matches)
            {
                string documentGUID = match.Groups[1].Value;
                Guid.TryParse(documentGUID, out Guid guid);
                guids.Add(guid);
            }

            return guids;
        }

        /// <summary>
        /// 提取文本中的图片占位符
        /// </summary>
        /// <param name="markdownText"></param>
        /// <param name="hyperLinkList"></param>
        /// <returns></returns>
        public static string ReplaceMarkdownImageWithUuid(string markdownText, Dictionary<Guid,string> imagerUrlMap, List<Guid> imageGuids)
        {
            string imagePattern = @"!\[(.*?)\]\((.*?)\)";
            MatchCollection matches = Regex.Matches(markdownText, imagePattern);

            HashSet<string> uniqueLinks = new HashSet<string>();
            foreach (Match match in matches)
            {
                string link = match.Groups[2].Value;
                if (!uniqueLinks.Contains(link))
                {
                    uniqueLinks.Add(link);
                    Guid uuid = Guid.NewGuid();
                    string newUuid = "{{image:" + uuid.ToString() + "}}";
                    markdownText = markdownText.Replace(match.Groups[0].Value, newUuid);
                    imagerUrlMap.Add(uuid, link);
                    imageGuids.Add(uuid);
                }
            }

            return markdownText;
        }
        

        public static string RemoveBase64Img(string input)
        {
            // 匹配 base64 图片字符串的正则表达式
            string pattern = @"!\[.*?\]\(data:image\/[^;]+;base64,[^)]+\)";

            // 使用正则表达式匹配并移除 base64 图片字符串
            string result = Regex.Replace(input, pattern, "");

            return result;
        }
        
        public static byte[] ConvertToWebPAsync(String imageName, byte[] imageData, CancellationToken ct = default)
        {
            try
            {
                Log.Debug("图片：{0}, 压缩前大小: {1}", imageName, imageData.Length);
                using var tempInput = new TempFileHelper(Path.GetExtension(imageName).ToLowerInvariant());
                using var tempOutput = new TempFileHelper(".webp");

                File.WriteAllBytes(tempInput.FilePath, imageData);

                // 构建 cwebp 命令
                var startInfo = new ProcessStartInfo
                {
                    FileName = "cwebp",
                    Arguments = $" \"{tempInput.FilePath}\" -o \"{tempOutput.FilePath}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process == null)
                {
                    var error = process.StandardError.ReadToEndAsync();
                    Log.Error("cwebp 转换失败: {}", error);
                    return null;
                }

                process.WaitForExit(5*1000);
                if (process.ExitCode != 0)
                {
                    var error = process.StandardError.ReadToEndAsync();
                    Log.Error("cwebp 转换失败: {}", error);
                    return null;
                }

                var comprssImage = File.ReadAllBytes(tempOutput.FilePath);
                Log.Debug("图片：{0}, 压缩后大小: {1}", imageName, comprssImage.Length);
                return comprssImage;
            }
            catch (Exception ex)
            {
                Log.Error(ex,"转换为 WebP 时出错: {}", ex.Message);
                return null;
            }
        }

        // 辅助类：创建临时文件并自动删除
        private class TempFileHelper : IDisposable
        {
            public string FilePath { get; }

            public TempFileHelper(string extension)
            {
                FilePath = Path.GetTempFileName() + extension;
            }

            public void Dispose()
            {
                if (File.Exists(FilePath))
                {
                    File.Delete(FilePath);
                }
            }
        }
    }
}

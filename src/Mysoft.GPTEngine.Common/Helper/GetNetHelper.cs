using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

namespace Mysoft.GPTEngine.Common.Helper
{
    /// <summary>
    /// HTTP请求调用帮助类
    /// </summary>
    public class GetNetHelper
    {
        /// <summary>
        /// GET调用方法
        /// </summary>
        /// <param name="serviceAddress">地址信息</param>
        /// <returns></returns>
        public static string Get(string serviceAddress)
        {

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(serviceAddress);
            request.Method = "GET";
            request.ContentType = "text/html;charset=UTF-8";
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            Stream myResponseStream = response.GetResponseStream();
            StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.UTF8);
            string retString = myStreamReader.ReadToEnd();
            myStreamReader.Close();
            myResponseStream.Close();
            return retString;
        }

        /// <summary>
        /// POST调用方法
        /// </summary>
        /// <param name="serviceUrl">完整的服务地址</param>
        /// <param name="content">请求内容</param>
        /// <returns></returns>
        public static string Post(string serviceUrl, string content = "", List<Cookie> cookies = null)
        {
            string responseJson = string.Empty;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(serviceUrl);
            request.Method = "POST";
            setHeaders(request, cookies);
            request.ContentType = "application/json; charset=utf-8";
            Encoding encoding = Encoding.UTF8;
            byte[] postData = encoding.GetBytes(content);
            using (Stream requestStream = request.GetRequestStream())
            {
                requestStream.Write(postData, 0, postData.Length);
            }

            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            {
                using (Stream strem = response.GetResponseStream())
                {
                    using (StreamReader reader = new StreamReader(strem, encoding))
                    {
                        responseJson = reader.ReadToEnd();
                    }
                }
            }
            return responseJson;
        }

        /// <summary>
        /// 设置请求Headers
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cookies"></param>
        /// <returns></returns>

        private static void setHeaders(HttpWebRequest request, List<Cookie> cookies) 
        {
            if (cookies != null && cookies.Count > 0) 
            {
                //三大认证特殊处理，找到cookie中的my-api-Authorization
                Cookie myApiAuthorizationCookie = cookies.Find(x => x != null && x.Name != null && x.Name.Equals("my-api-Authorization"));
                if (myApiAuthorizationCookie != null)
                {
                    request.Headers.Add("my-api-Authorization", myApiAuthorizationCookie.Value);
                }
                request.Headers.Add("Cookie", getCookies(cookies));
            }
        }

        /// <summary>
        /// 构建请求cookie
        /// </summary>
        /// <param name="cookies"></param>
        /// <returns></returns>
        private static string getCookies(List<Cookie> cookies)
        {
            StringBuilder cookiesValue = new StringBuilder();
            for (int i = 0; i < cookies.Count(); i++)
            {
                Cookie cookie = cookies[i];
                if (cookie == null) 
                {
                    break;
                }
                cookiesValue.Append(cookie.Name).Append("=").Append(cookie.Value);
                if (i != cookies.Count() - 1)
                {
                    // 最后一个
                    cookiesValue.Append(";");
                }
            }
            return cookiesValue.ToString();
        }
    }
}

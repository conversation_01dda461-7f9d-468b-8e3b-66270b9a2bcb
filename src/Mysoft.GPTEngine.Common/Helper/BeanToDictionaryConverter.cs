using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Mysoft.GPTEngine.Common.Helper
{
    public class BeanToDictionaryConverter
    {
        public static Dictionary<string, object> ConvertBeanToDictionary(object bean, string prefix)
        {
            if (bean == null)
                return new Dictionary<string, object>();

            var properties = bean.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            return properties.ToDictionary(
                prop => prefix+prop.Name,
                prop => prop.GetValue(bean, null));
        }
    }
}
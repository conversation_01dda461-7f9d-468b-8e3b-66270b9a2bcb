using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class JsonUtility
    {
        
        static JsonSerializerOptions _options = new JsonSerializerOptions
        {
            ReadCommentHandling = JsonCommentHandling.Skip,
            AllowTrailingCommas = true,
        };
        /// <summary>
        /// 从JSON字符串中获取对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="jsonStr"></param>
        /// <param name="output"></param>
        /// <returns></returns>
        public static bool TryDeserializeObject<T>(string jsonStr, out T output)
        {
            try
            {
                var jsonRegex = new Regex(@"\{([\s\S]*?)\}");
                var matches = jsonRegex.Matches(jsonStr);
                jsonStr = matches.FirstOrDefault() == null ? jsonStr : matches.First().Value;

                output = JsonConvert.DeserializeObject<T>(jsonStr);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.StackTrace);
                output = default(T);
                return false;
            }
        }
        
        public static bool TryDeserializeObjectList<T>(string jsonStr, out T output)
        {
            try
            {
                var jsonRegex = new Regex(@"\[(?:[^{}]|(?<Open>{)|(?<-Open>}))*(?(Open)(?!))\]");
                var matches = jsonRegex.Matches(jsonStr);
                jsonStr = matches.FirstOrDefault() == null ? jsonStr : matches.First().Value;

                output = JsonSerializer.Deserialize<T>(jsonStr, _options);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.StackTrace);
                output = default(T);
                return false;
            }
        }
        
        public static bool TryDeserializeJsonStringObjectList<T>(string input, out T output)
        {
            try
            {
                string pattern = @"```\s*json\s*(.*?)```";

                // 创建Regex对象，并使用Matches方法获取所有匹配项
                MatchCollection matches = Regex.Matches(input, pattern, RegexOptions.Singleline);
                var jsonStr = matches.LastOrDefault() != null ? matches.Last().Groups[1].Value : matches.First().Groups[1].Value;

                
                output = JsonSerializer.Deserialize<T>(jsonStr, _options);
                return true;
            }
            catch (Exception ex)
            {
                if (TryDeserializeObjectList<T>(input, out var dto))
                {
                    output = dto;
                    return true;
                }
                output = default(T);
                return false;
            }
        }

        public static string TryGetJson(string jsonStr)
        {
            try
            {
                var jsonRegex = new Regex(@"\{([\s\S]*?)\}");
                var matches = jsonRegex.Matches(jsonStr);
                jsonStr = matches.FirstOrDefault() == null ? jsonStr : matches.First().Value;

                return jsonStr;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 从JSON字符串中获取属性值。
        /// </summary>
        /// <param name="jsonStr">JSON字符串。</param>
        /// <param name="propertyName">要获取的属性名称。</param>
        /// <returns>属性值，如果不存在则返回 null。</returns>
        public static string TryGetPropertyValue(string jsonStr, string propertyName)
        {
            try
            {
                JObject jsonObject = JObject.Parse(jsonStr);
                JToken value = jsonObject[propertyName];
                return value?.ToString();
            }
            catch (Exception ex)
            {
                // 处理解析异常，例如 JSON 格式错误
                Console.WriteLine($"Error parsing JSON: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 判断JSON字符串是否包含指定属性。
        /// </summary>
        /// <param name="jsonString">JSON字符串</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否包含指定属性</returns>
        public static bool IsContainsOutput(string jsonString)
        {
            
            if (string.IsNullOrEmpty(jsonString))
                return false;
            string propertyName = "output";
            if (!jsonString.StartsWith("{"))
                return false;
            
            if (!jsonString.Contains(propertyName))
                return false;

            var jsonDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonString);
            return jsonDict != null && jsonDict.ContainsKey(propertyName);
        }
        
        /// <summary>
        /// 判断JSON字符串是否包含指定属性。
        /// </summary>
        /// <param name="jsonString">JSON字符串</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否包含指定属性</returns>
        public static string RemoveInputHistoryProperty(string jsonString)
        {
            if (string.IsNullOrEmpty(jsonString))
                return jsonString;
            // 将 JSON 字符串解析为 JObject
            JObject jsonObject = JObject.Parse(jsonString);
            // 移除指定的属性
            jsonObject.Property("ChatHistory")?.Remove();
            //判断 jsonObject 属性的个数
            if (jsonObject.Properties().Count() == 1)
            {
                return jsonObject.Properties().FirstOrDefault().Value.ToString(); 
            }
            else
            {
                // 将 JObject 转换回 JSON 字符串
                string newJson = jsonObject.ToString(Formatting.Indented);
                return newJson;
            }
        }

        /// <summary>
        /// 从JSON字符串中提取指定属性的值。
        /// </summary>
        /// <param name="jsonString">JSON字符串</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        public static string OutPutValue(string jsonString)
        {
            string propertyName = "output";
            if (string.IsNullOrEmpty(jsonString) || string.IsNullOrEmpty(propertyName))
                throw new ArgumentException("输入参数不能为空");

            if (!jsonString.StartsWith("{"))
                throw new ArgumentException("输入的字符串不是一个有效的JSON对象");

            var jsonDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonString);
            if (jsonDict == null || !jsonDict.ContainsKey(propertyName))
                return null; // 或者抛出异常

            return jsonDict[propertyName]?.ToString();
        }

        public static string HyperLinkClean(string hyperlinkStr)
        {
            // 正则表达式
            string pattern = @"MEMBER_CONTACT_CARD\s*{[^}]*}";

            // 使用正则表达式替换匹配项
            string cleanedText = Regex.Replace(hyperlinkStr, pattern, "");

            return cleanedText;
        }

        public static string ControlCharClean(string str)
        {
            // 正则表达式，用于匹配ASCII控制字符
            string pattern = @"[\x00-\x09\x10-\x1F]";

            // 使用正则表达式替换匹配项
            string cleanedText = Regex.Replace(str, pattern, "");

            return cleanedText;
        }

        public static string MenuClean(string str)
        {
            // 正则表达式，用于匹配和提取章节信息
            string pattern = @"HYPERLINK\s+\\l\s+""[^""]+""\s+(.*?)\s+PAGEREF\s+[^ ]+\s+\\h\s+\d+";

            // 使用正则表达式替换所有匹配项，只保留章节信息
            string cleanedText = Regex.Replace(str, pattern, "$1", RegexOptions.Multiline);
            cleanedText = Regex.Replace(cleanedText, @"TOC\s+\\o\s+""[^""]+""\s+\\h\s+\\z\s+\\u", "", RegexOptions.Singleline | RegexOptions.Multiline);
            return cleanedText;
        }
    }
}

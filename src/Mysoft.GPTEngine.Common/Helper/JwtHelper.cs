using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Reflection;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class JwtHelper<T>
    {
        
        public const string DefaultSecret = "32CharKey1234567890abcd";
        
        public static string GenerateJwtToken(T t, string secret, long expireTime)
        {
            // 设置 JWT 签名密钥
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secret));
            
            // 设置 JWT 签名证书
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
            
            // 创建 JWT Header
            var header = new JwtHeader(credentials);
            
            // 创建 JWT Payload
            var payload = new JwtPayload
            {
                { "exp", expireTime }
            };
            Dictionary<string, object> data = ConvertToDictionary(t);
            foreach (var row in data)
            {
                payload.Add(row.Key, row.Value);
            }
            
            // 创建 JWT 实例
            var jwt = new JwtSecurityToken(header, payload);
            
            // 生成 JWT 字符串
            var jwtHandler = new JwtSecurityTokenHandler();
            return jwtHandler.WriteToken(jwt);
        }
        
        public static T ValidateJwtToken(string token, string secret)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(secret);

            var principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false, // 如果你需要验证 issuer，请设为 true 并提供有效的 issuer
                ValidateAudience = false, // 如果你需要验证 audience，请设为 true 并提供有效的 audience
                // 设置时钟偏差为零，通常建议设置一个合理的值，例如 5 分钟
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            // 将 ClaimsPrincipal 转换成 JSON 字符串
            var claimsDictionary = principal.Claims.ToDictionary(c => c.Type, c => c.Value);
            var json = JsonConvert.SerializeObject(claimsDictionary, Formatting.Indented);
            return JsonConvert.DeserializeObject<T>(json);
        }
        
        
        public static Dictionary<string, object> ConvertToDictionary(object obj)  
        {  
            return obj.GetType()  
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)  
                .ToDictionary(  
                    prop => prop.Name,  
                    prop => prop.GetValue(obj, null)  
                );  
        }  
    }
}
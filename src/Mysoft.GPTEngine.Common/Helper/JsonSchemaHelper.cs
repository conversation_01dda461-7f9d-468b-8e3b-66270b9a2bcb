using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Schema;

namespace Mysoft.GPTEngine.Common.Helper
{
    public class JsonSchemaHelper
    {
        public static string ReplaceJsonDataDescriptions(string jsonSchema, string jsonData)
        {
            // 解析 JSON Schema
            JSchema schema = JSchema.Parse(jsonSchema);

            // 解析 JSON 字符串
            JObject json = JObject.Parse(jsonData);

            // 替换 JSON 字符串的键
            JObject replacedJson = ReplaceKeysWithDescriptions(schema, json);
            
            return replacedJson.ToString(Formatting.Indented);
        }
        
        private static JObject ReplaceKeysWithDescriptions(JSchema schema, JObject json)
        {
            JObject result = new JObject();

            foreach (var prop in schema.Properties)
            {
                string key = prop.Key;
                JSchema propSchema = prop.Value;

                if (json.TryGetValue(key, out JToken value))
                {
                    string description = string.IsNullOrEmpty(propSchema.Title) ? propSchema.Description : propSchema.Title;
                    if (!string.IsNullOrEmpty(description))
                    {
                        key = description;
                    }

                    switch (value.Type)
                    {
                        case JTokenType.Object:
                            JObject objValue = value.ToObject<JObject>();
                            JObject replacedObj = ReplaceKeysWithDescriptions(propSchema, objValue);
                            result.Add(key, replacedObj);
                            break;
                        case JTokenType.Array:
                            JArray arrayValue = value.ToObject<JArray>();
                            JArray replacedArray = ReplaceArrayWithDescriptions(propSchema, arrayValue);
                            result.Add(key, replacedArray);
                            break;
                        default:
                            result.Add(key, value.DeepClone());
                            break;
                    }
                }
                else
                {
                    result.Add(key, null);
                }
            }

            return result;
        }

        private static JArray ReplaceArrayWithDescriptions(JSchema schema, JArray array)
        {
            JArray result = new JArray();

            foreach (JToken item in array)
            {
                if (item.Type == JTokenType.Object)
                {
                    JObject objItem = item.ToObject<JObject>();
                    JObject replacedObj = ReplaceKeysWithDescriptions(schema.Items[0], objItem);
                    result.Add(replacedObj);
                }
                else
                {
                    result.Add(item.DeepClone());
                }
            }

            return result;
        }
        
        
    }
}
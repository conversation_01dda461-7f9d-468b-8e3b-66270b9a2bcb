using System;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;

namespace Mysoft.GPTEngine.Common.Helper
{
    public static class ErrorCodeInitHelper
    {
        private static string commonErrCode = "error";
        private static string noRequiredArgumentErrCode = "NoRequiredArgument";
        private static string vectorExErrCode = "VectorException";
        private static string formatException = "formatException";
        private static string validateError = "validateError";
        private static string ocrError = "ocrError";
        private static string llmError = "llmError";
        private static string exception = "exception";
        private static string validateException = "validateException";
        private static string pluginException = "pluginException";
        public static (string code,string message,string type, int modelInstanceTypeEnum) ErrorCodeInit(Exception ex)
        {
            string errorCode;
            string statusCode = commonErrCode;
            string message = "发生了异常,请稍后重试或联系管理员!";
            string type = string.Empty;
            int modelInstanceTypeEnum = 0;
            switch (ex)
            {
                case NoRequiredArgumentException noRequiredArgumentEx:
                    statusCode = noRequiredArgumentErrCode;
                    break;
                case VectorException vectorEx:
                    statusCode = vectorExErrCode;
                    break;
                case JsonFormatException jsonFormatException:
                    statusCode = formatException;
                    break;
                case LLmCustomException llmEx:
                    statusCode = llmEx.StatusCode;
                    modelInstanceTypeEnum = llmEx.ModelInstanceTypeEnum;
                    break;
                case HttpOperationException httpEx:
                    statusCode = httpEx.StatusCode.ToString();
                    break;
                case KernelException kernelEx:
                    statusCode = ErrorStatusInit(kernelEx.Message);
                    break;
                case ValidateException validateEx:
                    statusCode = validateException;
                    break;
                case PluginException pluginEx:
                    statusCode = pluginException;
                    break;
                default:
                    break;
            }
            switch (statusCode)
            {
                // 未注册
                case "NoRegistered":
                    errorCode = "serviceUnregister";
                    message = "AI模型服务不可用，请联系系统管理员检查模型配置。";
                    type = validateError;
                    break ;
                // 必填参数校验
                case "NoRequiredArgument":
                    errorCode = "requireArgument";
                    message = ex.Message;
                    type = validateError;
                    break;
                case "Ocr":
                    errorCode = "error";
                    message = ex.Message;
                    type = ocrError;
                    break;
                case "LLM":
                    errorCode = "error";
                    message = ex.Message;
                    type = llmError;
                    break;
                case "VectorException":
                    errorCode = "memoryUnavailable";
                    message = "向量库不可用，请联系系统管理员。";
                    type = validateError;
                    break;
                case "InitModelNoRegistered":
                    errorCode = "serviceUnregister";
                    message = ex.Message;
                    type = validateError;
                    break;
                case "formatException":
                    errorCode = formatException;
                    message = ex.Message;
                    type = llmError;
                    break;
                case "validateException":
                    errorCode = validateException;
                    message = "敏感词校验";
                    type = validateError;
                    break;
                case "pluginException":
                    errorCode = pluginException;
                    message = ex.Message;
                    type = validateError;
                    break;
                default:
                    errorCode = statusCode;
                    type = exception;
                    break;
            }
            return (errorCode, message, type, modelInstanceTypeEnum);
        }

        public static string ErrorStatusInit(string errorMessage) { 
            if (errorMessage.Contains("not registered"))
            {
                return "NoRegistered";
            }
            return "";
        }
    }
}

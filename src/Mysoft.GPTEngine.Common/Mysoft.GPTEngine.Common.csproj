<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Rabbitmq\" />
    <None Remove="Rabbitmq\Const\" />
    <None Remove="Rabbitmq\Model\" />
    <None Remove="Rabbitmq\Config\" />
    <None Remove="Resources\Aspose.Total.lic" />
    <None Remove="Resources\Aspose.Words.NET.lic" />
    <PackageReference Include="Aspose.Cells" Version="20.7.0" />
<!--    <PackageReference Include="Aspose.PDF" Version="25.7.0" />-->
    <PackageReference Include="Aspose.PDF.Drawing" Version="25.7.0" />
    <PackageReference Include="Aspose.Words" Version="21.12.0" />
    <PackageReference Include="Microsoft.ML.Tokenizers" Version="0.22.0-preview.24271.1" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Newtonsoft.Json.Schema" Version="4.0.2-beta1" />
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
    <PackageReference Include="RabbitMQ.Client" Version="6.8.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="3.4.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Expressions" Version="4.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.80.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.8.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\Aspose.Total.lic">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\Aspose.Words.NET.lic">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Rabbitmq\Model\" />
    <Folder Include="Rabbitmq\Config\" />
  </ItemGroup>
</Project>

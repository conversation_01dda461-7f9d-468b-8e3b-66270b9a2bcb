using System.Collections.Generic;
using System.IO;
using System.Linq;
using Serilog;
using Serilog.Templates;
using ILogger = Serilog.ILogger;
namespace Mysoft.GPTEngine.Common.fast
{
    public class FastLogger
    {
        private static Dictionary<string, ILogger> _loggers;
        
        public static void Init()
        {
            _loggers = new Dictionary<string, ILogger>
            {
                { FastRequestLog.Name, CreateFastLogger(FastRequestLog.Name) }
            };
            Log.Information("天眼日志已初始化:{loggers}", _loggers.Keys.ToArray());
        }

        private static ILogger CreateFastLogger(string name)
        {
            return new LoggerConfiguration()
                .Enrich.FromLogContext()
                .WriteTo
                .File(
                    new ExpressionTemplate("{ @m }\n"),
                    path: Path.Combine("logs", name, ".log"),
                    rollingInterval: RollingInterval.Day,// 按天滚动  
                    rollOnFileSizeLimit: true, // 当文件大小达到限制时滚动  
                    fileSizeLimitBytes: 10485760, // 文件大小限制，例如10MB
                    retainedFileCountLimit: 30 // 保留最近30个滚动文件 
                )
                .CreateLogger();
        }

        public static void Write(FastBaseLog log)
        {
            if (!_loggers.TryGetValue(log.GetName(), out var logger))
            {
                Log.Warning("未初始化Logger:{name}", log.GetName());
                return;
            }
            
            logger.Information("{message}", log);
        }
        
    }
}
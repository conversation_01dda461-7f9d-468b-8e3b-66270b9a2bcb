using System;

namespace Mysoft.GPTEngine.Common.fast
{
    public class FastRequestLog : FastBaseLog
    {
        public const string Name = "RequestLog";
        
        public override string GetName()
        {
            return Name;
        }
        
        /// <summary>
        /// 请求方法，POST, GET
        /// </summary>
        public string Mothed { get; set; }
        
        /// <summary>
        /// 请求路径
        /// </summary>
        public string ResponseUrl { get; set; }
        
        /// <summary>
        /// Cookie 
        /// </summary>
        public string Cookie { get; set; }
        
        /// <summary>
        /// Handler 请求头
        /// </summary>
        public string Handler { get; set; }
        
        /// <summary>
        /// 请求内容
        /// </summary>
        public string RequestBody { get; set; }
        
        /// <summary>
        /// 响应内容
        /// </summary>
        public string ResponseBody { get; set; }
        
        /// <summary>
        /// 请求状态
        /// </summary>
        public string StatusCode { get; set; }
        
        /// <summary>
        /// 请求时长
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// 请求开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 请求结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
    }
}
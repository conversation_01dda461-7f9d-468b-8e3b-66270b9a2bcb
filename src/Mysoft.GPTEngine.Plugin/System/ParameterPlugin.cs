using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AutoMapper;
using HandlebarsDotNet;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.SemanticKernel.Core.Parsers;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.System
{
#pragma warning disable CS4014 // 由于此调用不会等待，因此在调用完成前将继续执行当前方法

    public class ParameterPlugin : PluginBase
    {
        private readonly IJsonOutputParsers _jsonOutputParsers;

        private readonly ILogger<ParameterPlugin> _logger;

        public ParameterPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IJsonOutputParsers jsonOutputParsers, IHttpContextAccessor httpContextAccessor, IMapper mapper, IMysoftContextFactory mysoftContextFactory, ILogger<ParameterPlugin> logger) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _jsonOutputParsers = jsonOutputParsers;
            _logger = logger;
        }

        [KernelFunction]
        [Description("空实现")]
        public async Task EmptyImplement([Description("输入")] string input)
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel)
            {
                await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, "用户确认")).ConfigureAwait(false);
                return;
            }
            _logger.LogInformation("空实现-输入: {0}", input);
            await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, input)).ConfigureAwait(false);
            await AddFirstOutput(input).ConfigureAwait(false);
        }
        [KernelFunction]
        [Description("生成固定值参数")]
        public async Task FixedGenerate([Description("输入")] string input, CancellationToken cancellationToken = default)
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return;
            var node = await chatRunDto.GetFlowNode();
            if (node.Config.Outputs.Any())
            {
                foreach (var output in node.Config.Outputs)
                {
                    output.LiteralValue = chatRunDto.ChatArguments.ContainsName(output.Code) ? chatRunDto.ChatArguments[output.Code] as string : null;
                }
                await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, JsonConvert.SerializeObject(node.Config.Outputs)));
            }
            else if (string.IsNullOrWhiteSpace(input) == false)
            {
                _logger.LogInformation("空实现-输入: {0}", input);
                await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, input));
                await AddFirstOutput(input);
            }

            //_logger.LogInformation("生成固定值参数-输入: {0}", input);
            //await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, input));
            //if (string.IsNullOrWhiteSpace(input)) { return; }
            //var chatRunDto = await GetChatRunDto();

            //var outputs = await chatRunDto.GetFlowNodeOutputs();
            //if (outputs == null || outputs.Count == 0) return;

            //var fields = string.Join("\r\n", outputs.Select(x => $"- key:{x.Code},type:{x.Type},description:{x.Description}").ToList());

            //var arguments = new KernelArguments { { nameof(fields), fields } };
            //var result = await ChatCompletion(systemPromptName: EmbeddedResource.ParameterPlugin_FixedGenerate, input: input, arguments: arguments, cancellationToken: cancellationToken);

            //var resultFormat = _jsonOutputParsers.Format(result);

            //if (JsonUtility.TryDeserializeObject<List<KeyValueDto>>(resultFormat, out var paramValueDtos))
            //{
            //    foreach (var output in outputs)
            //    {
            //        output.LiteralValue = paramValueDtos?.FirstOrDefault(x => x.Key == output.Code)?.Value ?? string.Empty;
            //    }
            //}
            //await AddMessage(ChatRoleConstant.User, string.Format(EventDataConstant.TextEvent, input));
            ////await AddMessage(ChatRoleConstant.Assistant, result);
            //_logger.LogInformation("生成固定值参数-输入: {0}; 输出：{1}", input, result);
        }
        [KernelFunction]
        [Description("输出参数格式化")]
        public async Task<string> OutFormat()
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return string.Empty;
            var outputs = await chatRunDto.GetFlowNodeOutputs();
            if (outputs == null || outputs.Count == 0) return await Task.FromResult(string.Empty);
            await chatRunDto.OutputsArgumentParser();
            var result = new ExpandoObject();
            foreach (var output in outputs)
            {
                result.TryAdd(output.Code, output.LiteralValue);
            }

            chatRunDto.Chat.CurrentOrchestrationGUID = null;

            string resultStr = JsonConvert.SerializeObject(result);
            _logger.LogInformation("输出参数格式化: {0}", resultStr);
            // 添加技能的返回值
            await chatRunDto.AddArgument(KernelArgumentsConstant.Output, resultStr);
            return await Task.FromResult(resultStr);
        }
        [KernelFunction]
        [Description("文本模板 - 文本解析")]
        public async Task<string> ContentAnalysis()
        {
            var chatRunDto = await GetChatRunDto();
            await NodeArgumentCheck(chatRunDto);
            var currentNode = await chatRunDto.GetFlowNode();
            var arguments = await chatRunDto.GetNodeInputArgument();
            Dictionary<string, Object> data = new Dictionary<string, Object>();
            foreach (var arg in arguments)
            {
                parseJsonString(data, arg.Key, arg.Value?.ToString() ?? string.Empty);
            }
            var content = currentNode.Config.Content;
            var template = Handlebars.Compile(content);
            string result = template(data);
            result = HttpUtility.HtmlDecode(result);
            await AddFirstOutput(result);
            return await Task.FromResult(result);
        }

        private void parseJsonString(Dictionary<string, Object> data, string key, string value)
        {
            if (IsValidJsonList(value))
            {
                data.Add(key, JsonConvert.DeserializeObject<List<Dictionary<string, Object>>>(value.ToString()));
            }
            else if (IsValidJson(value))
            {
                data.Add(key, JsonConvert.DeserializeObject(value));
            }
            else
            {
                data.Add(key, value);
            }
        }

        private bool IsValidJson(string json)
        {
            try
            {
                dynamic result = JsonConvert.DeserializeObject(json);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private bool IsValidJsonList(string json)
        {
            try
            {
                List<Dictionary<string, Object>> result = JsonConvert.DeserializeObject<List<Dictionary<string, Object>>>(json.ToString());
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }

}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.Resources;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.System
{
    public class SelectorPlugin : PluginBase
    {
        private readonly ILogger<SelectorPlugin> _logger;

        public SelectorPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper,
            IMysoftContextFactory mysoftContextFactory, ILogger<SelectorPlugin> logger) : base(kernel, memoryBuilder,
            httpContextAccessor, mapper,
            mysoftContextFactory)
        {
            _logger = logger;
        }

        [KernelFunction]
        [Description("比较")]
        public async Task<string> Comparison()
        {
            var chaRunDto = await GetChatRunDto();
            if (chaRunDto.Cancel) return string.Empty;
            var flowNode = await chaRunDto.GetFlowNode();
            if (flowNode == null || flowNode.Config == null || flowNode.Config.Conditions.Any() == false)
            {
                return string.Empty;
            }
            CheckComparison(flowNode.Name, flowNode.Config.Conditions);
            foreach (var condition in flowNode.Config.Conditions)
            {
                if (condition.Expressions == null) continue;
                foreach (var expression in condition.Expressions)
                {
                    foreach (var rule in expression.Rules)
                    {
                        string leftValue = rule.Left?.LiteralValue ?? string.Empty;
                        string rightValue = rule.Right?.LiteralValue ?? string.Empty;
                        int length = 0;
                        switch (rule.Operator)
                        {
                            // 等于
                            case "eq":
                                rule.Value = string.Equals(leftValue, rightValue, StringComparison.OrdinalIgnoreCase);
                                break;
                            // 不等于
                            case "neq":
                                rule.Value = !string.Equals(leftValue, rightValue, StringComparison.OrdinalIgnoreCase);
                                break;
                            // 大于
                            case "gt":
                            {
                                rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                             decimal.TryParse(rightValue, out decimal rightV) && leftV > rightV;
                                break;
                            }
                            // 大于等于
                            case "gte":
                            {
                                rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                             decimal.TryParse(rightValue, out decimal rightV) && leftV >= rightV;
                                break;
                            }
                            // 小于
                            case "lt":
                            {
                                rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                             decimal.TryParse(rightValue, out decimal rightV) && leftV < rightV;
                                break;
                            }
                            // 小于等于
                            case "lte":
                            {
                                rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                             decimal.TryParse(rightValue, out decimal rightV) && leftV <= rightV;
                                break;
                            }
                            // 长度大于
                            case "lgthgt":
                                if (int.TryParse(rightValue, out length))
                                {
                                    rule.Value = leftValue != null && rightValue != null && leftValue.Length > length;
                                }

                                break;
                            // 长度大于等于
                            case "lgthgte":
                                if (int.TryParse(rightValue, out length))
                                {
                                    rule.Value = leftValue != null && rightValue != null && leftValue.Length >= length;
                                }

                                break;
                            // 长度小于
                            case "lgthlt":
                                if (int.TryParse(rightValue, out length))
                                {
                                    rule.Value = leftValue != null && rightValue != null && leftValue.Length < length;
                                }

                                break;
                            // 长度小于等于
                            case "lgthlte":
                                if (int.TryParse(rightValue, out length))
                                {
                                    rule.Value = leftValue != null && rightValue != null && leftValue.Length <= length;
                                }

                                break;
                            // 包含
                            case "contains":
                                rule.Value = leftValue != null && rightValue != null &&
                                             leftValue.IndexOf(rightValue, StringComparison.OrdinalIgnoreCase) >= 0;
                                break;
                            // 不包含
                            case "notcontains":
                                rule.Value = leftValue == null || rightValue == null ||
                                             leftValue.IndexOf(rightValue, StringComparison.OrdinalIgnoreCase) < 0;
                                break;
                            // 为空
                            case "isempty":
                                rule.Value = string.IsNullOrWhiteSpace(leftValue);
                                break;
                            // 不为空
                            case "notisempty":
                                rule.Value = !string.IsNullOrWhiteSpace(leftValue);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }

            var result = flowNode.Config.Conditions.FirstOrDefault(x => x.Value)?.Target ?? string.Empty;
            _logger.LogInformation("表达式：" + result);
            return await Task.FromResult(result);
        }

        private void CheckComparison(string nodeName,List<Condition> conditions)
        {
            List<string> noCheckOperator = new List<string>() { "isempty" , "notisempty" };
            foreach (var condition in conditions)
            {
                foreach(var expression in condition.Expressions)
                {
                    foreach(var rule in expression.Rules)
                    {
                        if (string.IsNullOrEmpty(rule.Left.Value.Content) || (!noCheckOperator.Contains(rule.Operator) && string.IsNullOrEmpty(rule.Right.Value.Content)))
                        {
                            throw new NoRequiredArgumentException($"节点【{nodeName}】的分支【{condition.Title}】参数设置错误");
                        }
                    }
                }
            }
        }

        private async Task ClassificationCheck(ChatRunDto chatRunDto,FlowNode flowNode)
        {
            await NodeArgumentCheck(chatRunDto);
            foreach (var item in flowNode.Config.Classifications)
            {
                if (string.IsNullOrEmpty(item.Intention))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】意图【{item.Id}】没有设置");
                }
            }
        }

        [KernelFunction]
        [Description("意图识别")]
        public async Task<string> Classification(CancellationToken cancellationToken)
        {
            var chatRunDto = await GetChatRunDto();
            var flowNode = await chatRunDto.GetFlowNode();
            if (chatRunDto.Cancel) return string.Empty;;
            // 参数校验
            await ClassificationCheck(chatRunDto, flowNode);
            if (flowNode == null || flowNode.Config == null || flowNode.Config.Classifications.Any() == false)
            {
                return string.Empty;
            }

            var inputs = await chatRunDto.GetFlowNodeInputs();
            var input = "";
            input = inputs.FirstOrDefault(x => x.Code == nameof(input))?.LiteralValue ?? input;
            var classification = JsonConvert.SerializeObject(flowNode.Config.Classifications);
            var arguments = new KernelArguments { { nameof(classification), classification}};
            //执行提示词
            var result = await ChatCompletionByMemories(systemPromptName: EmbeddedResource.SelectorPlugin_Classification,
                input: input, arguments: arguments, serviceId: chatRunDto.ModelInstance.InstanceCode,
                cancellationToken: cancellationToken);
            // { "classificationId": 2, "reason": "The user is asking for a calculation." }
            Dictionary<string, object> keyValueDtos = JsonCompile(result);
            var outputs = await chatRunDto.GetFlowNodeOutputs();
            foreach (var item in outputs)
            {
                if (keyValueDtos.ContainsKey(item.Code))
                {
                    item.LiteralValue = keyValueDtos[item.Code] == null ? "" : keyValueDtos[item.Code].ToString();
                }
                else
                {
                    item.LiteralValue = "";
                }
                //await chatRunDto.AddNodeOutputArgument(item.Code, item.LiteralValue);
            }
            return await Task.FromResult(result);
        }
    }
}
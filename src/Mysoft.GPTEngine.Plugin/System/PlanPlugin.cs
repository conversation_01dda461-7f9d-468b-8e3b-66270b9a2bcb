using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.System
{
    public class PlanPlugin : PluginBase
    {
        private readonly ILogger<PlanPlugin> _logger;
        private readonly IMysoftApiService _mysoftApiService;

        public PlanPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper,
            IMysoftContextFactory mysoftContextFactory, IMysoftApiService mysoftApiService, ILogger<PlanPlugin> logger) : base(kernel, memoryBuilder,
            httpContextAccessor, mapper,
            mysoftContextFactory)
        {
            _logger = logger;
            _mysoftApiService = mysoftApiService;
        }

        [KernelFunction]
        [Description("智能检查")]
        public async Task SmartInspection()
        {
            var chaRunDto = await GetChatRunDto();
            if (chaRunDto.Cancel) return;
            var flowNode = await chaRunDto.GetFlowNode();
            if (flowNode.Config.Id == null || flowNode.Config.Id == "")
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】选择方案不能为空");
            }
            await NodeArgumentCheck(chaRunDto);
            _logger.LogInformation("智能检查: {0}", flowNode.Id);

            var inputs = await chaRunDto.GetFlowNodeInputs();
            Dictionary<string, object> planData = new Dictionary<string, object>();
            foreach (var input in inputs)
            {
                planData[input.Code] = input.LiteralValue;
            }
            
            var planResult = await executePlan(flowNode.Config.Id, planData, GetPlanMessageNodeLogDto(chaRunDto, flowNode));
            
            var interactiveCard = new FormCardDto();
            interactiveCard.Type = "plan";
            interactiveCard.Data.Id = planResult.PlanInstanceGUID;
            await chaRunDto.OutputsArgumentParser();
            foreach (var item in flowNode.Config.Outputs)
            {

                //await chaRunDto.AddNodeOutputArgument(item.Code, "");
            }

            interactiveCard.Data.NodeCode = flowNode.Code;

            interactiveCard.Data.Outputs = flowNode.Config.Outputs;
            await DataEvent(interactiveCard, true);
        }
        
        private async Task<PlanInstanceInfoDto> executePlan(string planId, Dictionary<string, object> planData, PlanMessageNodeLogDto planMessageNodeLogDto)
        {
            var context = _mysoftContextFactory.GetMysoftContext();
            ExecutePlanParamDto executePlanParamDto = new ExecutePlanParamDto()
            {
                PlanGUID = planId,
                Options = planData,
                PlanMessageNodeLogDto = planMessageNodeLogDto
            };
            var uri = "/pub/42001401/gpt/approval/executePlan";
            _logger.LogInformation("技能元数据更新准备请求参数：路径：{0},参数:{1}", context.GptBuilderUrl + uri, executePlanParamDto);
            var result = await _mysoftApiService.PostAsync(context.GptBuilderUrl + uri, JsonConvert.SerializeObject(executePlanParamDto));
            _logger.LogInformation("技能元数据更新返回结果了：{0}", JsonConvert.SerializeObject(result));
            var data = JsonConvert.DeserializeObject<MysoftApiResultDto>(result);

            if (data?.Success == false)
            {
                return null;
            }
            //对象data.Data转换为 PlanInstanceInfoDto
            return JsonConvert.DeserializeObject<PlanInstanceInfoDto>(data.Data.ToString());;
        }

        private PlanMessageNodeLogDto GetPlanMessageNodeLogDto(ChatRunDto chaRunDto, FlowNode flowNode)
        {
            PlanMessageNodeLogDto dto = new PlanMessageNodeLogDto();
            dto.ChatGUID = chaRunDto.Chat.ChatGUID;
            dto.NodeGUID = Guid.Parse(flowNode.Id);
            dto.BatchGUID = chaRunDto.BatchGuid;
            dto.Name = flowNode.Name;
            dto.Index = 0;
            var nodeLog = chaRunDto.NodeLogs.FirstOrDefault(x => x.NodeGUID == chaRunDto.Chat.CurrentNodeGUID);
            if (nodeLog != null)
            {
                dto.Index = nodeLog.Index;
            }

            return dto;
        }
        
    }
}
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.System;

namespace Mysoft.GPTEngine.Plugin
{
    public class FormBindingPlugin : PluginBase
    {
        private readonly ILogger<CardPlugin> _logger;

        public FormBindingPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper,
            IMysoftContextFactory mysoftContextFactory, ILogger<CardPlugin> logger) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _logger = logger;
        }

        [KernelFunction]
        [Description("辅助录入")]
        public async Task FormBinding()
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Cancel) return;
            AutoInputDto autoInputDto = new AutoInputDto();            
            var node = await chatRunDto.GetFlowNode();
            await FormBindingCheck(chatRunDto, node);
            var formId = node.Config.FormId;
            var inputs = await chatRunDto.GetFlowNodeInputs();
            List<KeyValueDto> keyValueDtos = new List<KeyValueDto>();
            foreach (var input in inputs)
            {
                KeyValueDto keyValue = new KeyValueDto();
                keyValue.Code = input.Code;
                keyValue.Value = input.LiteralValue;

                keyValueDtos.Add(keyValue);
            }
            autoInputDto.FormId = formId;
            autoInputDto.data = keyValueDtos;
            FormBindingDto formBindingDto = new FormBindingDto();
            formBindingDto.Data = autoInputDto;
            await DataEvent(formBindingDto, true);
        }

        private async Task FormBindingCheck(ChatRunDto chatRunDto, FlowNode node)
        {
            var config = node.Config;
            if (string.IsNullOrEmpty(config.AppCode))
            {
                throw new NoRequiredArgumentException($"节点【{node.Name}】子系统没有设置");
            }
            if (string.IsNullOrEmpty(config.FormId))
            {
                throw new NoRequiredArgumentException($"节点【{node.Name}】表单控件没有设置");
            }
            await NodeArgumentCheck(chatRunDto);
        }
    }
}

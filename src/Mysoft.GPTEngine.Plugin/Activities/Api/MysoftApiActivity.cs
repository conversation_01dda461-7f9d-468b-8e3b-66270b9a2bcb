using System;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.Activities.Api
{
    /// <summary>
    /// 明源ERP API
    /// </summary>
    public class MysoftApiActivity : ApiActivity
    {
        private readonly IMysoftApiService _mysoftApiService;
        
        
        public MysoftApiActivity(IServiceProvider serviceProvider, ILogger<MysoftApiActivity> logger
            , IMysoftApiService mysoftApiService, PluginRepostory pluginRepostory, MysoftMemoryCache mysoftMemoryCache, 
            PluginMetadataRepostory pluginMetadataRepostory, IMysoftContextFactory mysoftContextFactory,IConfigurationService configurationService) 
            : base(serviceProvider, logger, mysoftMemoryCache, configurationService,mysoftContextFactory, pluginRepostory, pluginMetadataRepostory)
        {
            _mysoftApiService = mysoftApiService;
        }

        [KernelFunction(nameof(MysoftApiActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }

        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            OpenApiRequestOptions openApiRequestOptions = await GetOpenApiRequestOptions(flowNode);
            MysoftApiResultDto? data;
            try
            {
                openApiRequestOptions.ServerUrlOverride = new Uri(_mysoftContextFactory.GetMysoftContext().GptBuilderUrl);
                openApiRequestOptions.AuthMode = 2;
                if (flowNode.Config.Async)
                {
                    RequestApi(openApiRequestOptions);
                    return;
                }
                    
                var result = await RequestApi(openApiRequestOptions);

                data = JsonConvert.DeserializeObject<MysoftApiResultDto>(result.ToString());

                if (data?.Success == false)
                {
                    string? errorMsg = data?.Error?.Exception?.Message;
                    if (string.IsNullOrEmpty(errorMsg))
                    {
                        errorMsg = data?.Message;
                    }

                    throw new BusinessException(errorMsg);
                }

                if (data?.Data == null) return;
                
                await GenerateOutputValue(flowNode, data.Data);
            }
            catch (Exception e)
            {
                string errMsg = string.Format(ErrMsgConst.Plugin_ErrMsg, openApiRequestOptions.Path, e.Message);

                throw new PluginException(errMsg, e);
            }
            // 推送Api事件 TODO待确认是否移除
            // flowNode.Config.Outputs.ForEach(async f =>
            // {
            //     FieldEvent fieldEvent = new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, f.Code), f.LiteralValue);
            //     await _chatRunDto.EventBus.PublishAsync(fieldEvent);
            // });

            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
        }
        
    }
}

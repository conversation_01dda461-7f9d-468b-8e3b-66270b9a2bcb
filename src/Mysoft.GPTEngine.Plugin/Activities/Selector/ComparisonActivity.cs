using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 选择器分支
    /// </summary>
    public class ComparisonActivity : SemanticKernelActivity<string>
    {
        public ComparisonActivity(IServiceProvider serviceProvider, ILogger<ComparisonActivity> logger) : base(serviceProvider, logger)
        {
        }

        [KernelFunction(nameof(ComparisonActivity))]
        [Description("选择器分支")]
        public new async Task<string> ExecuteAsync(CancellationToken cancellationToken)
        {
            return await base.ExecuteAsync(cancellationToken) ?? string.Empty;
        }
        protected override Task InitRefParams(FlowNode flowNode)
        {
            flowNode._refParams = new List<ParamDto>();
            foreach (var condition in flowNode.Config.Conditions)
            {
                if (condition.Expressions == null) continue;
                foreach (var expression in condition.Expressions)
                {
                    foreach (var rule in expression.Rules)
                    {
                        flowNode._refParams.Add(rule.Left);
                        flowNode._refParams.Add(rule.Right);
                    }
                }
            }
            return Task.CompletedTask;
        }
        protected override async Task<string?> PostExecuteActivityAsync(FlowNode flowNode, string? result, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, result, cancellationToken);
            if (flowNode == null || flowNode.Config == null || flowNode.Config.Conditions.Any() == false)
            {
                return string.Empty;
            }
            CheckComparison(flowNode.Name, flowNode.Config.Conditions);
            foreach (var condition in flowNode.Config.Conditions)
            {
                if (condition.Expressions == null) continue;
                foreach (var expression in condition.Expressions)
                {
                    foreach (var rule in expression.Rules)
                    {
                        string leftValue = rule.Left?.LiteralValue ?? string.Empty;
                        string rightValue = rule.Right?.LiteralValue ?? string.Empty;
                        int length = 0;
                        switch (rule.Operator)
                        {
                            // 等于
                            case "eq":
                                rule.Value = string.Equals(leftValue, rightValue, StringComparison.OrdinalIgnoreCase);
                                break;
                            // 不等于
                            case "neq":
                                rule.Value = !string.Equals(leftValue, rightValue, StringComparison.OrdinalIgnoreCase);
                                break;
                            // 大于
                            case "gt":
                                {
                                    rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                                 decimal.TryParse(rightValue, out decimal rightV) && leftV > rightV;
                                    break;
                                }
                            // 大于等于
                            case "gte":
                                {
                                    rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                                 decimal.TryParse(rightValue, out decimal rightV) && leftV >= rightV;
                                    break;
                                }
                            // 小于
                            case "lt":
                                {
                                    rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                                 decimal.TryParse(rightValue, out decimal rightV) && leftV < rightV;
                                    break;
                                }
                            // 小于等于
                            case "lte":
                                {
                                    rule.Value = decimal.TryParse(leftValue, out decimal leftV) &&
                                                 decimal.TryParse(rightValue, out decimal rightV) && leftV <= rightV;
                                    break;
                                }
                            // 长度大于
                            case "lgthgt":
                                length = GetLength(leftValue);
                                rule.Value =  length > int.Parse(rightValue);

                                break;
                            // 长度大于等于
                            case "lgthgte":
                                length = GetLength(leftValue);
                                rule.Value =  length >= int.Parse(rightValue);

                                break;
                            // 长度小于
                            case "lgthlt":
                                length = GetLength(leftValue);
                                rule.Value =  length < int.Parse(rightValue);

                                break;
                            // 长度小于等于
                            case "lgthlte":
                                length = GetLength(leftValue);
                                rule.Value =  length <= int.Parse(rightValue);

                                break;
                            // 包含
                            case "contains":
                                rule.Value = leftValue != null && rightValue != null &&
                                             leftValue.IndexOf(rightValue, StringComparison.OrdinalIgnoreCase) >= 0;
                                break;
                            // 不包含
                            case "notcontains":
                                rule.Value = leftValue == null || rightValue == null ||
                                             leftValue.IndexOf(rightValue, StringComparison.OrdinalIgnoreCase) < 0;
                                break;
                            // 为空
                            case "isempty":
                                rule.Value = string.IsNullOrWhiteSpace(leftValue);
                                break;
                            // 不为空
                            case "notisempty":
                                rule.Value = !string.IsNullOrWhiteSpace(leftValue);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }

            result = flowNode.Config.Conditions.FirstOrDefault(x => x.Value)?.Target ?? string.Empty;

            AddSucceedNodeLog(flowNode, JsonConvert.SerializeObject(flowNode.Config.Conditions), result);

            _logger.LogInformation("表达式：" + result);
            return await Task.FromResult(result);
        }
        
        private static int GetLength(string leftValue)
        {
            leftValue = JsonFixer.FixJson(leftValue);
            if (JsonUtility.TryDeserializeJsonStringObjectList<List<dynamic>>(leftValue, out var excuteResults))
            {
                return excuteResults.Count;
            }

            return leftValue.Length;
        }

        public async Task HandleAsync(FlowNode flowNode,FieldEvent @event)
        {
            if (flowNode._inputDependencies.Exists(e => e.NodeCode == @event.FlowCode) == false) return;
            await StreamEvent(@event.ToString());
        }

        private void CheckComparison(string nodeName, List<Condition> conditions)
        {

            List<string> noCheckOperator = new List<string>() { "isempty", "notisempty" };
            foreach (var condition in conditions)
            {
                foreach (var expression in condition.Expressions)
                {
                    foreach (var rule in expression.Rules)
                    {
                        if (string.IsNullOrEmpty(rule.Left.Value.Content) || (!noCheckOperator.Contains(rule.Operator) && string.IsNullOrEmpty(rule.Right.Value.Content)))
                        {
                            throw new NoRequiredArgumentException($"节点【{nodeName}】的分支【{condition.Title}】参数设置错误");
                        }
                    }
                }
            }
        }
    }
}

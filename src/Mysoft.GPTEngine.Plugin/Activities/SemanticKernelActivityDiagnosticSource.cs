using System;
using System.Diagnostics;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.Plugin.Activities
{
    internal class SemanticKernelActivityDiagnosticSource
    {
        private static readonly DiagnosticSource _diagnosticSource = new DiagnosticListener("SemanticKernelActivityDiagnosticListener");

        private const string Prefix = "Mysoft.GPTEngine.Plugin.SemanticKernelActivity.";
        private const string StartExecuteName = Prefix + nameof(StartExecute);
        private const string EndExecuteName = Prefix + nameof(EndExecute);
        private const string SetFlowNodeName = Prefix + nameof(SetFlowNode);

        public static void StartExecute(Guid operationId, string stepName, FlowNode? flowNode, long startTime)
        {
            if (_diagnosticSource.IsEnabled(StartExecuteName))
            {
                _diagnosticSource.Write(StartExecuteName, new { OperationId = operationId, StepName = stepName, FlowNode = flowNode, StartTime = startTime });
            }
        }

        public static void EndExecute(Guid operationId, long endTime)
        {
            if (_diagnosticSource.IsEnabled(EndExecuteName))
            {
                _diagnosticSource.Write(EndExecuteName, new { OperationId = operationId, EndTime = endTime });
            }
        }

        public static void SetFlowNode(Guid operationId, FlowNode flowNode)
        {
            if (_diagnosticSource.IsEnabled(SetFlowNodeName))
            {
                _diagnosticSource.Write(SetFlowNodeName, new { OperationId = operationId, FlowNode = flowNode });
            }
        }
    }

    internal class SemanticKernelActivityDiagnosticSourceScope : IDisposable
    {
        private readonly Guid _operationId;

        private string StepName { get; }

        private long StartTime { get; }


        public SemanticKernelActivityDiagnosticSourceScope(string stepName)
        {
            StartTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            StepName = stepName;
            _operationId = Guid.NewGuid();
            SemanticKernelActivityDiagnosticSource.StartExecute(_operationId, StepName, null, StartTime);
        }

        public SemanticKernelActivityDiagnosticSourceScope(string stepName, FlowNode flowNode)
        {
            StartTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            StepName = stepName;
            _operationId = Guid.NewGuid();
            SemanticKernelActivityDiagnosticSource.StartExecute(_operationId, StepName, flowNode, StartTime);
        }

        public void SetFlowNode(FlowNode? flowNode)
        {
            if (flowNode == null) return;
            SemanticKernelActivityDiagnosticSource.SetFlowNode(_operationId, flowNode);
        }

        public void Dispose()
        {
            SemanticKernelActivityDiagnosticSource.EndExecute(_operationId, DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
        }
    }
}
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Plugin.System;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 赋值录入
    /// </summary>
    public class FormBindingActivity : SemanticKernelActivity
    {
        public FormBindingActivity(IServiceProvider serviceProvider, ILogger<FormBindingActivity> logger) : base(serviceProvider, logger)
        {
        }
         
        [KernelFunction(nameof(FormBindingActivity))]
        [Description("辅助录入")]
        public new Task ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }

        protected override async Task ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.ExecuteActivityAsync(flowNode,cancellationToken);
        }
        
        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            AutoInputDto autoInputDto = new AutoInputDto();

            var node = _chatRunDto.GetFlowNode();
            var formId = node.Result.Config.FormId;
            var inputs = flowNode.Config.Inputs;
            List<KeyValueDto> keyValueDtos = new List<KeyValueDto>();
            foreach (var input in inputs)
            {
                KeyValueDto keyValue = new KeyValueDto();
                keyValue.Code = input.Code;
                keyValue.Value = input.LiteralValue;

                keyValueDtos.Add(keyValue);
            }
            autoInputDto.FormId = formId;
            autoInputDto.data = keyValueDtos;
            FormBindingDto formBindingDto = new FormBindingDto
            {
                Data = autoInputDto
            };
            await DataEvent(formBindingDto, _chatRunDto.IsStream);
        }
        
        public async Task HandleAsync(FieldEvent @event)
        {
            var flowNode = await _chatRunDto.GetFlowNode(@event.FlowCode);
            await StreamEvent(@event.ToString());
        }

    }
}

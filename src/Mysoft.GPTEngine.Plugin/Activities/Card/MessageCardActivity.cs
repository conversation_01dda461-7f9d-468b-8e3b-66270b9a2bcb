using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 消息卡片
    /// </summary>
    public class MessageActivity : SemanticKernelActivity<string>, IEventHandler<FieldEvent>
    {

        public MessageActivity(IServiceProvider serviceProvider, ILogger<MessageActivity> logger) : base(serviceProvider, logger)
        {
        }

        [KernelFunction(nameof(MessageActivity))]
        [Description("消息卡片")]
        public new Task<string?> ExecuteAsync(CancellationToken cancellationToken)
        {
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;

            if (flowNode._inputDependencies.Count > 0)
            {
                // 订阅事件
                _chatRunDto.EventBus.Subscribe<FieldEvent>(this, flowNode.Code);
            }

            return flowNode;
        }
        protected override async Task<string?> ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.ExecuteActivityAsync(flowNode, cancellationToken);

            var promptTemplate = await TemplateReplaceAsync(promptTemplateText: flowNode.Config.Content, arguments: GetRefArguments(flowNode));

            var body = new MessageActivityBody()
            {
                Code = flowNode.Code,
                Data = new MessageActivityBodyData()
                {
                    Content = promptTemplate,
                    Inputs = flowNode.Config.Inputs
                }
            };
            await DataEvent(body, _chatRunDto.IsStream);

            return promptTemplate;
        }
        protected override async Task<string?> PostExecuteActivityAsync(FlowNode flowNode, string? result, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, result, cancellationToken);
            var promptTemplate = await TemplateReplaceAsync(promptTemplateText: flowNode.Config.Content, arguments: GetRefArguments(flowNode));
            var body = new MessageActivityBody()
            {
                Code = flowNode.Code,
                Data = new MessageActivityBodyData()
                {
                    Content = promptTemplate,
                    Inputs = flowNode.Config.Inputs
                }
            };
            var setting = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
            await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.DataEvent, JsonConvert.SerializeObject(body, setting)));
            result = await TemplateReplaceAsync(promptTemplateText: result ?? flowNode.Config.Content, arguments: GetRefArguments(flowNode));
            AddSucceedNodeLog(flowNode, null, result);
            return result;
        }

        public async Task HandleAsync(FieldEvent @event, string code)
        {
            var flowNode = await _chatRunDto.GetFlowNode(code);
            if (flowNode._refParams.Exists(c => c.Value != null && c.Value.Content == @event.Key))
            {
                await StreamEvent(@event.ToString());
            }
        }
    }

    public class MessageActivityBody
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "message";

        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public MessageActivityBodyData Data { get; set; } = new MessageActivityBodyData();
    }

    public class MessageActivityBodyData
    {
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("inputs")]
        public List<ParamDto> Inputs { get; set; } = new List<ParamDto>();

        [JsonPropertyName("outputs")]
        public List<ParamDto> Outputs { get; set; } = new List<ParamDto>();
    }
}

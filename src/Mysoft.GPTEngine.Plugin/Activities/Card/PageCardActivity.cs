using System;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ParamValueDto = Mysoft.GPTEngine.SemanticKernel.Core.Dtos.ParamValueDto;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 页面卡片
    /// </summary>
    public class PageActivity : SemanticKernelActivity, IEventHandler<FieldEvent>
    {
        public PageActivity(IServiceProvider serviceProvider, ILogger<PageActivity> logger) : base(serviceProvider, logger)
        {
        }
        private string _nextNodeGuid;
        [KernelFunction(nameof(PageActivity))]
        [Description("Get Streaming ChatMessage Contents.")]
        public new Task ExecuteAsync(string? nextNodeGuid = null, CancellationToken cancellationToken = default)
        {
            _nextNodeGuid = nextNodeGuid ?? string.Empty;
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;

            var cardParamDtos = flowNode.Config.Props;
            var urlValue = cardParamDtos.FirstOrDefault(x => x.Name == "url")?.Value;
            if (urlValue == null) throw new NoRequiredArgumentException($"节点【{flowNode.Name}】卡片地址没有设置");
            // 手动填充一个URL的input信息
            var urlContent = JsonConvert.DeserializeObject<ParamValueDto>(urlValue.ToString());
            flowNode.Config.Inputs.Add(new ParamDto
            {
                Value = urlContent
            });
            await InitRefStreamingChats(flowNode);
            
            if (flowNode._inputDependencies.Count > 0)
            {
                // 订阅事件
                _chatRunDto.EventBus.Subscribe<FieldEvent>(this, flowNode.Code);
            }
            
            return flowNode;
        }
        
        protected override async Task ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.ExecuteActivityAsync(flowNode, cancellationToken);
            var cardParamDtos = flowNode.Config.Props;
            var urlValue = cardParamDtos.FirstOrDefault(x => x.Name == "url")?.Value;
            var urlContent = JsonConvert.DeserializeObject<CardParamValueDto>(urlValue.ToString());
            var newContent = urlContent.Type != "ref" ? urlContent.Content : await ResolveUrlContent(urlContent.Content, _chatRunDto);

            var props = cardParamDtos.Select(p => new PagePropDto
            {
                Name = p.Name,
                Value = p.Name == "url" ? newContent : p.Value
            }).ToList();

            var PageParamValueDto = new PageParamValueDto
            {
                Id = flowNode.Config.TemplateId,
                props = props,
                Outputs = flowNode.Config.Outputs,
                NodeCode = flowNode.Code,
                Inputs = flowNode.Config.Inputs

            };
            var pageCardDto = new PageCardDto
            {
                Next = _chatRunDto.GetNextNodeIdNotEnd(_nextNodeGuid),
                Data = PageParamValueDto,
                
            };
            await _chatRunDto.OutputsArgumentParser();
            await DataEvent(pageCardDto, _chatRunDto.IsStream);
            var setting = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
        }
        
        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
            var cardParamDtos = flowNode.Config.Props;
            var urlValue = cardParamDtos.FirstOrDefault(x => x.Name == "url")?.Value;
            var urlContent = JsonConvert.DeserializeObject<CardParamValueDto>(urlValue.ToString());
            var newContent = urlContent.Type != "ref" ? urlContent.Content : await ResolveUrlContent(urlContent.Content, _chatRunDto);

            var props = cardParamDtos.Select(p => new PagePropDto
            {
                Name = p.Name,
                Value = p.Name == "url" ? newContent : p.Value
            }).ToList();
            var PageParamValueDto = new PageParamValueDto
            {
                Id = flowNode.Config.TemplateId,
                props = props,
                Outputs = flowNode.Config.Outputs,
                NodeCode = flowNode.Code,
                Inputs = flowNode.Config.Inputs

            };
            var pageCardDto = new PageCardDto
            {
                Next = _chatRunDto.GetNextNodeIdNotEnd(_nextNodeGuid),
                Data = PageParamValueDto,
                
            };
            var setting = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
            await AddMessage(ChatRoleConstant.Assistant, string.Format(EventDataConstant.DataEvent, JsonConvert.SerializeObject(pageCardDto, setting)));

            await Task.CompletedTask;
        }
        
        private async Task<string> ResolveUrlContent(string content, ChatRunDto chatRunDto)
        {
            if (content == KernelArgumentsConstant.Input || content.StartsWith(KernelArgumentsConstant._prefixKeyword))
            {
                return await chatRunDto.GetArgumentValue<string>(content);
            }

            var keys = content.Split('_');
            if (keys.Length < 3) throw new ArgumentException("Invalid content format.");
            var prefix = keys[0];
            var nodeCode = keys[1];
            var parameterCode = content.Replace($"{prefix}_{nodeCode}_", "");
            var paramFlowNode = await chatRunDto.GetFlowNode(nodeCode);
            var item = prefix == KernelArgumentsConstant._prefixNodeInput
                ? paramFlowNode.Config.Inputs.FirstOrDefault(x => x.Code == parameterCode)
                : paramFlowNode.Config.Outputs.FirstOrDefault(x => x.Code == parameterCode);

            if (item == null) throw new ArgumentNullException(nameof(item));

            return string.IsNullOrWhiteSpace(item.LiteralValue)
                ? await chatRunDto.GetArgumentValue<string>(content)
                : item.LiteralValue;
        }
        
        protected override async Task InitRefParams(FlowNode flowNode)
        {
            await base.InitRefParams(flowNode);
            if (flowNode.Config.Outputs.Count > 0)
            {
                flowNode._refParams.AddRange(flowNode.Config.Outputs);
            }
        }
        
   
        
        public async Task HandleAsync(FieldEvent @event, string code)
        {
            var flowNode = await _chatRunDto.GetFlowNode(code);
            if (flowNode._refParams.Exists(c => c.Value != null && c.Value.Content == @event.Key))
            {
                await StreamEvent(@event.ToString());
            }
        }
    }
}

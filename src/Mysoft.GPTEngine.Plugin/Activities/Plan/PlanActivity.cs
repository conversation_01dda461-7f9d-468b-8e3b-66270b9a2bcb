using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Domain.Entity.Approval;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Repositories.Approval;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Mysoft.GPTEngine.Plugin.Activities.Card
{
    /// <summary>
    /// 选择器分支
    /// </summary>
    public class PlanActivity : SemanticKernelActivity
    {
        private readonly IMysoftApiService _mysoftApiService;

        private readonly PlanInstanceRepostory _planInstanceRepostory;

        private readonly PlanRepostory _planRepostory;

        private readonly ChatMessageNodeLogEntityRepostory _chatMessageNodeLogEntityRepostory;
        
        private string _nextNodeGuid;

        private int _checkIntervalMs = 2000;
        private int _maxAttempts = 900; // 最多30分钟

        public PlanActivity(IServiceProvider serviceProvider, IMysoftApiService mysoftApiService, ILogger<ComparisonActivity> logger
        , PlanInstanceRepostory planInstanceRepostory, PlanRepostory planRepostory, ChatMessageNodeLogEntityRepostory chatMessageNodeLogEntityRepostory) : base(serviceProvider, logger)
        {
            _mysoftApiService = mysoftApiService;
            _planInstanceRepostory = planInstanceRepostory;
            _planRepostory = planRepostory;
            _chatMessageNodeLogEntityRepostory = chatMessageNodeLogEntityRepostory;
        }

        [KernelFunction(nameof(PlanActivity))]
        [Description("智能检查")]
        public new Task ExecuteAsync(string? nextNodeGuid, CancellationToken cancellationToken)
        {
            _nextNodeGuid = nextNodeGuid ?? string.Empty;
            return base.ExecuteAsync(cancellationToken);
        }
        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            var flowNode = await base.PreExecuteActivityAsync(cancellationToken);
            if (flowNode == null) return flowNode;

            //todo： 需要识别是否还需要加校验
            await NodeArgumentCheck(flowNode);
            return flowNode;
        }
        protected override async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            if (flowNode.Config.Id == null || flowNode.Config.Id == "")
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】选择方案不能为空");
            }

            _logger.LogInformation("智能检查: {0}, {1}", flowNode.Name, flowNode.Id);
            
            // 如果通过参数指定了方案ID，则认为是执行方案技能，直接通过方案的参数去技能入参中匹配
            if (await CheckPlanIdExecuted(flowNode)) return;
            
            // 流程执行的时候，会提前执行，所以，这里只需要查询状态
            if (await CheckPlanInstanceIsExecuted()) return;
            
            // 如果没有流程实例ID，也没有方案ID，则任务是技能触发的任务
            await ExecuteSkill(flowNode);

            await base.PostExecuteActivityAsync(flowNode, cancellationToken);
        }
        
        private async Task<bool> CheckPlanIdExecuted(FlowNode flowNode)
        {
            var planId = "";
            if (_chatRunDto.ChatArguments.TryGetValue("System_Plan_Id", out var systemPlanId))
            {
                planId = systemPlanId?.ToString();
            }


            if (string.IsNullOrEmpty(planId))
            {
                return await Task.FromResult(false);
            }

            _chatRunDto.ChatArguments.TryGetValue("System_Plan_BusinessId", out var businessId);
            _chatRunDto.ChatArguments.TryGetValue("System_Plan_WorkSpaceCode", out var workSpaceCode);

            var planData = new Dictionary<string, object>();
            if (_chatRunDto.ChatArguments.TryGetValue("System_Plan_Options", out var Options))
            {
                planData = JsonConvert.DeserializeObject<Dictionary<string, object>>(Options?.ToString() ?? "{}");
            }
            
            var dynamicRules = new Dictionary<string, List<string>>();
            if (_chatRunDto.ChatArguments.TryGetValue("System_Plan_Dynamic_Rules", out var rules))
            {
                dynamicRules = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(rules?.ToString() ?? "{}");
            }

            SetChatMessagePlanGUID(planId);
            ExecutePlanParamDto executePlanParamDto = new ExecutePlanParamDto()
            {
                PlanGUID = planId,
                WorkSpaceCode = workSpaceCode?.ToString() ?? "",
                Options = planData,
                Rules = dynamicRules,
                BusinessId = businessId?.ToString() ?? "",
                PlanMessageNodeLogDto = GetPlanMessageNodeLogDto(_chatRunDto, flowNode),
                PlanInstanceGUID = _chatRunDto.Chat.ChatGUID.ToString() //指定回话ID，方案实例ID和会话实例ID相同
            };
            var planResult = await ExecutePlan(executePlanParamDto, flowNode);

            if (_chatRunDto.IsStream)
            {
                await SendDataEvent(flowNode, planResult, planId);
            }
            else
            {
                await GenerateOutputValue(planResult);
                _chatRunDto.Next = true;
                _chatRunDto.NextId = _nextNodeGuid;
                _httpContextAccessor.AddItem(nameof(ChatRunDto), _chatRunDto);
            }
            return await Task.FromResult(true);
            
        }

        private async Task ExecuteSkill(FlowNode flowNode)
        {
            await _chatRunDto.InputsArgumentParser();
            var inputs = await _chatRunDto.GetFlowNodeInputs();
            Dictionary<string, object> planData = new Dictionary<string, object>();
            foreach (var input in inputs)
            {
                planData[input.Code] = input.LiteralValue;
            }
            ExecutePlanParamDto executePlanParamDto = new ExecutePlanParamDto()
            {
                PlanGUID = flowNode.Config.Id,
                Options = planData,
                PlanMessageNodeLogDto = GetPlanMessageNodeLogDto(_chatRunDto, flowNode)
            };
            var planResult = await ExecutePlan(executePlanParamDto, flowNode);

            await SendDataEvent(flowNode, planResult, flowNode.Config.Id);
        }

        private async Task SendDataEvent(FlowNode flowNode, PlanInstanceInfoDto planResult, string planId)
        {
            var planCardActivityBody = new PlanCardActivityBody();
            planCardActivityBody.Data.Id = planResult.PlanInstanceGUID;
            planCardActivityBody.Data.Description = await GetPlanDescribe(Guid.Parse(planId));
            planCardActivityBody.Next = _chatRunDto.GetNextNodeIdNotEnd(_nextNodeGuid);
            planCardActivityBody.Code = flowNode.Code;
            await _chatRunDto.OutputsArgumentParser();
            foreach (var item in flowNode.Config.Outputs)
            {
                await AddNodeOutputArgument(flowNode, item.Code, "");
            }

            await DataEvent(planCardActivityBody, _chatRunDto.IsStream);
            var setting = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
            var data = JsonConvert.SerializeObject(planCardActivityBody, setting);
            await AddMessage(AuthorRole.Assistant.Label, string.Format(EventDataConstant.DataEvent, data));
        }

        private async Task<string> GetPlanDescribe(Guid planGUID)
        {
            PlanEntity plan = await _planRepostory.GetAsync(f => f.PlanGUID == planGUID);
            return plan == null ? string.Empty : plan.Describe;
        }

        private async Task<bool> CheckPlanInstanceIsExecuted()
        {
            var instanceId = "";
            PlanInstanceEntity entity = new PlanInstanceEntity();
            if (_chatRunDto.ChatArguments.TryGetValue("System_Plan_InstanceId", out var planInstanceId))
            {
                instanceId = planInstanceId?.ToString();
                entity = await _planInstanceRepostory.GetFirstAsync(x => x.PlanInstanceGUID == instanceId);
            }
            else if (_chatRunDto.ChatArguments.TryGetValue("System_Plan_BusinessId", out var busindessId))
            {
                entity = await _planInstanceRepostory.GetFirstAsync(x => x.BusinessId == busindessId);
            }
            if(entity == null)
            {
                return false;
            }
            instanceId = entity.PlanInstanceGUID;
            if (!string.IsNullOrEmpty(instanceId))
            {
                var planCardActivityBody = new PlanCardActivityBody();
                planCardActivityBody.Data.Id = instanceId;
                planCardActivityBody.Data.Description = await GetPlanDescribe(entity.PlanGUID);
                planCardActivityBody.Next = _chatRunDto.GetNextNodeIdNotEnd(_nextNodeGuid);
                _chatRunDto.SaveLog = false;
                _httpContextAccessor.AddItem(nameof(ChatRunDto), _chatRunDto);
                await DataEvent(planCardActivityBody, _chatRunDto.IsStream);
                return await Task.FromResult(true);
            }
            return await Task.FromResult(false);
        }

        private async Task<PlanInstanceInfoDto> ExecutePlan(ExecutePlanParamDto executePlanParamDto, FlowNode flowNode)
        {
            var context = _mysoftContextFactory.GetMysoftContext();
            
            var uri = "/pub/42001401/gpt/approval/executePlanOnSite";
            _logger.LogInformation("技能元数据更新准备请求参数：路径：{0}，参数:{1}", context.GptBuilderUrl + uri, executePlanParamDto);
            var result = await _mysoftApiService.PostAsync(context.GptBuilderUrl + uri, JsonConvert.SerializeObject(executePlanParamDto));
            _logger.LogInformation("技能元数据更新返回结果：{0}", JsonConvert.SerializeObject(result));
            var data = JsonConvert.DeserializeObject<MysoftApiResultDto>(result);

            if (data?.Success == false)
            {
                var errorMsg = string.IsNullOrEmpty(data.Message) ? JsonConvert.SerializeObject(data.Error) : data.Message;
                throw new BusinessException("方案执行异常:" + errorMsg);
            }

            if (data is null || string.IsNullOrEmpty(data.Data.ToString()))
            {
                throw new BusinessException("执行方案响应结果为空");
            }

            //对象data.Data转换为 PlanInstanceInfoDto
            var planInstanceInfoDto = JsonConvert.DeserializeObject<PlanInstanceInfoDto>(data.Data.ToString());
            if (planInstanceInfoDto is null)
            {
                throw new BusinessException("执行方案响应结果解析为空");
            }
            
            return await AfterExecutePlan(context, flowNode, planInstanceInfoDto);
        }
        
        private async Task<PlanInstanceInfoDto> AfterExecutePlan(MysoftContext context, FlowNode flowNode, PlanInstanceInfoDto planInstanceInfoDto) 
        {
            if (_chatRunDto.ChatInput.EvalTaskGUID is null) return planInstanceInfoDto;

            // 评测任务轮询等待执行结束
            var searchResultUri = "/api/42001401/plan/searchExecuteResult";
            var attempt = 1;

            var param = new SearchExecuteResultParamDto
            {
                InstanceIds = new List<string> { planInstanceInfoDto.PlanInstanceGUID }
            };
            
            while (attempt < _maxAttempts)
            {
                try
                {
                    await Task.Delay(_checkIntervalMs);
                    _logger.LogInformation("查询检查方案 {0} 执行结果, GUID: {1}，次数：{2}", planInstanceInfoDto.PlanName, planInstanceInfoDto.PlanInstanceGUID, attempt);
                    var resp = await _mysoftApiService.PostAsync(context.GptBuilderUrl + searchResultUri,
                        JsonConvert.SerializeObject(param));
                    var respData = JsonConvert.DeserializeObject<MysoftApiResultDto>(resp);
                    if (respData?.Success == false)
                    {
                        var errorMsg = string.IsNullOrEmpty(respData.Message)
                            ? JsonConvert.SerializeObject(respData.Error)
                            : respData.Message;
                        throw new BusinessException("查询方案执行结果异常:" + errorMsg);
                    }

                    if (respData is null || string.IsNullOrEmpty(respData.Data.ToString()))
                    {
                        throw new BusinessException("方案执行结果为空");
                    }
                    
                    List<PlanInstanceInfoDto> planInstanceInfoList = JsonConvert.DeserializeObject<List<PlanInstanceInfoDto>>(respData.Data.ToString());
                    if (planInstanceInfoList is null || planInstanceInfoList.Count == 0)
                    {
                        throw new BusinessException("方案执行信息为空");
                    }

                    planInstanceInfoDto = planInstanceInfoList[0];
                    if (planInstanceInfoDto.Status == (int)ApprovalPlanStatusEnum.Success || planInstanceInfoDto.Status == (int)ApprovalPlanStatusEnum.Fail)
                    {
                        // 评测时无法正常返回结果
                        // 相关代码逻辑：ApprovalAppService.SavePlanActivityNodeLog
                        Dictionary<string, object> map = new Dictionary<string, object>();
                        map.Add("result", planInstanceInfoDto.Result);
                        map.Add("ruleGroups", JsonConvert.SerializeObject(planInstanceInfoDto.RuleGroups));
                        // 添加节点输出
                        foreach (var item in flowNode.Config.Outputs)
                        {
                            if (map.TryGetValue(item.Code, out var value))
                            {
                                item.LiteralValue = value.ToString() ?? string.Empty;
                            }
                        }
                        
                        _logger.LogInformation("检查方案 {0} 执行结束, GUID: {1}", planInstanceInfoDto.PlanName, planInstanceInfoDto.PlanInstanceGUID);
                        break;
                    }
                }
                catch (BusinessException e)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "查询方案执行结果异常");
                    throw;
                }
                finally
                {
                    attempt++;
                }
            }

            return planInstanceInfoDto;
        }

        private PlanMessageNodeLogDto GetPlanMessageNodeLogDto(ChatRunDto chaRunDto, FlowNode flowNode)
        {
            PlanMessageNodeLogDto dto = new PlanMessageNodeLogDto();
            dto.ChatGUID = chaRunDto.Chat.ChatGUID;
            dto.NodeGUID = Guid.Parse(flowNode.Id);
            dto.BatchGUID = chaRunDto.BatchGuid;
            dto.Name = flowNode.Name;
            dto.Index = 0;
            var nodeLog = chaRunDto.NodeLogs.FirstOrDefault(x => x.NodeGUID == chaRunDto.Chat.CurrentNodeGUID);
            if (nodeLog != null)
            {
                dto.Index = nodeLog.Index;
            }
            return dto;
        }
        
        public async Task GenerateOutputValue(object data)
        {
            var outputs = await _chatRunDto.GetFlowNodeOutputs();
            if (outputs == null || outputs.Count == 0) return;
            try
            {
                foreach (var output in outputs)
                {
                    if (output.Code == "ruleGroups")
                    {
                        output.LiteralValue = JsonConvert.SerializeObject(data);
                    }
                }
            }
            catch(Exception ex)
            {
                if (outputs.Count == 1)
                {
                    outputs.First().LiteralValue = data?.ToString() ?? string.Empty;
                    return;
                }
            }
        }

        private void SetChatMessagePlanGUID(string planId)
        {
            var chatMessagesWithEmptyGuid = _chatRunDto.ChatMessages.Where(cm => cm.ChatMessageGUID == Guid.Empty).ToList();
            foreach (var messageEntity in chatMessagesWithEmptyGuid)
            {
                messageEntity.PlanGUID = planId;
            }
        }
    }
    public class PlanCardActivityBody
    {
        public PlanCardActivityBody() { 
            this.Data = new PlanCardActivityBodyData();
        }

        [JsonPropertyName("type")]
        public string Type { get; set; } = "plan";

        [JsonPropertyName("next")]
        public string Next { get; set; } = string.Empty;

        [JsonPropertyName("code")]
        public string Code { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public PlanCardActivityBodyData Data { get; set; } = new PlanCardActivityBodyData();
    }

    public class PlanCardActivityBodyData
    {
        public string Id { get; set; }

        public string Description { get; set; }
    }
}

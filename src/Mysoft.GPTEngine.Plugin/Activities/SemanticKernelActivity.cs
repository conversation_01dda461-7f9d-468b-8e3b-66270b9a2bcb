using System;
using System.ClientModel;
using System.ClientModel.Primitives;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.ML.Tokenizers;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Event;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using OpenAI.Chat;
using static Mysoft.GPTEngine.Domain.Shared.Dtos.FlowNode;
using JsonSerializer = System.Text.Json.JsonSerializer;
using KernelBuilderExtensions = Mysoft.GPTEngine.SemanticKernel.Core.Extensions.KernelBuilderExtensions;

namespace Mysoft.GPTEngine.Plugin.Activities
{
    public class SemanticKernelActivity : SemanticKernelActivity<None>, IEventHandler<LlmResultEvent>
    {
        public SemanticKernelActivity(IServiceProvider serviceProvider, ILogger<TActivity> logger) : base(serviceProvider, logger)
        {
        }
        [KernelFunction]
        [Description("Get Streaming ChatMessage Contents.")]
        public new virtual async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            using (var executeActivityAsync = new SemanticKernelActivityDiagnosticSourceScope($"{GetType().Name}"))
            {
                FlowNode? flowNode;
                using (var scopePreExecuteActivityAsync = new SemanticKernelActivityDiagnosticSourceScope("PreExecuteActivityAsync"))
                {
                    flowNode = await PreExecuteActivityAsync(cancellationToken);
                    scopePreExecuteActivityAsync.SetFlowNode(flowNode);
                }

                if (flowNode == null) return;
                _cancellationToken = cancellationToken;

                using (var scopeExecuteActivityAsync = new SemanticKernelActivityDiagnosticSourceScope("ExecuteActivityAsync", flowNode))
                {
                    await ExecuteActivityAsync(flowNode, _cancellationToken);
                }
                using (var scopeExcuteCurrentFlowNode = new SemanticKernelActivityDiagnosticSourceScope("ExcuteCurrentFlowNode", flowNode))
                {
                    await ExcuteCurrentFlowNode(flowNode, cancellationToken);
                }

                if (nodeTypeNeedWait.Contains(flowNode.Type) && flowNode._inputDependencies?.Count > 0)
                {
                    using (var scopeAwaitRefStreamingChats = new SemanticKernelActivityDiagnosticSourceScope("AwaitRefStreamingChats", flowNode))
                    {
                        await AwaitRefStreamingChats(flowNode, cancellationToken);
                    }
                    using (var scopePostExecuteActivityAsync = new SemanticKernelActivityDiagnosticSourceScope("PostExecuteActivityAsync", flowNode))
                    {
                        await PostExecuteActivityAsync(flowNode, _cancellationToken);
                    }
                }

                if (flowNode._inputDependencies?.Count == 0)
                {
                    using (var scopePostExecuteActivityAsync = new SemanticKernelActivityDiagnosticSourceScope("PostExecuteActivityAsync", flowNode))
                    {
                        await PostExecuteActivityAsync(flowNode, _cancellationToken);
                    }
                }
            }
        }

        protected override async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            return await base.PreExecuteActivityAsync(cancellationToken);
        }
        protected new virtual async Task ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.ExecuteActivityAsync(flowNode, cancellationToken);
        }
        protected new virtual async Task PostExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            await base.PostExecuteActivityAsync(flowNode, null, cancellationToken);
        }
        public new async Task HandleAsync(LlmResultEvent @event, string code)
        {
            if (@event.FlowCode != code) return;
            await LlmResultHandleAsync(@event);
        }
    }


    public class SemanticKernelActivity<TResult> : TActivity, IEventHandler<LlmResultEvent> where TResult : class
    {
        private readonly IServiceProvider _serviceProvider;
        public readonly IHttpContextAccessor _httpContextAccessor;
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly IMapper _mapper;
        public readonly ILogger<TActivity> _logger;
        public static readonly Tokenizer s_tokenizer = Tokenizer.CreateTiktokenForModel("gpt-4");
        public ChatRunDto _chatRunDto;
        public Kernel _kernel;
        public CancellationToken _cancellationToken;
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        public readonly List<string> nodeTypeNeedWait = new List<string>()
        {
            SkillNodeTypeConstant.FormBinding,
            SkillNodeTypeConstant.Plan,
            SkillNodeTypeConstant.Card,
            SkillNodeTypeConstant.Classification,
            SkillNodeTypeConstant.Selector,
            SkillNodeTypeConstant.End,
            SkillNodeTypeConstant.PromptTemplateNode,
            SkillNodeTypeConstant.DocumentAnalysisNode,
            SkillNodeTypeConstant.ImageAnalysisNode,
            SkillNodeTypeConstant.DataQuery
        };

        #region 格式化提示词
        public virtual string GetOutputPrompt(string jsonSchema)
        {
            return string.Format(@"
## 输出要求 
{0}
- 输出应按照符合 JSON 模式的格式进行格式化，不得添加注释。
- 不得添加解释", jsonSchema);
        }

        #endregion
        public SemanticKernelActivity(IServiceProvider serviceProvider, ILogger<TActivity> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(IServiceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(ILogger));
            _httpContextAccessor = _serviceProvider.GetService<IHttpContextAccessor>() ?? throw new ArgumentNullException(nameof(IHttpContextAccessor));
            _mysoftContextFactory = _serviceProvider.GetService<IMysoftContextFactory>() ?? throw new ArgumentNullException(nameof(IMysoftContextFactory));
            _kernel = _serviceProvider.GetService<Kernel>() ?? throw new ArgumentNullException(nameof(Kernel));
            _mapper = _serviceProvider.GetService<IMapper>() ?? throw new ArgumentNullException(nameof(IMapper));
            _modelInstanceRepostory = _serviceProvider.GetService<ModelInstanceRepostory>() ?? throw new ArgumentNullException(nameof(ModelInstanceRepostory));
        }
        public virtual async Task<TResult?> ExecuteAsync(CancellationToken cancellationToken)
        {
            using (var executeActivityScope = new SemanticKernelActivityDiagnosticSourceScope($"{GetType().Name}.ExecuteAsync"))
            {
                FlowNode? flowNode = await PreExecuteActivityAsync(cancellationToken);
                if (flowNode == null) return default;
                _cancellationToken = cancellationToken;
                using (var executeActivityAsyncScope = new SemanticKernelActivityDiagnosticSourceScope("ExecuteActivityAsync", flowNode))
                {
                    var _result = await ExecuteActivityAsync(flowNode, cancellationToken);
                    await ExcuteCurrentFlowNode(flowNode, cancellationToken);

                    if (nodeTypeNeedWait.Contains(flowNode.Type) && flowNode._inputDependencies?.Count > 0)
                    {
                        using (var awaitRefStreamingChatsScope = new SemanticKernelActivityDiagnosticSourceScope("AwaitRefStreamingChats", flowNode))
                        {
                            await AwaitRefStreamingChats(flowNode, cancellationToken);
                        }
                        using (var postExecuteActivityScope = new SemanticKernelActivityDiagnosticSourceScope("PostExecuteActivityAsync", flowNode))
                        {
                            _result = await PostExecuteActivityAsync(flowNode, _result, _cancellationToken);
                        }
                        return await Task.FromResult(_result);
                    }

                    if (flowNode._inputDependencies?.Count == 0)
                    {
                        using (var postExecuteActivityScope = new SemanticKernelActivityDiagnosticSourceScope("PostExecuteActivityAsync", flowNode))
                        {
                            _result = await PostExecuteActivityAsync(flowNode, _result, _cancellationToken);
                        }
                    }

                    return await Task.FromResult(_result);
                }
            }
        }
        // 节点输出到对话框的场景
        public virtual async Task ExcuteCurrentFlowNode(FlowNode flowNode, CancellationToken cancellationToken)
        {
            if (!flowNode.ExcuteCurrentFlowNode) return;
            var awaitNode = await _chatRunDto.GetFlowNode(flowNode.Code);
            await _chatRunDto.ArgumentParser(flowNode, flowNode._refParams);
            // 1、先找到当前节点的委托
            LlmGetStreamingChatMessage? llmGetStreamingChatMessage = flowNode._inputDependencies.FirstOrDefault(f => f.NodeCode == flowNode.Code)?.nodeDependencyHandler;
            if (llmGetStreamingChatMessage == null) return;
            // 2、先执行父级依赖
            flowNode._inputDependencies.RemoveAll(f => f.NodeCode == flowNode.Code);
            await AwaitRefStreamingChats(flowNode, cancellationToken);
            // 3、再执行当前节点
            _ = await llmGetStreamingChatMessage(awaitNode, cancellationToken);
        }
        public virtual async Task AwaitRefStreamingChats(FlowNode flowNode, CancellationToken cancellationToken)
        {
            // 等待其他节点的耗时，不计算在当前节点上
            flowNode.Stopwatch.Stop();
            await ExcuteDependencies(flowNode, flowNode._inputDependencies, cancellationToken);
            await _chatRunDto.ArgumentParser(flowNode, flowNode._refParams);
            flowNode.Stopwatch.Start();
        }
        // 递归执行入参依赖
        public virtual async Task ExcuteDependencies(FlowNode flowNode, List<NodeDependency> nodeDependencies, CancellationToken cancellationToken)
        {
            foreach (NodeDependency nodeDependency in nodeDependencies)
            {
                if (nodeDependency.IsFinish || _chatRunDto.outputCache.ContainsKey(nodeDependency.NodeCode))
                {
                    continue;
                }
                FlowNode dependentFlowNode = await _chatRunDto.GetFlowNode(nodeDependency.NodeCode);
                
                _logger.LogDebug("执行节点依赖，当前节点：{0}, 依赖节点：{1}", flowNode.Name, dependentFlowNode.Name);
                // 执行到委托就重新计时
                dependentFlowNode.Stopwatch.Restart();
                dependentFlowNode.Stopwatch.Stop();
                // 深度优先 
                await ExcuteDependencies(dependentFlowNode, nodeDependency.ParentDependency, cancellationToken);
                dependentFlowNode.Stopwatch.Start();
                var awaitNode = await _chatRunDto.GetFlowNode(nodeDependency.NodeCode);
                _ = await nodeDependency.nodeDependencyHandler(awaitNode, cancellationToken);
                // 参数赋值，记录日志
                await PostThisExecuteActivityAsync(dependentFlowNode, null, cancellationToken);
                // 存入缓存
                _chatRunDto.UpdateOutputCache(dependentFlowNode.Code, dependentFlowNode._outParams);
                // 标记已完成
                nodeDependency.IsFinish = true;
            }
        }
        protected virtual async Task FileArgumentCheck(FlowNode flowNode)
        {
            foreach (var item in flowNode.Config.Files)
            {
                if (item.Required && (item.Value == null || string.IsNullOrEmpty(item.Value.Content)))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】字段【{item.Name ?? item.Code}】没有设置");
                }
                if (item.Required && string.IsNullOrEmpty(item.LiteralValue))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】未上传文件");
                }
            }
            if (flowNode.Config.Outputs.Count == 0)
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】输出参数不能为空");
            }
            await Task.CompletedTask;
        }

        private bool IsBrowserRequest()
        {
            var userAgent = _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].FirstOrDefault();
            if (string.IsNullOrEmpty(userAgent))
                return false;
            
            var browserKeywords = new[] { "Chrome", "Firefox", "Safari", "Edge", "Opera", "MSIE", "Trident" };
            return browserKeywords.Any(keyword => userAgent.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        protected virtual async Task<FlowNode?> PreExecuteActivityAsync(CancellationToken cancellationToken)
        {
            _chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto)) ?? throw new ArgumentNullException(nameof(ChatRunDto));
            var flowNode = _chatRunDto.GetFlowNode().GetAwaiter().GetResult() ?? throw new ArgumentNullException(nameof(FlowNode));

            //_flowNodeCode = _flowNode.Code;

            if (_chatRunDto.Cancel) return null;

            // 开始节点计时
            flowNode.Stopwatch.Start();

            _chatRunDto.EventBus.Subscribe<LlmResultEvent>(this, flowNode.Code);

            await NodeArgumentCheck(flowNode);

            await InitRefParams(flowNode);

            await InitRefStreamingChats(flowNode);

            await _chatRunDto.ArgumentParser(flowNode, flowNode._refParams);

            await InitOutParams(flowNode);

            AddNodeLog(flowNode);

            return flowNode;
        }
        protected virtual async Task InitRefStreamingChats(FlowNode flowNode)
        {
            foreach (var input in flowNode._refParams)
            {
                if (string.IsNullOrEmpty(input.Value?.Content) || input.Value.Type == "literal") continue;
                var data = await _chatRunDto.GetLiteralValue(input.Value?.Content);
                if (data.Item1 is null)
                {
                    continue;
                }
                if ((data.Item2 is LlmGetStreamingChatMessage chatMessage && flowNode._inputDependencies.Exists(e => e.NodeCode == data.Item1) == false))
                {
                    NodeDependency nodeDependency = new NodeDependency()
                    {
                        NodeCode = data.Item3.Code,
                        ParentDependency = data.Item3._inputDependencies,
                        nodeDependencyHandler = chatMessage
                    };
                    flowNode._inputDependencies.Add(nodeDependency);
                    continue;
                }
            }
        }
        protected virtual async Task<TResult?> ExecuteActivityAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            return await Task.FromResult<TResult?>(default);
        }
        protected virtual async Task<TResult?> PostExecuteActivityAsync(FlowNode flowNode, TResult? result, CancellationToken cancellationToken)
        {
            return await PostThisExecuteActivityAsync(flowNode, result, cancellationToken);
        }

        private async Task<TResult?> PostThisExecuteActivityAsync(FlowNode flowNode, TResult? result, CancellationToken cancellationToken)
        {
            await _chatRunDto.InputsArgumentParser(flowNode);
            await _chatRunDto.OutputsArgumentParser(flowNode);

            foreach (var item in flowNode.Config.Outputs)
            {
                await AddNodeOutputArgument(flowNode, item.Code, item.LiteralValue);
            }

            _logger.LogInformation("节点：{0}, 类型：{1}, 执行PostExecuteActivityAsync", flowNode.Name, flowNode.Type);
            AddSucceedNodeLog(flowNode);
            flowNode.Stopwatch.Stop();
            return result;
        }

        public async Task AddNodeOutputArgument(FlowNode flowNode, string key, string value)
        {
            var argumentKey = string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, key);
            await _chatRunDto.AddArgument(argumentKey, value);
        }

        protected virtual async Task NodeArgumentCheck(FlowNode flowNode)
        {
            var inputs = await _chatRunDto.GetFlowNodeInputs();
            var outputs = await _chatRunDto.GetFlowNodeOutputs();
            var node = await _chatRunDto.GetFlowNode();
            foreach (var item in inputs)
            {
                string fieldInfo = string.IsNullOrEmpty(item.Name) ? item.Code : item.Code;
                if (string.IsNullOrEmpty(item.Code))
                {
                    throw new NoRequiredArgumentException($"节点【{node.Name}】输入参数配置缺失");
                }
                if (item.Required && (item.Value == null || string.IsNullOrEmpty(item.Value.Content)))
                {
                    throw new NoRequiredArgumentException($"节点【{node.Name}】参数【{fieldInfo}】没有设置");
                }
                //if (item.Required && string.IsNullOrEmpty(item.LiteralValue))
                //{
                //    throw new NoRequiredArgumentException($"节点【{node.Name}】参数【{fieldInfo}】没有传值");
                //}
            }
            // 开始节点不校验输出配置
            if (flowNode.Type != "Start")
            {
                validateOutputs(outputs, node.Name);
            }
        }
        public void validateOutputs(List<ParamDto> paramDtos, string nodeName)
        {
            foreach (var item in paramDtos)
            {
                string fieldInfo = string.IsNullOrEmpty(item.Name) ? item.Code : item.Code;
                if (item == null)
                {
                    return;
                }
                if (string.IsNullOrEmpty(item.Code))
                {
                    throw new NoRequiredArgumentException($"节点【{nodeName}】输出参数配置缺失");
                }
                if (item.Required && (item.Value == null || string.IsNullOrEmpty(item.Value.Content)))
                {
                    throw new NoRequiredArgumentException($"节点【{nodeName}】参数【{fieldInfo}】没有设置");
                }
                if (item.Schema != null && item.Schema.Count > 0)
                {
                    validateOutputs(item.Schema, nodeName);
                }
            }
        }
        /// <summary>
        /// 初始化引用参数
        /// </summary>
        /// <returns></returns>
        protected virtual async Task InitRefParams(FlowNode flowNode)
        {
            flowNode._refParams = new List<ParamDto>();
            if (flowNode?.Config?.Inputs?.Count > 0)
            {
                flowNode._refParams.AddRange(flowNode?.Config?.Inputs);
            }

            if (flowNode?.Config?.Files?.Count > 0)
            {
                flowNode._refParams.AddRange(flowNode?.Config?.Files);
            }

            await Task.CompletedTask;
        }
        protected virtual async Task InitOutParams(FlowNode flowNode)
        {
            flowNode._outParams = flowNode?.Config?.Outputs ?? new List<ParamDto>();
            await Task.CompletedTask;
        }

        protected virtual KernelArguments GetRefArguments(FlowNode flowNode)
        {
            var arguments = new KernelArguments();
            foreach (var item in flowNode.Config.Inputs)
            {
                if (!string.IsNullOrEmpty(item.Code) && !string.IsNullOrEmpty(item.LiteralValue))
                {
                    arguments.Add(item.Code, item.LiteralValue);
                }
            }
            return arguments;
        }

        /// <summary>
        /// 当前节点的提示词执行玩这种事件
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        protected virtual async Task LlmResultHandleAsync(LlmResultEvent @event)
        {
            await Task.CompletedTask;
        }
        #region private
        //public async Task AddFirstOutput(object result)
        //{
        //    var outputs = await _chatRunDto.GetFlowNodeOutputs().ConfigureAwait(false);
        //    if (outputs == null || outputs.Count == 0) return;

        //    if (result is string resultString)
        //    {
        //        outputs.First().LiteralValue = resultString;
        //    }
        //    if (result is LlmGetStreamingChatMessage chatMessage)
        //    {
        //        outputs.First().Value = new SemanticKernel.Core.Dtos.ParamValueDto { SyncMessage = chatMessage };
        //    }
        //    await Task.CompletedTask;
        //}
        #endregion

        #region llm
        public async Task<TemplateDto> TemplateRenderAsync(string promptTemplateText, KernelArguments? arguments, Kernel? kernel = null)
        {
            kernel = kernel ?? _kernel;
            var promptTemplateFactory = new KernelPromptTemplateFactory();

            var promptTemplateConfig = new PromptTemplateConfig(promptTemplateText)
            {
                //AllowUnsafeContent = true
            };
            var promptTemplate = promptTemplateFactory.Create(promptTemplateConfig);

            if (kernel == null) throw new ArgumentNullException(nameof(kernel));

            var template = new TemplateDto
            {
                Content = await promptTemplate.RenderAsync(kernel, arguments),
                Inputs = promptTemplateConfig.InputVariables.Select(x => new ParamDto
                {
                    Code = x.Name,
                    LiteralValue = arguments?.GetValueOrDefault(x.Name)?.ToString()
                }).ToList()
            };

            template.Content = HttpUtility.HtmlDecode(template.Content);

            return await Task.FromResult(template);
        }
        public async Task<string> TemplateReplaceAsync(string promptTemplateText, KernelArguments arguments)
        {
            var promptTemplateFactory = new KernelPromptTemplateFactory();

            foreach (var item in arguments)
            {
                if (item.Value != null)
                {
                    promptTemplateText = promptTemplateText.Replace("{{$" + item.Key + "}}", item.Value.ToString());
                }
            }

            return await Task.FromResult(HttpUtility.HtmlDecode(promptTemplateText));
        }

        public async Task<ChatHistory> CreateChatHistory(FlowNode flowNode, PromptDto prompt, KernelArguments? arguments)
        {
            ChatHistory chatHistory;
            if (prompt.MessageContents == null || prompt.MessageContents.Count == 0)
            {
                var promptTemplate = await TemplateRenderAsync(promptTemplateText: prompt.PromptTemplate, arguments: arguments);

                chatHistory = await CreateChatHistory(flowNode);
                await AddMemoriesChatHistory(flowNode, chatHistory);
                chatHistory.AddUserMessage(promptTemplate.Content);
            }
            else
            {
                var promptTemplate = await TemplateRenderAsync(promptTemplateText: prompt.PromptTemplate, arguments: arguments);

                chatHistory = await CreateChatHistory(flowNode, promptTemplate.Content);
                await AddMemoriesChatHistory(flowNode, chatHistory);
                foreach (var item in prompt.MessageContents)
                {
                    promptTemplate = await TemplateRenderAsync(promptTemplateText: item.Content, arguments: arguments);
                    if (item.Role == AuthorRole.User.Label)
                    {
                        chatHistory.AddUserMessage(promptTemplate.Content);
                    }
                    else if (item.Role == AuthorRole.Assistant.Label)
                    {
                        chatHistory.AddAssistantMessage(promptTemplate.Content);
                    }
                }
            }
            _logger.LogInformation("节点：{0}，提示词节点ChatHistory的数据：{1}", flowNode.Name, JsonConvert.SerializeObject(chatHistory));
            return await Task.FromResult(chatHistory);
        }
        public async Task<ChatHistory> AddMemoriesChatHistory(FlowNode flowNode, ChatHistory chatHistory)
        {
            if (_chatRunDto.Nodes.Count == 0) return await Task.FromResult(chatHistory);
            var memories = flowNode.Config.Memories;
            var nodeGUID = Guid.Parse(flowNode.Id);
            if (memories == 0)
            {
                return await Task.FromResult(chatHistory);
            }
            var nodeLogs = _chatRunDto.NodeLogs.Where(x => x.NodeGUID == nodeGUID && x.BatchGUID.ToString() != "00000000-0000-0000-0000-000000000000").OrderByDescending(x => x.Index).Take(memories).OrderBy(x => x.Index).ToList();
            foreach (var nodeLog in nodeLogs)
            {

                chatHistory.AddUserMessage(nodeLog.Inputs);
                chatHistory.AddAssistantMessage(nodeLog.Outputs);
            }
            return await Task.FromResult(chatHistory);
        }

        public async Task<ChatHistory> CreateChatHistory(FlowNode flowNode, string? systemMessage = null, int history = 0)
        {
            var chatHistory = systemMessage == null ? new ChatHistory() : new ChatHistory(systemMessage);
            if (history == 0) return await Task.FromResult(chatHistory);

            if (_chatRunDto?.ChatMessages != null && _chatRunDto.ChatMessages.Count > 0)
            {
                var nodeGUID = Guid.Parse(flowNode.Id);
                var chatMessage = _chatRunDto.ChatMessages.Where(x => x.NodeGUID == nodeGUID).OrderByDescending(x => x.Index).Take(2 * history).OrderBy(x => x.Index).ToList();
                foreach (var message in chatMessage)
                {
                    switch (message.Role)
                    {
                        case ChatRoleConstant.User:
                            chatHistory.AddUserMessage(message.Content); break;
                        case ChatRoleConstant.Assistant:
                            chatHistory.AddAssistantMessage(message.Content); break;
                        default:
                            break;
                    }
                }
            }

            return await Task.FromResult(chatHistory);
        }

        /**
         * 立即执行ChatCompletion
         */
        public async Task<string> ChatCompletionExec(FlowNode flowNode, string systemPromptName, string input, KernelArguments? arguments = null, string? serviceId = null, string? executionSetting = null, PublishEventConfig? publishEventConfig = null, CancellationToken cancellationToken = default, int history = 3)
        {
            LlmGetStreamingChatMessage chatMessage = await ChatCompletion(flowNode, systemPromptName, input, arguments, serviceId, executionSetting, publishEventConfig, cancellationToken, history);
            return await chatMessage(flowNode, cancellationToken);
        }
        public async Task<string> ChatCompletionExec(FlowNode flowNode, ChatHistory chatHistory, string? serviceId = null, string? executionSetting = null, PublishEventConfig? publishEventConfig = null, CancellationToken cancellationToken = default)
        {
            LlmGetStreamingChatMessage chatMessage = await ChatCompletion(flowNode, chatHistory, serviceId, executionSetting, publishEventConfig, cancellationToken);
            return await chatMessage(flowNode, cancellationToken);
        }
        public async Task<LlmGetStreamingChatMessage> ChatCompletion(FlowNode flowNode, string systemPromptName, string input, KernelArguments? arguments = null, string? serviceId = null, string? executionSetting = null, PublishEventConfig? publishEventConfig = null, CancellationToken cancellationToken = default, int history = 3)
        {
            arguments = arguments ?? await _chatRunDto.GetNodeInputArgument();
            var promptTemplateText = EmbeddedResource.Read(systemPromptName);
            var systemPrompy = await TemplateRenderAsync(promptTemplateText: promptTemplateText, arguments: arguments);
            var chatHistory = await CreateChatHistory(flowNode, systemMessage: systemPrompy.Content, history: history);
            await AddMemoriesChatHistory(flowNode, chatHistory);
            if (string.IsNullOrWhiteSpace(input) == false)
            {
                chatHistory.AddUserMessage(input);
            }
            return await ChatCompletion(flowNode: flowNode, chatHistory: chatHistory, serviceId: serviceId, executionSetting: executionSetting, publishEventConfig, cancellationToken: cancellationToken);
        }

        /**
         * 创建对话完成委托
         */
        public async Task<LlmGetStreamingChatMessage> ChatCompletion(FlowNode flowNode, ChatHistory chatHistory, string? serviceId = null, string? executionSetting = null, PublishEventConfig? publishEventConfig = null, CancellationToken cancellationToken = default)
        {
            LlmGetStreamingChatMessage chatMessage = new LlmGetStreamingChatMessage(GetStreamingChatMessageContentsAsync);

            return await Task.FromResult(chatMessage);
        }

        protected virtual async Task<ChatHistory> ChatMessages(FlowNode flowNode, string promptGuid)
        {
            var chatHistory = flowNode._chatHistory;
            if (chatHistory != null)
            {
                return await Task.FromResult(chatHistory);
            }
            return await Task.FromResult<ChatHistory?>(default);
        }

        #region 流式聊天消息内容获取相关常量
        private const int MaxRetryCount = 10; // 最大重试次数
        private const int InitialDelaySeconds = 1; // 初始延迟秒数
        private const int MinJitterMs = 500; // 最小抖动毫秒数
        private const int MaxJitterMs = 2000; // 最大抖动毫秒数
        private const string DefaultOutputParamCode = "output"; // 默认输出参数代码
        private const string CancellationMessage = "线程被取消"; // 取消消息
        private const string ResourceWaitingMessage = "当前请求较多，小助手正在为您安排资源，预计 1 分钟内完成计算，感谢您的耐心等待~ \n"; // 资源等待消息
        private const string OutputTypeInconsistentMessage = "节点输出类型与提示词不一致，请更新技能。"; // 输出类型不一致消息
        #endregion

        /// <summary>
        /// 获取流式聊天消息内容的主入口方法
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>聊天消息内容</returns>
        public async Task<string> GetStreamingChatMessageContentsAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            // 1. 初始化并验证前置条件
            var (executionSettings, chatHistory, chatCompletionService, modelInstance) =
                await InitializeStreamingChatAsync(flowNode, cancellationToken);

            // 2. 执行带重试机制的流式处理
            return await ExecuteStreamingWithRetryAsync(
                flowNode,
                chatHistory,
                executionSettings,
                chatCompletionService,
                modelInstance,
                cancellationToken);
        }

        /// <summary>
        /// 初始化流式聊天相关组件
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行设置、聊天历史、聊天完成服务和模型实例的元组</returns>
        private async Task<(PromptExecutionSettings?, ChatHistory, IChatCompletionService, ModelInstanceDto)>
            InitializeStreamingChatAsync(FlowNode flowNode, CancellationToken cancellationToken)
        {
            // 验证内核是否已初始化
            ValidateKernel();

            // 初始化执行设置
            var executionSettings = InitializeExecutionSettings(flowNode);
            // 验证并获取聊天历史
            var chatHistory = await ValidateAndGetChatHistory(flowNode);
            // 获取聊天完成服务和模型实例
            var (chatCompletionService, modelInstance) = await GetChatCompletionServiceAndModel(flowNode);

            // 记录聊天历史日志
            LogChatHistory(chatHistory);
            // 注册取消回调
            RegisterCancellationCallback(cancellationToken);

            return (executionSettings, chatHistory, chatCompletionService, modelInstance);
        }

        /// <summary>
        /// 验证内核是否已初始化
        /// </summary>
        private void ValidateKernel()
        {
            if (_kernel == null)
                throw new ArgumentNullException(nameof(_kernel));
        }

        /// <summary>
        /// 初始化提示执行设置
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <returns>提示执行设置</returns>
        private PromptExecutionSettings? InitializeExecutionSettings(FlowNode flowNode)
        {
            if (string.IsNullOrWhiteSpace(flowNode._executionSetting))
                return null;

            return KernelBuilderExtensions.DeserializePromptExecutionSettings(flowNode._executionSetting);
        }

        /// <summary>
        /// 验证并获取聊天历史
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <returns>聊天历史</returns>
        private async Task<ChatHistory> ValidateAndGetChatHistory(FlowNode flowNode)
        {
            var chatHistory = await ChatMessages(flowNode, flowNode._promptGuid);

            // 检查是否包含用户输入
            if (!chatHistory.Any(f => f.Role == AuthorRole.User))
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】用户输入缺失，无法调用大模型");
            }

            return chatHistory;
        }

        /// <summary>
        /// 获取聊天完成服务和模型实例
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <returns>聊天完成服务和模型实例的元组</returns>
        private async Task<(IChatCompletionService service, ModelInstanceDto modelInstance)>
            GetChatCompletionServiceAndModel(FlowNode flowNode)
        {
            var defaultModelInstance = _chatRunDto.ModelInstance;
            IChatCompletionService chatCompletionService;
            ModelInstanceDto chatCompletionModelInstance = defaultModelInstance;

            // 根据节点配置选择合适的聊天完成服务
            if (!string.IsNullOrWhiteSpace(flowNode._serviceId))
            {
                // 使用指定的服务ID
                chatCompletionService = await GetChatCompletionService(flowNode, flowNode._serviceId);
                chatCompletionModelInstance = await GetModelInstance(flowNode._serviceId, chatCompletionModelInstance);
            }
            else if (defaultModelInstance != null && !defaultModelInstance.IsDefault)
            {
                // 使用非默认的模型实例
                chatCompletionService = await GetChatCompletionService(flowNode, defaultModelInstance.InstanceCode);
                flowNode._serviceId = defaultModelInstance.InstanceCode;
            }
            else
            {
                // 使用默认服务
                chatCompletionService = await GetChatCompletionService(flowNode, null);
            }

            return (chatCompletionService, chatCompletionModelInstance);
        }

        /// <summary>
        /// 记录聊天历史日志
        /// </summary>
        /// <param name="chatHistory">聊天历史</param>
        private void LogChatHistory(ChatHistory chatHistory)
        {
            foreach (var message in chatHistory)
            {
                // 提取文本内容用于日志记录
                string logContent = string.Join(" ", message.Items
                    .Where(item => item is TextContent)
                    .Select(item => (item as TextContent)?.Text));
                _logger.LogInformation("调用大模型内容 - Role: {Role}, Content: {Content}", message.Role, logContent);
            }
        }

        /// <summary>
        /// 注册取消回调函数
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private void RegisterCancellationCallback(CancellationToken cancellationToken)
        {
            cancellationToken.Register(() => { Console.WriteLine(CancellationMessage); });
        }

        /// <summary>
        /// 执行带重试机制的流式处理
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="chatHistory">聊天历史</param>
        /// <param name="executionSettings">执行设置</param>
        /// <param name="chatCompletionService">聊天完成服务</param>
        /// <param name="modelInstance">模型实例</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果</returns>
        private async Task<string> ExecuteStreamingWithRetryAsync(
            FlowNode flowNode,
            ChatHistory chatHistory,
            PromptExecutionSettings? executionSettings,
            IChatCompletionService chatCompletionService,
            ModelInstanceDto modelInstance,
            CancellationToken cancellationToken)
        {
            var retryState = new RetryState
            {
                RetryCount = 0,
                Delay = TimeSpan.FromSeconds(InitialDelaySeconds)
            };
            var random = new Random(); // 用于生成随机抖动

            while (true)
            {
                try
                {
                    // 尝试处理流式聊天
                    return await ProcessStreamingChatAsync(
                        flowNode,
                        chatHistory,
                        executionSettings,
                        chatCompletionService,
                        modelInstance,
                        cancellationToken);
                }
                catch (HttpOperationException httpEx)
                {
                    await ProcessRetryLogicAsync(
                        () => HandleHttpOperationException(httpEx, flowNode, retryState.RetryCount, cancellationToken),
                        () => CreateHttpExceptionForMaxRetry(httpEx, flowNode),
                        "HttpOperation",
                        httpEx.Message,
                        retryState,
                        random,
                        cancellationToken);
                }
                catch (ClientResultException clientEx)
                {
                    await ProcessRetryLogicAsync(
                        () => HandleClientResultException(clientEx, flowNode, retryState.RetryCount, cancellationToken),
                        () => CreateClientExceptionForMaxRetry(clientEx, flowNode),
                        "ClientResult",
                        clientEx.Message,
                        retryState,
                        random,
                        cancellationToken);
                }
                catch (LLmCustomException llmEx)
                {
                    await ProcessRetryLogicAsync(
                        () => HandleLLmCustomException(llmEx, flowNode, retryState.RetryCount, cancellationToken),
                        () => llmEx, // 直接返回原异常
                        "LLmCustom",
                        llmEx.Message,
                        retryState,
                        random,
                        cancellationToken);
                }
            }
        }

        /// <summary>
        /// 重试状态信息
        /// </summary>
        private class RetryState
        {
            public int RetryCount { get; set; }
            public TimeSpan Delay { get; set; }
        }

        /// <summary>
        /// 通用重试逻辑处理
        /// </summary>
        /// <param name="handleExceptionFunc">异常处理函数</param>
        /// <param name="createMaxRetryExceptionFunc">创建最大重试异常函数</param>
        /// <param name="exceptionType">异常类型名称</param>
        /// <param name="exceptionMessage">异常消息</param>
        /// <param name="retryState">重试状态</param>
        /// <param name="random">随机数生成器</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ProcessRetryLogicAsync(
            Func<Task<bool>> handleExceptionFunc,
            Func<Exception> createMaxRetryExceptionFunc,
            string exceptionType,
            string exceptionMessage,
            RetryState retryState,
            Random random,
            CancellationToken cancellationToken)
        {
            // 处理特定异常
            var shouldRetry = await handleExceptionFunc();

            if (!shouldRetry)
                throw createMaxRetryExceptionFunc();

            retryState.RetryCount++;
            // 检查是否超过最大重试次数
            if (retryState.RetryCount > MaxRetryCount)
            {
                throw createMaxRetryExceptionFunc();
            }

            _logger.LogError("第 {0} 次重试 - {1}异常: {2}", retryState.RetryCount, exceptionType, exceptionMessage);

            // 计算当前延迟时间（包含抖动）
            var currentDelayWithJitter = CalculateRetryDelay(retryState.RetryCount, retryState.Delay, random);
            await Task.Delay(currentDelayWithJitter, cancellationToken);

            // 更新下次迭代的延迟时间
            retryState.Delay = GetNextRetryDelay(retryState.RetryCount);
        }

        /// <summary>
        /// 为HTTP异常创建最大重试时的异常
        /// </summary>
        /// <param name="httpEx">HTTP异常</param>
        /// <param name="flowNode">流程节点</param>
        /// <returns>LLmCustomException</returns>
        private LLmCustomException CreateHttpExceptionForMaxRetry(HttpOperationException httpEx, FlowNode flowNode)
        {
            var errMsg = string.Format(ErrMsgConst.LLM_ErrMsgOthers,
                flowNode._serviceId, (int)httpEx.StatusCode, httpEx.ResponseContent ?? httpEx.Message);
            return new LLmCustomException("LLM", errMsg);
        }

        /// <summary>
        /// 为ClientResult异常创建最大重试时的异常
        /// </summary>
        /// <param name="clientEx">ClientResult异常</param>
        /// <param name="flowNode">流程节点</param>
        /// <returns>LLmCustomException</returns>
        private LLmCustomException CreateClientExceptionForMaxRetry(ClientResultException clientEx, FlowNode flowNode)
        {
            var errMsg = string.Format(ErrMsgConst.LLM_ErrMsgOthers,
                flowNode._serviceId, clientEx.Status, clientEx.Message);
            return new LLmCustomException("LLM", errMsg);
        }

        /// <summary>
        /// 处理HTTP操作异常
        /// </summary>
        /// <param name="httpEx">HTTP操作异常</param>
        /// <param name="flowNode">流程节点</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否应该重试</returns>
        private async Task<bool> HandleHttpOperationException(
            HttpOperationException httpEx,
            FlowNode flowNode,
            int retryCount,
            CancellationToken cancellationToken)
        {
            var exMessage = httpEx.ResponseContent ?? httpEx.Message;
            string errMsg = string.Format(ErrMsgConst.LLM_ErrMsgOthers, flowNode._serviceId, (int)httpEx.StatusCode, exMessage);

            // 判断是否应该重试的状态码和错误类型
            bool shouldRetry = ShouldRetryHttpException(httpEx, exMessage);

            if (!shouldRetry)
            {
                throw new LLmCustomException("LLM", errMsg);
            }

            // 首次重试时显示等待消息
            if (retryCount == 0 && IsBrowserRequest())
            {
                await _chatRunDto.EventBus.PublishAsync(new TextEvent(flowNode.Code, ResourceWaitingMessage));
            }

            return true; // 应该重试
        }

        /// <summary>
        /// 处理ClientResult异常
        /// </summary>
        /// <param name="clientEx">ClientResult异常</param>
        /// <param name="flowNode">流程节点</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否应该重试</returns>
        private async Task<bool> HandleClientResultException(
            ClientResultException clientEx,
            FlowNode flowNode,
            int retryCount,
            CancellationToken cancellationToken)
        {
            var exMessage = clientEx.Message;
            string errMsg = string.Format(ErrMsgConst.LLM_ErrMsgOthers, flowNode._serviceId, clientEx.Status, exMessage);

            // 判断是否应该重试的状态码和错误类型
            bool shouldRetry = ShouldRetryClientResultException(clientEx, exMessage);

            if (!shouldRetry)
            {
                throw new LLmCustomException("LLM", errMsg);
            }

            // 首次重试时显示等待消息
            if (retryCount == 0  && IsBrowserRequest())
            {
                await _chatRunDto.EventBus.PublishAsync(new TextEvent(flowNode.Code, ResourceWaitingMessage));
            }

            return true; // 应该重试
        }

        /// <summary>
        /// 处理LLmCustom异常
        /// </summary>
        /// <param name="llmEx">LLmCustom异常</param>
        /// <param name="flowNode">流程节点</param>
        /// <param name="retryCount">重试次数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否应该重试</returns>
        private async Task<bool> HandleLLmCustomException(
            LLmCustomException llmEx,
            FlowNode flowNode,
            int retryCount,
            CancellationToken cancellationToken)
        {
            var exMessage = llmEx.Message;

            // 判断是否应该重试
            bool shouldRetry = ShouldRetryLLmCustomException(llmEx, exMessage);

            if (!shouldRetry)
            {
                throw llmEx; // 直接抛出原异常
            }

            // 首次重试时显示等待消息
            if (retryCount == 0 && IsBrowserRequest())
            {
                await _chatRunDto.EventBus.PublishAsync(new TextEvent(flowNode.Code, ResourceWaitingMessage));
            }

            return true; // 应该重试
        }

        /// <summary>
        /// 判断ClientResult异常是否应该重试
        /// </summary>
        /// <param name="clientEx">ClientResult异常</param>
        /// <param name="exMessage">异常消息</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryClientResultException(ClientResultException clientEx, string exMessage)
        {
            // HTTP 429 请求过多 - 应该重试
            if (clientEx.Status == 429)
            {
                return true;
            }

            // 检查是否是配额不足相关的错误消息
            if (!string.IsNullOrEmpty(exMessage))
            {
                var lowerMessage = exMessage.ToLower();
                // 配额不足、请求过多等应该重试
                if (lowerMessage.Contains("insufficient_quota") ||
                    lowerMessage.Contains("quota") ||
                    lowerMessage.Contains("rate limit") ||
                    lowerMessage.Contains("too many requests") ||
                    lowerMessage.Contains("throttling") ||
                    lowerMessage.Contains("exceeded your current quota"))
                {
                    return true;
                }
            }
            // 其他情况不重试
            return false;
        }

        /// <summary>
        /// 判断LLmCustom异常是否应该重试
        /// </summary>
        /// <param name="llmEx">LLmCustom异常</param>
        /// <param name="exMessage">异常消息</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryLLmCustomException(LLmCustomException llmEx, string exMessage)
        {
            // 检查是否是配额不足相关的错误消息
            if (!string.IsNullOrEmpty(exMessage))
            {
                var lowerMessage = exMessage.ToLower();
                // 配额不足、请求过多等应该重试
                if (lowerMessage.Contains("insufficient_quota") ||
                    lowerMessage.Contains("quota") ||
                    lowerMessage.Contains("rate limit") ||
                    lowerMessage.Contains("too many requests") ||
                    lowerMessage.Contains("denied due") ||
                    lowerMessage.Contains("throttling") ||
                    lowerMessage.Contains("exceeded your current quota"))
                {
                    return true;
                }
            }

            // 其他情况不重试
            return false;
        }

        /// <summary>
        /// 判断HTTP异常是否应该重试
        /// </summary>
        /// <param name="httpEx">HTTP操作异常</param>
        /// <param name="exMessage">异常消息</param>
        /// <returns>是否应该重试</returns>
        private bool ShouldRetryHttpException(HttpOperationException httpEx, string exMessage)
        {
            var statusCode = (int)httpEx.StatusCode;

            // 429 请求过多 - 应该重试
            if (statusCode == 429)
            {
                return true;
            }

            // 检查是否是流复制相关的错误消息
            if (!string.IsNullOrEmpty(exMessage))
            {
                var lowerMessage = exMessage.ToLower();
                // 流复制错误、连接重置、网络错误等应该重试
                if (lowerMessage.Contains("error while copying content to a stream") ||
                    lowerMessage.Contains("connection reset") ||
                    lowerMessage.Contains("network error") ||
                    lowerMessage.Contains("timeout") ||
                    lowerMessage.Contains("connection aborted"))
                {
                    return true;
                }
            }

            return false; // 其他情况不重试
        }

        /// <summary>
        /// 计算重试延迟时间（包含抖动）
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <param name="currentDelay">当前延迟时间</param>
        /// <param name="random">随机数生成器</param>
        /// <returns>包含抖动的延迟时间</returns>
        private TimeSpan CalculateRetryDelay(int retryCount, TimeSpan currentDelay, Random random)
        {
            // 使用指数退避策略并添加随机抖动
            var jitter = TimeSpan.FromMilliseconds(random.Next(MinJitterMs, MaxJitterMs));
            var totalDelay = currentDelay + jitter;

            // 注意：此方法返回当前延迟时间加上抖动，用于立即使用
            // 调用方法应该更新延迟变量以供下次迭代使用
            return totalDelay;
        }

        /// <summary>
        /// 获取下次重试的延迟时间
        /// </summary>
        /// <param name="retryCount">重试次数</param>
        /// <returns>下次重试的延迟时间</returns>
        private TimeSpan GetNextRetryDelay(int retryCount)
        {
            // 重试延迟策略：1s, 5s, 15s, 30s, 60s...
            return retryCount switch
            {
                1 => TimeSpan.FromSeconds(5),
                2 => TimeSpan.FromSeconds(15),
                3 => TimeSpan.FromSeconds(30),
                _ => TimeSpan.FromSeconds(59)
            };
        }

        /// <summary>
        /// 处理流式聊天的核心方法
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="chatHistory">聊天历史</param>
        /// <param name="executionSettings">执行设置</param>
        /// <param name="chatCompletionService">聊天完成服务</param>
        /// <param name="modelInstance">模型实例</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果</returns>
        private async Task<string> ProcessStreamingChatAsync(
            FlowNode flowNode,
            ChatHistory chatHistory,
            PromptExecutionSettings? executionSettings,
            IChatCompletionService chatCompletionService,
            ModelInstanceDto modelInstance,
            CancellationToken cancellationToken)
        {
            // 初始化流式聊天
            // 注释：阿里云百炼的qwen-long模型，限制文件上传最大qps为5
            flowNode.StreamingChats = chatCompletionService.GetStreamingChatMessageContentsAsync(
                chatHistory: chatHistory,
                executionSettings: executionSettings,
                cancellationToken: cancellationToken);
            flowNode.PublishEventConfig = flowNode._publishEventConfig;

            // 重置节点开始时间
            ResetNodeStartTime(flowNode);

            // 判断是否启用深度思考
            var enableThinking = IsDeepThinkingEnabled(executionSettings, modelInstance);

            // 验证输出类型一致性
            ValidateOutputTypeConsistency(flowNode);

            // 确定输出格式
            var shouldOutputJsonObject = ShouldOutputJsonObject(flowNode);

            // 处理流式响应
            return await ProcessStreamingResponse(flowNode, enableThinking, shouldOutputJsonObject, cancellationToken);
        }

        /// <summary>
        /// 判断是否启用深度思考功能
        /// </summary>
        /// <param name="executionSettings">执行设置</param>
        /// <param name="modelInstance">模型实例</param>
        /// <returns>是否启用深度思考</returns>
        private bool IsDeepThinkingEnabled(PromptExecutionSettings? executionSettings, ModelInstanceDto? modelInstance)
        {
            if (executionSettings == null || modelInstance == null)
                return false;

            try
            {
                // 序列化执行设置以便解析
                var executionSettingsJson = JsonSerializer.Serialize(executionSettings);
                var jsonElement = JsonSerializer.Deserialize<JsonElement>(executionSettingsJson);

                // 检查是否包含enable_thinking属性
                if (jsonElement.ValueKind == JsonValueKind.Object &&
                    jsonElement.TryGetProperty("enable_thinking", out JsonElement thinkingElement) &&
                    thinkingElement.ValueKind == JsonValueKind.Number)
                {
                    int enableThinkingSetting = thinkingElement.GetInt32();
                    bool isTextGenerationService = modelInstance.ServiceTypeEnum == ServiceTypeEnum.TextGeneration;
                    bool isSupportDeepThink = modelInstance.SupportDeepThink > 0;
                    bool isEnableThinkingSetting = enableThinkingSetting > 0;

                    // 只有当设置启用、服务类型为文本生成且模型支持深度思考时才启用
                    return isEnableThinkingSetting && isTextGenerationService && isSupportDeepThink;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("解析enable_thinking设置失败: {0}", ex.Message);
            }

            return false;
        }

        /// <summary>
        /// 验证输出类型一致性
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        private void ValidateOutputTypeConsistency(FlowNode flowNode)
        {
            var promptDto = GetPrompt(flowNode);
            var config = flowNode.PublishEventConfig;

            if (_chatRunDto.IsStream && promptDto != null && config != null)
            {
                bool isPromptVariableOutput = promptDto.OutputType == PromptOutputTypeEnum.Variables;
                bool isConfigJsonOutput = config.ResponseFormatType == ResponseFormatType.JsonObject;

                // 检查不一致性：提示词节点，节点输出类型和提示词输出参数类型不一致
                if ((isPromptVariableOutput && !isConfigJsonOutput) ||
                    (!isPromptVariableOutput && isConfigJsonOutput))
                {
                    _logger.LogError("节点：{0}，输出类型不一致，请更新技能，节点输出类型： {1}，提示词输出类型: {2}",
                        flowNode.Code, config.ResponseFormatType, promptDto.OutputType);
                    throw new LLmCustomException("LLM", OutputTypeInconsistentMessage);
                }
            }
        }

        /// <summary>
        /// 判断是否应该输出JSON对象格式
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <returns>是否应该输出JSON对象</returns>
        private bool ShouldOutputJsonObject(FlowNode flowNode)
        {
            var promptDto = GetPrompt(flowNode);
            var config = flowNode.PublishEventConfig;

            // 内置提示词没有提示词数据，使用config?.ResponseFormatType指定输出类型
            return promptDto?.OutputType == PromptOutputTypeEnum.Variables ||
                   config?.ResponseFormatType == ResponseFormatType.JsonObject;
        }

        /// <summary>
        /// 处理流式响应的核心方法
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="enableThinking">是否启用深度思考</param>
        /// <param name="shouldOutputJsonObject">是否应该输出JSON对象</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果</returns>
        private async Task<string> ProcessStreamingResponse(
            FlowNode flowNode,
            bool enableThinking,
            bool shouldOutputJsonObject,
            CancellationToken cancellationToken)
        {
            // 初始化内容构建器
            var contentBuilder = new StringBuilder();
            var allReasoningContentBuilder = new StringBuilder();
            var usage = new CompletionUsage();
            var config = flowNode.PublishEventConfig;

            // 初始化参数跟踪
            var paramCodes = GetParameterCodes(config);
            var paramValueDic = paramCodes.ToDictionary(x => x, x => string.Empty);
            var invalidContent = string.Empty; // 用于处理无效的JSON内容
            long elapsedMilliseconds = 0;

            _logger.LogInformation("开始接收大模型返回，BatchGUID：{0}，使用JSON解析输出参数：{1}",
                _chatRunDto.BatchGuid, shouldOutputJsonObject);

            // 遍历流式聊天响应
            await foreach (var item in flowNode.StreamingChats)
            {
                // 当第一个响应到达时停止计时
                if (_chatRunDto.Stopwatch.IsRunning)
                {
                    _chatRunDto.Stopwatch.Stop();
                    elapsedMilliseconds = _chatRunDto.Stopwatch.ElapsedMilliseconds;
                }

                // 处理推理内容和常规内容
                var reasoningContent = ReasoningContent(item, enableThinking);
                allReasoningContentBuilder.Append(reasoningContent);
                contentBuilder.Append(item.Content);

                // 格式化使用情况统计
                FormatUsage(item, usage);

                // 如果启用流式输出，则处理流式项目
                if (_chatRunDto.IsStream)
                {
                    await ProcessStreamingItem(
                        flowNode,
                        item,
                        reasoningContent,
                        shouldOutputJsonObject,
                        paramCodes,
                        paramValueDic,
                        invalidContent,
                        contentBuilder);
                }
            }

            // 完成并返回结果
            return await FinalizeStreamingResult(
                flowNode,
                contentBuilder,
                allReasoningContentBuilder,
                usage,
                config,
                elapsedMilliseconds);
        }

        /// <summary>
        /// 获取参数代码列表
        /// </summary>
        /// <param name="config">发布事件配置</param>
        /// <returns>参数代码列表</returns>
        private List<string> GetParameterCodes(PublishEventConfig? config)
        {
            // 如果配置中有参数代码则使用配置的，否则使用默认的"output"
            return config?.ParamCodes != null && config.ParamCodes.Count != 0
                ? config.ParamCodes.Select(x => x).ToList()
                : new List<string> { DefaultOutputParamCode };
        }

        /// <summary>
        /// 处理单个流式项目
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="item">流式聊天消息内容</param>
        /// <param name="reasoningContent">推理内容</param>
        /// <param name="shouldOutputJsonObject">是否应该输出JSON对象</param>
        /// <param name="paramCodes">参数代码列表</param>
        /// <param name="paramValueDic">参数值字典</param>
        /// <param name="invalidContent">无效内容</param>
        /// <param name="contentBuilder">内容构建器</param>
        private async Task ProcessStreamingItem(
            FlowNode flowNode,
            StreamingChatMessageContent item,
            string reasoningContent,
            bool shouldOutputJsonObject,
            List<string> paramCodes,
            Dictionary<string, string> paramValueDic,
            string invalidContent,
            StringBuilder contentBuilder)
        {
            // 处理推理内容
            if (!string.IsNullOrEmpty(reasoningContent))
            {
                await ReasoningEvent(reasoningContent, _chatRunDto.IsStream);
            }

            // 跳过空内容
            if (string.IsNullOrEmpty(item.Content))
                return;

            // 根据输出格式选择不同的处理方式
            if (shouldOutputJsonObject)
            {
                await ProcessJsonObjectOutput(flowNode, item, paramValueDic, invalidContent, contentBuilder);
            }
            else
            {
                await ProcessTextOutput(flowNode, item, paramCodes);
            }
        }

        /// <summary>
        /// 处理JSON对象输出
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="item">流式聊天消息内容</param>
        /// <param name="paramValueDic">参数值字典</param>
        /// <param name="invalidContent">无效内容</param>
        /// <param name="contentBuilder">内容构建器</param>
        private async Task ProcessJsonObjectOutput(
            FlowNode flowNode,
            StreamingChatMessageContent item,
            Dictionary<string, string> paramValueDic,
            string invalidContent,
            StringBuilder contentBuilder)
        {
            // 处理无效的JSON内容（带尾随点的整数）
            if (JsonValidateHelper.IsIntegerWithTrailingDot(item.Content))
            {
                invalidContent = item.Content;
                return;
            }

            // 如果需要，将无效内容与当前内容合并
            if (!string.IsNullOrEmpty(invalidContent))
            {
                item.Content = invalidContent + item.Content;
                invalidContent = "";
            }

            // 发布文本事件
            await _chatRunDto.EventBus.PublishAsync(new TextEvent(flowNode.Code, item.Content));

            // 处理JSON属性
            var json = JsonFixer.FixJson(contentBuilder.ToString());
            try
            {
                if (string.IsNullOrWhiteSpace(json))
                    return;

                // 获取JSON的最后属性并发布
                var (perProperty, lastProperty) = JsonFixer.GetLastJsonProperty(json);
                await PublishProperty(flowNode, paramValueDic, perProperty);
                await PublishProperty(flowNode, paramValueDic, lastProperty);
            }
            catch (Exception ex)
            {
                _logger.LogDebug("节点：{0}，内容: {1}，错误信息: {2}", flowNode.Code, json, ex.Message);
            }
        }

        /// <summary>
        /// 处理文本输出
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="item">流式聊天消息内容</param>
        /// <param name="paramCodes">参数代码列表</param>
        private async Task ProcessTextOutput(FlowNode flowNode, StreamingChatMessageContent item, List<string> paramCodes)
        {
            // 发布文本事件
            await _chatRunDto.EventBus.PublishAsync(new TextEvent(flowNode.Code, item.Content));
            // 发布字段事件
            await _chatRunDto.EventBus.PublishAsync(new FieldEvent(
                flowNode.Code,
                string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, paramCodes.First()),
                item.Content));
        }

        /// <summary>
        /// 完成流式结果处理
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="contentBuilder">内容构建器</param>
        /// <param name="allReasoningContentBuilder">所有推理内容构建器</param>
        /// <param name="usage">使用情况统计</param>
        /// <param name="config">发布事件配置</param>
        /// <param name="elapsedMilliseconds">耗时毫秒数</param>
        /// <returns>最终内容</returns>
        private async Task<string> FinalizeStreamingResult(
            FlowNode flowNode,
            StringBuilder contentBuilder,
            StringBuilder allReasoningContentBuilder,
            CompletionUsage usage,
            PublishEventConfig? config,
            long elapsedMilliseconds)
        {
            var content = contentBuilder.ToString();
            var allReasoningContent = allReasoningContentBuilder.ToString();

            _logger.LogInformation("大模型输出完成，BatchGUID：{0}，推理过程: {1}, 原始内容：{2}",
                _chatRunDto.BatchGuid, allReasoningContent, content);

            // 如果配置了发布最终结果事件，则发布
            if (config != null && config.PublishLlmResultEvent)
            {
                await _chatRunDto.EventBus.PublishAsync(new LlmResultEvent(flowNode.Code, content, usage));
            }

            // 停止计时并添加日志
            flowNode.Stopwatch.Stop();
            AddPromptNodeLog(flowNode, usage.UsagePromptTokens, elapsedMilliseconds, usage.UsageCompletionTokens, allReasoningContent);

            return content;
        }

        /// <summary>
        /// 获取聊天完成服务
        /// </summary>
        /// <param name="flowNode">流程节点</param>
        /// <param name="serviceId">服务ID</param>
        /// <returns>聊天完成服务</returns>
        private Task<IChatCompletionService> GetChatCompletionService(FlowNode flowNode, string? serviceId)
        {
            if (string.IsNullOrWhiteSpace(serviceId))
            {
                _logger.LogInformation("节点：{0}, 类型：{1}, 使用默认模型", flowNode.Name, flowNode.Type);
                return Task.FromResult(_kernel.GetRequiredService<IChatCompletionService>());
            }
            _logger.LogInformation("节点：{0}, 类型：{1}, 使用模型： {2}", flowNode.Name, flowNode.Type, serviceId);
            return Task.FromResult(_kernel.GetRequiredService<IChatCompletionService>(serviceId));
        }

        /// <summary>
        /// 获取模型实例
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <param name="defModelInstance">默认模型实例</param>
        /// <returns>模型实例</returns>
        private async Task<ModelInstanceDto> GetModelInstance(string serviceId, ModelInstanceDto defModelInstance)
        {
            if (string.IsNullOrWhiteSpace(serviceId))
            {
                return defModelInstance;
            }
            // 从数据库查询模型实例
            var modelInstance = await _modelInstanceRepostory.GetFirstAsync(x => x.InstanceCode == serviceId);
            if (modelInstance == null)
            {
                return defModelInstance;
            }
            return _mapper.Map<ModelInstanceDto>(modelInstance);
        }

        /// <summary>
        /// 格式化使用情况统计
        /// </summary>
        /// <param name="item">流式聊天消息内容</param>
        /// <param name="usage">使用情况统计对象</param>
        private void FormatUsage(StreamingChatMessageContent item, CompletionUsage usage)
        {
            // 从元数据中提取完成令牌数
            if (item.Metadata != null && item.Metadata.TryGetValue(nameof(CompletionUsage.UsageCompletionTokens), out var completionTokensMetadata) && int.TryParse(completionTokensMetadata?.ToString(), out int completionTokens))
            {
                usage.UsageCompletionTokens = completionTokens;
            }
            // 从元数据中提取提示令牌数
            if (item.Metadata != null && item.Metadata.TryGetValue(nameof(CompletionUsage.UsagePromptTokens), out var promptTokensMetadata) && int.TryParse(promptTokensMetadata?.ToString(), out int promptTokens))
            {
                usage.UsagePromptTokens = promptTokens;
            }

            // 处理聊天令牌使用情况
            if (item.Metadata != null && item.Metadata.TryGetValue(nameof(LlmResultEvent.Usage), out var usageMetadata) && usageMetadata is ChatTokenUsage chatTokenUsage)
            {
                usage.UsageCompletionTokens = chatTokenUsage.OutputTokenCount;
                usage.UsagePromptTokens = chatTokenUsage.InputTokenCount;
            }
        }

        private string ReasoningContent(StreamingChatMessageContent item, bool enableThinking)
        {
            if (item.InnerContent == null)
                return "";

            var modelId = item.ModelId;
            JsonNode? jsonContent = null;

            if (enableThinking && modelId.StartsWith("qwen", StringComparison.OrdinalIgnoreCase))
            {
                // 如果 InnerContent 是具体类型（如 QwenResponse），应直接访问属性而非序列化解析
                try
                {
                    jsonContent = JsonNode.Parse(JsonSerializer.Serialize(item.InnerContent, new JsonSerializerOptions { WriteIndented = true }));
                }
                catch (Exception ex)
                {
                    _logger.LogError("json covert deepseek reasoning content error:{0}, {1}：\n{2}", ErrMsgConst.JsonFormat_ErrMsg, ex.Message, item.InnerContent);
                    return "";
                }
            }
            else if (modelId.StartsWith("deepseek-r1", StringComparison.OrdinalIgnoreCase) ||
                     modelId.StartsWith("deepseek-v3", StringComparison.OrdinalIgnoreCase))
            {
                try
                {
                    jsonContent = JsonNode.Parse(ModelReaderWriter.Write(item.InnerContent));
                }
                catch (Exception ex)
                {
                    _logger.LogError("json covert deepseek reasoning content error:{0}, {1}：\n{2}", ErrMsgConst.JsonFormat_ErrMsg, ex.Message, item.InnerContent);
                    return "";
                }
            }
            else
            {
                return "";
            }
            
            if (jsonContent is null)
                return "";
            
            // 提取 reasoning_content 安全地
            var choices = jsonContent["choices"];
            if (!(choices is JsonArray array) || array.Count == 0)
            {
                return string.Empty;
            }
                
            var firstChoice = array[0];
            if (modelId.StartsWith("qwen", StringComparison.OrdinalIgnoreCase))
            {
                var message = firstChoice["message"];
                if (message is null)
                    return "";
                
                var reasoningContent = message["reasoning_content"];
                if (reasoningContent is null || string.IsNullOrEmpty(reasoningContent.ToString()))
                    return "";
            
                var content = reasoningContent.ToString();
                _logger.LogInformation("reasoning_content: {0}", content);
                return content;
            }
            else if (modelId.StartsWith("deepseek", StringComparison.OrdinalIgnoreCase))
            {
                var delta = firstChoice["delta"];
                if (delta is null)
                    return "";
                var reasoningContent = delta["reasoning_content"];
                if (reasoningContent is null || string.IsNullOrEmpty(reasoningContent.ToString()))
                    return "";
            
                var content = reasoningContent.ToString();
                _logger.LogInformation("reasoning_content: {0}", content);
                return content;
            }

            return "";
        }

        private async Task PublishProperty(FlowNode flowNode, Dictionary<string, string> paramValueDic, IJsonFixerProperty property)
        {
            if (property == null) { await Task.CompletedTask; return; }

            if (paramValueDic.ContainsKey(property.Name) && string.Equals(property.Value, paramValueDic[property.Name]) == false)
            {
                var paramValue = paramValueDic[property.Name].RemoveStartAndEnd('"');
                var lastPropertyValue = property.Value.RemoveStartAndEnd('"');

                var paramContent = lastPropertyValue.StartsWith(paramValue) ?
                    lastPropertyValue.Substring(paramValue.Length) :
                    property.Value.RemoveStartAndEnd('"');

                if (string.Equals("null", paramContent))
                    return;

                paramValueDic[property.Name] = property.Value;
                await _chatRunDto.EventBus.PublishAsync(new FieldEvent(flowNode.Code, string.Format(KernelArgumentsConstant.NodeOutput, flowNode.Code, property.Name), paramContent, property));
            }
        }
        public void TraverseParamDtos(List<ParamDto> paramDtos, ref string str, string code = "")
        {
            str += $" {code}" + "{ ";
            foreach (var paramDto in paramDtos)
            {
                if (!String.Equals(paramDto.Type, "array<object>", StringComparison.OrdinalIgnoreCase))
                {
                    str += $"\"{paramDto.Code}\": {paramDto.Type} //{paramDto.Name} {paramDto.Description} ";
                }
                else
                {
                    str += $"\"{paramDto.Code}\": List[{paramDto.Code}] //{paramDto.Name} {paramDto.Description} ";
                }
            }
            str += "}";
            foreach (var paramDto in paramDtos)
            {
                // 如果 Schema 不为空且包含元素，则递归调用此方法
                if (paramDto.Schema != null && paramDto.Schema.Count > 0)
                {
                    TraverseParamDtos(paramDto.Schema, ref str, paramDto.Code + ":");
                }
            }
        }

        public async Task AddFirstOutput(FlowNode flowNode, object result)
        {
            var outputs = flowNode.Config.Outputs;
            if (outputs == null || outputs.Count == 0) return;

            if (result is string resultString)
            {
                outputs.First().LiteralValue = resultString;
                outputs.First().Value = null;
            }

            await _chatRunDto.AddNodeOutputArgument2(outputs.First().Code, outputs.First().LiteralValue, flowNode.Code);

            await Task.CompletedTask;
        }

        public Dictionary<string, object> JsonCompile(string jsons)
        {
            jsons = JsonValidateHelper.CleanUpJson(jsons);
            _logger.LogInformation("大模型返回：\n{0}", jsons);
            if (string.IsNullOrWhiteSpace(jsons))
            {
                return new Dictionary<string, object>();
            }

            try
            {
                var dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsons);
                return dictionary ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                _logger.LogError("{0}, {1}：\n{2}", ErrMsgConst.JsonFormat_ErrMsg, ex.Message, jsons);
            }
            
            return new Dictionary<string, object>();
        }

        #endregion

        #region log
        public async Task AddMessage(string role, string content, bool? isHidden = false)
        {
            var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;

            var index = _chatRunDto.ChatMessages.Count;
            var dto = new ChatMessageDto
            {
                ChatGUID = _chatRunDto.Chat?.ChatGUID ?? Guid.Empty,
                NodeGUID = _chatRunDto.Chat?.CurrentNodeGUID ?? Guid.Empty,
                Role = role,
                Content = content,
                Index = index,
                IsHidden = isHidden ?? false ? 1 : 0,
            };

            _chatRunDto.ChatMessages.Add(dto);

            await Task.CompletedTask;
        }
        private void ResetNodeStartTime(FlowNode flowNode)
        {
            flowNode.Stopwatch.Restart();
            var nodeLog = GetNodeLogDto(flowNode.Id);
            if (nodeLog == null) return;

            nodeLog.StartTime = TimeZoneUtility.LocalNow();
        }
        public void AddNodeLog(FlowNode flowNode)
        {
            var nodeLog = new ChatMessageNodeLogDto
            {
                ChatMessageNodeLogGUID = Guid.NewGuid(),
                ChatGUID = _chatRunDto.Chat.ChatGUID,
                NodeGUID = Guid.Parse(flowNode.Id),
                Name = flowNode.Name,
                Description = flowNode.Description,
                Index = _chatRunDto.NodeLogs.Count,
                StartTime = TimeZoneUtility.LocalNow(),
                Config = JsonConvert.SerializeObject(flowNode.Config),
            };
            _chatRunDto.NodeLogs.Add(nodeLog);
        }
        public void AddSucceedNodeLog(FlowNode flowNode, string? inputs = null, string? outputs = null)
        {
            var nodeLog = GetNodeLogDto(flowNode.Id);
            if (nodeLog == null) return;

            nodeLog.EndTime = TimeZoneUtility.LocalNow();
            nodeLog.Inputs = inputs ?? ParamFormatJson(flowNode._refParams);
            nodeLog.Outputs = outputs ?? ParamFormatJson(flowNode._outParams);

            nodeLog.SetDuration(flowNode.Stopwatch.ElapsedMilliseconds);
            _logger.LogInformation("节点：{0}, 类型：{1}, 耗时: {2} ms", flowNode.Name, flowNode.Type, nodeLog.Duration);
        }
        public void AddPromptNodeLog(FlowNode flowNode, int promptTokens, long elapsedMilliseconds, int completeTokens, string reasoningContent)
        {
            var nodeLog = _chatRunDto.NodeLogs.FirstOrDefault(x => x.NodeGUID == Guid.Parse(flowNode.Id));
            if (nodeLog == null) return;
            nodeLog.PromptTokens = promptTokens;
            nodeLog.CompleteTokens = completeTokens;
            nodeLog.ThinkOutputs = reasoningContent;
            nodeLog.SetDuration(flowNode.Stopwatch.ElapsedMilliseconds);
            nodeLog.SetFirstRespDuration(elapsedMilliseconds);
        }
        private string ParamFormatJson(List<ParamDto> paramDtos)
        {
            var result = new ExpandoObject();
            foreach (var arg in paramDtos)
            {
                _ = result.TryAdd(arg.Code ?? arg.LiteralCode, arg.Type == FieldTypeConstant.ArrayField ? JsonConvert.DeserializeObject<Object>(arg.LiteralValue) : arg.LiteralValue);
            }
            return JsonConvert.SerializeObject(result);
        }
        protected ChatMessageNodeLogDto GetNodeLogDto(string nodeGuid)
        {
            return _chatRunDto.NodeLogs.FirstOrDefault(x => x.NodeGUID == Guid.Parse(nodeGuid) && x.BatchGUID.ToString() == "00000000-0000-0000-0000-000000000000");
        }
        #endregion

        #region SSE Event
        public async Task ReasoningEvent(string content, bool isStream)
        {
            if (isStream == false || string.IsNullOrWhiteSpace(content)) return;

            content = content.Replace("\n", "|n");

            _logger.LogInformation("----PluginBase.TextEvent,content:{0}", content);

            await SafeWriteToResponseAsync(async () =>
            {
                var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ReasoningEvent, content));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
                await Task.Delay(MysoftConstant.ChatDelay);
            });
            // await AddMessage(AuthorRole.Tool.Label, content);
        }

        public async Task TextEvent(string content, bool isStream)
        {
            if (isStream == false || string.IsNullOrWhiteSpace(content)) return;

            content = content.Replace("\n", "|n");

            //_logger.LogInformation("----PluginBase.TextEvent:isStream:{0},content:{1}", isStream, content);

            await SafeWriteToResponseAsync(async () =>
            {
                var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.TextEvent, content));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
                await Task.Delay(MysoftConstant.ChatDelay);
            });
            // await AddMessage(AuthorRole.Tool.Label, content);
        }

        public async Task ErrorEvent(string message, bool isStream)
        {
            if (isStream == false || string.IsNullOrWhiteSpace(message)) return;

            _logger.LogInformation("----PluginBase.ErrorEvent:isStream:{0},message:{1}", isStream, message);

            await SafeWriteToResponseAsync(async () =>
            {
                var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ErrorEvent, message));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
                await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            });

            await AddMessage(AuthorRole.Tool.Label, message);
        }
        public async Task ReplaceEvent(object content, bool isStream)
        {
            if (isStream == false || content == null) return;

            var setting = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
            var data = JsonConvert.SerializeObject(content, setting);

            _logger.LogInformation("----PluginBase.ReplaceEvent:isStream:{0},content:{1}", isStream, content);

            await SafeWriteToResponseAsync(async () =>
            {
                var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ReplaceEvent, data));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
                await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            });

            await AddMessage(AuthorRole.Assistant.Label, string.Format(EventDataConstant.ReplaceEvent, data));
        }

        public async Task DataEvent(object content, bool isStream)
        {
            if (isStream == false || content == null) return;

            var setting = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
            var data = JsonConvert.SerializeObject(content, setting);

            _logger.LogInformation("----PluginBase.DataEvent:isStream:{0},content:{1}", isStream, data);

            await SafeWriteToResponseAsync(async () =>
            {
                var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.DataEvent, data));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
                await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            });
            //await AddMessage(AuthorRole.Assistant.Label, string.Format(EventDataConstant.DataEvent, data));
        }

        public async Task StreamEvent(string content)
        {
            if (_chatRunDto.IsStream == false || string.IsNullOrEmpty(content)) return;

            content = content.Replace("\n", "|n");
            // Console.WriteLine("----StreamEvent::{0}", content);

            await SafeWriteToResponseAsync(async () =>
            {
                var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.StreamEvent, content));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
                await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            });
        }

        public async Task DoneEvent(bool isStream)
        {
            if (isStream == false) return;

            _logger.LogInformation("----PluginBase.DoneEvent:isStream:{0}", isStream);

            await SafeWriteToResponseAsync(async () =>
            {
                var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.DoneEvent));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
                await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            });
        }

        public async Task ContentReviewEvent(string contentReviewResult)
        {
            _logger.LogInformation("----ContentReviewFactory.ContentReviewEvent:ContentReviewEvent:{0}", contentReviewResult);

            await SafeWriteToResponseAsync(async () =>
            {
                var bytes = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ContentReviewEvent, contentReviewResult));
                await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bytes);
                await Task.Delay(MysoftConstant.ChatDelay);
            });
        }

        /// <summary>
        /// 安全地写入响应流，避免在响应已开始后出现异常
        /// </summary>
        /// <param name="writeAction">写入操作</param>
        private async Task SafeWriteToResponseAsync(Func<Task> writeAction)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext?.Response != null && !httpContext.Response.HasStarted)
                {
                    await writeAction();
                }
                else if (httpContext?.Response?.HasStarted == true)
                {
                    // 响应已开始，直接执行写入操作（可能会失败，但不会抛出致命异常）
                    await writeAction();
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Headers are read-only"))
            {
                // 忽略响应头只读异常，这是正常的流式响应行为
                _logger.LogDebug("响应头已锁定，忽略写入操作: {0}", ex.Message);
            }
            catch (Exception ex)
            {
                // 记录其他异常但不抛出，避免中断流式响应
                _logger.LogWarning(ex, "写入响应流时发生异常");
            }
        }

        #endregion

        #region handler
        public async Task HandleAsync(LlmResultEvent @event, string code)
        {
            if (@event.FlowCode != code) return;
            await LlmResultHandleAsync(@event);
        }
        #endregion
        
        protected PromptDto? GetPrompt(FlowNode flowNode)
        {
            var promptGuid = flowNode.Config.TemplateId;
            if (promptGuid == null)
            {
                return null;
            }

            if (Guid.TryParse(promptGuid, out var guid))
            {
                return _chatRunDto.SkillOrchestration.Prompts?.FirstOrDefault(x => x.Id == guid);
            }
            
            _logger.LogWarning("解析提示词GUID失败：{0}", promptGuid);

            return null;
        }
    }

    public class None { }

    public interface TActivity
    {

    }
}

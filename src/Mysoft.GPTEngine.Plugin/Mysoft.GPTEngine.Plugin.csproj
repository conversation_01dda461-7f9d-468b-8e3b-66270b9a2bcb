<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <NoWarn>$(NoWarn);SKEXP0001,SK<PERSON>P0040,SKEXP0060</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\DocumentAnalysisPlugin.FixedGenerate.txt" />
    <None Remove="Resources\ParameterPlugin.FixedGenerate.txt" />
    <None Remove="Resources\PromptPlugin.Estimate.txt" />
    <None Remove="Resources\QuestionGeneratePlugin.FixedGenerate.txt" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Handlebars.Net" Version="2.1.6" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.ML.Tokenizers" Version="0.22.0-preview.24271.1" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Milvus" Version="1.60.0-alpha" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.OpenApi" Version="1.60.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="106.15.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\DocumentAnalysisPlugin.FixedGenerate.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\KnowledgePlugin.QuestionOptimization.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\PromptPlugin.Estimate.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ParameterPlugin.FixedGenerate.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ParameterPlugin.ParamMapping.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\QuestionGeneratePlugin.FixedGenerate.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\SelectorPlugin.Classification.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\DataQuery.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mysoft.GPTEngine.Application.Contracts\Mysoft.GPTEngine.Application.Contracts.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Domain.Shared\Mysoft.GPTEngine.Domain.Shared.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Domain\Mysoft.GPTEngine.Domain.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.SemanticKernel.Core\Mysoft.GPTEngine.SemanticKernel.Core.csproj" />
  </ItemGroup>

</Project>

using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Parsers;

namespace Mysoft.GPTEngine.Plugin
{
    public class PromptPlugin : PluginBase
    {
        private readonly IJsonOutputParsers _jsonOutputParsers;
        public PromptPlugin(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper, IJsonOutputParsers jsonOutputParsers, IMysoftContextFactory mysoftContextFactory) : base(kernel, memoryBuilder, httpContextAccessor, mapper, mysoftContextFactory)
        {
            _jsonOutputParsers = jsonOutputParsers;
        }

        [KernelFunction]
        public async Task<string> TestScene(PromptDto prompt, CancellationToken cancellationToken)
        {
            Verify.NotNull(prompt);
            var arguments = new KernelArguments();
            foreach (var param in prompt.InputParam)
            {
                arguments.Add(param.LiteralCode, param.LiteralValue);
            }
            // var promptTemplate = await TemplateRenderAsync(promptTemplateText: prompt.PromptTemplate, arguments: arguments);
            // prompt.PromptTemplate = promptTemplate.Content;
            var chatHistory = await CreateChatHistory(prompt, arguments);
            // chatHistory.AddUserMessage(promptTemplate.Content);

            var serviceId = string.IsNullOrWhiteSpace(prompt.ModelInstanceCode) ? ChatCompletionTypeDto.TextGeneration : prompt.ModelInstanceCode;
            return await ChatCompletion(chatHistory: chatHistory, textEvent: true, serviceId: serviceId, executionSetting: prompt.ExecutionSetting, cancellationToken: cancellationToken);
        }
        [KernelFunction]
        public async Task<EstimateDto> Estimate(string expectContent, string outputContent, string promptTemplate, string? serviceId = null, string? executionSetting = null, CancellationToken cancellationToken = default)
        {
            var arguments = new KernelArguments { { nameof(expectContent), expectContent }, { nameof(outputContent), outputContent }, { nameof(promptTemplate), promptTemplate } };
            serviceId = string.IsNullOrWhiteSpace(serviceId) ? ChatCompletionTypeDto.TextGeneration : serviceId;
            var result = await ChatCompletion(systemPromptName: EmbeddedResource.PromptPlugin_Estimate, input: "请根严格跟据提示词进行测评", arguments: arguments,
                serviceId: serviceId,
                executionSetting: executionSetting,
                cancellationToken: cancellationToken);
            if (JsonUtility.TryDeserializeObject<EstimateDto>(result, out var output))
            {
                await DataEvent(new { Type = "score", Data = new { Value = output.CombinedScore, Max = 5, Text = output.Comments } }, true);
                return await Task.FromResult(output);
            }
            return new EstimateDto();
        }
    }
}

using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.Plugin.Http
{
    public class HttpPluginBase
    {
        private readonly IHttpClientFactory _httpClientFactory;
        /// <summary>
        /// Initializes a new instance of the <see cref="HttpPlugin"/> class.
        /// </summary>
        public HttpPluginBase(IHttpClientFactory clientFactory)
        {
            _httpClientFactory = clientFactory;
        }
        /// <summary>Sends an HTTP request and returns the response content as a string.</summary>
        /// <param name="uri">The URI of the request.</param>
        /// <param name="method">The HTTP method for the request.</param>
        /// <param name="requestContent">Optional request content.</param>
        /// <param name="cancellationToken">The token to use to request cancellation.</param>
        public async Task<string> SendRequestAsync(string uri, HttpMethod method, HttpContent? requestContent, HttpClient? client, CancellationToken cancellationToken)
        {
            client = client ?? _httpClientFactory.CreateClient();

            using var request = new HttpRequestMessage(method, uri) { Content = requestContent };
            using var response = await client.SendWithSuccessCheckAsync(request, cancellationToken).ConfigureAwait(false);
            return await response.Content.ReadAsStringWithExceptionMappingAsync().ConfigureAwait(false);
        }
    }
}

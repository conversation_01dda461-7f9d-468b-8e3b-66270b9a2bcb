using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Mysoft.GPTEngine.Plugin.ContentReview
{
    public class ContentReviewFactory
    {
        
        private static readonly string DefaultSource = "baidu";

        protected static Dictionary<string, string> AllConfigMap = new Dictionary<string, string>();

        public static List<string> AllConfigKeyList = ContentReviewInvokeFactory.AllConfigKeyList;

        const string AssistantOpenCheckKey = "Assistant_Open_Check_Key";

        private readonly ILogger<ContentReviewFactory> _logger;

        private readonly IHttpContextAccessor _httpContextAccessor;

        private readonly IMysoftContextFactory _mysoftContextFactory;

        private readonly ChatMessageCheckRepostory _chatMessageCheckRepostory;

        private readonly MyParamValueRepostory _myParamValueRepostory;
        
        private readonly AssistantRepostory _assistantRepostory;
        
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        
        private readonly ModelRepostory _modelRepostory;

        private readonly MysoftMemoryCache _mysoftMemoryCache;

        private readonly IMysoftApiService _mysoftApiService;
        
        private readonly IServiceProvider _serviceProvider;
        
        public Kernel _kernel;
        
        private readonly ISqlSugarProviderFactory _sqlSugarProviderFactory;

        public ContentReviewFactory(IHttpContextAccessor httpContextAccessor, IMysoftContextFactory mysoftContextFactory, ILogger<ContentReviewFactory> logger,
            ChatMessageCheckRepostory chatMessageCheckRepostory, MyParamValueRepostory myParamValueRepostory,
            MysoftMemoryCache mysoftMemoryCache, AssistantRepostory assistantRepostory, IMysoftApiService mysoftApiService, IServiceProvider serviceProvider,
            ModelInstanceRepostory modelInstanceRepostory, ModelRepostory modelRepostory, ISqlSugarProviderFactory sqlSugarProviderFactory)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(IServiceProvider));
            _httpContextAccessor = httpContextAccessor;
            _mysoftContextFactory = mysoftContextFactory;
            _chatMessageCheckRepostory = chatMessageCheckRepostory;
            _myParamValueRepostory = myParamValueRepostory;
            _logger = logger;
            _mysoftMemoryCache = mysoftMemoryCache;
            _assistantRepostory = assistantRepostory;
            _mysoftApiService = mysoftApiService;
            _modelRepostory = modelRepostory;
            _modelInstanceRepostory = modelInstanceRepostory;
            _sqlSugarProviderFactory = sqlSugarProviderFactory;
            _kernel = _serviceProvider.GetService<Kernel>() ?? throw new ArgumentNullException(nameof(Kernel));

        }
        
       

        public async Task<Dictionary<string, string>> GetConfigMap(CheckSourceEnum checkSourceEnum)
        {
            var configMap = _mysoftMemoryCache.GetMyParamValuePrefixCache(checkSourceEnum.ToString());
            if (configMap != null) return await Task.FromResult(configMap);
            
            var apiName = "/api/42000101/home/<USER>/"+checkSourceEnum;
            var result = await _mysoftApiService.PostAsync(_mysoftContextFactory.GetMysoftContext().GptBuilderUrl + apiName, string.Empty);
            
            var paramsResult = JsonConvert.DeserializeObject<MysoftApiResultDto>(result);

            if (paramsResult != null && paramsResult.Success)
            {
                var data = paramsResult.Data;
                if (data is JObject)
                {
                    configMap = ((JObject)data).ToObject<Dictionary<string, string>>();
                    _mysoftMemoryCache.SetMyParamValuePrefixCache(checkSourceEnum.ToString(), configMap);
                    
                    return await Task.FromResult(configMap);
                }
            }
            
            return await Task.FromResult(new Dictionary<string, string>());
            
        }


        public async Task CheckInput(ChatInputDto chatInput, CancellationToken cancellationToken, bool isStream,
            CheckSourceEnum checkSourceEnum = CheckSourceEnum.BaiDu)
        {
            if (string.IsNullOrEmpty(chatInput.Input) || (!await CheckOpenCheck(chatInput.AssistanGUID).ConfigureAwait(false) && !(chatInput.CheckContent??false))) return;

            await Check(chatInput.Input, checkSourceEnum, cancellationToken,isStream, async (result) =>
            {
                var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;
                var entity = new ChatMessageCheckEntity();
                entity.SkillGUID = chatInput.SkillGUID;
                entity.Input = chatInput.Input;
                entity.CheckSource = checkSourceEnum;
                entity.CheckResult = result;
                entity.MessageSource = CheckMessageSourceEnum.ChatInput;
                entity.ChatMessageCheckGUID = Guid.NewGuid();
                entity.ChartGUID = chatInput.ChatGUID;
                await _chatMessageCheckRepostory.InsertAsync(entity);
            }).ConfigureAwait(false);
        }

        public async Task CheckOutput(ChatRunDto chatRunDto, string result, PromptDto prompt, CancellationToken cancellationToken = default,
            CheckSourceEnum checkSourceEnum = CheckSourceEnum.BaiDu)
        {
            if (!await CheckOpenCheck(chatRunDto.Chat.AssistantGUID)  && !chatRunDto.CheckContent) return;

            await Check(result, checkSourceEnum, cancellationToken,chatRunDto.IsStream, async (checkResult) =>
            {
                var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;
                var entity = new ChatMessageCheckEntity();
                entity.SkillGUID = chatRunDto.Chat.SkillGUID;
                entity.Input = chatRunDto.ChatArguments[KernelArgumentsConstant.Input]?.ToString();
                entity.ChartGUID = chatRunDto.Chat.ChatGUID;
                entity.ModelInstanceGUID = (chatRunDto.ModelInstance?.InstanceGUID ?? prompt.ModelInstanceGUID).GetValueOrDefault();
                entity.Result = result;
                entity.CheckSource = checkSourceEnum;
                entity.CheckResult = checkResult;
                entity.MessageSource = CheckMessageSourceEnum.ChatOutput;
                entity.ChatMessageCheckGUID = Guid.NewGuid();
                await _chatMessageCheckRepostory.InsertAsync(entity);

            });
        }

        public async Task Check(string content, CheckSourceEnum checkSourceEnum, CancellationToken cancellationToken, bool isStream, Action<string> func)
        {

            var contentReviewResult = ContentReviewResult.Success();
            try
            {
                //获取默认的安全检查策略
                var contentReviewModelInstancesAsync = await GetContentReviewModelInstancesAsync();
                if (contentReviewModelInstancesAsync.Count == 0) return;
                var ocrRecognizeService = _kernel.GetRequiredService<IContentReview>(contentReviewModelInstancesAsync[0].InstanceCode);
                contentReviewResult = ocrRecognizeService.Check(content);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "内容审查异常：{0}", content);
                contentReviewResult = ContentReviewResult.Failed(e.Message);
            }

            if (!contentReviewResult.IsSuccess)
            {
                func(contentReviewResult.Message);
                if (isStream)
                {
                    await ContentReviewEvent(contentReviewResult.Message);
                    cancellationToken.ThrowIfCancellationRequested();
                }
                else
                {
                    throw new Exception("内容审查不通过：" + contentReviewResult.Message);
                }
                
            }
        }

        private async Task ContentReviewEvent(string contentReviewResult)
        {
            Console.WriteLine("----ContentReviewFactory.ContentReviewEvent:ContentReviewEvent:{0}",contentReviewResult);
            
            var bytes = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ContentReviewEvent, contentReviewResult));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bytes);
            _httpContextAccessor.HttpContext.Response.Body.Close();
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
        }

        private async Task<bool> CheckOpenCheck(Guid assistanGUID)
        {
            var key = AssistantOpenCheckKey + "_" + assistanGUID;

            if (_httpContextAccessor.HttpContext.Items.ContainsKey(key))
            {
                var isCheck = _httpContextAccessor.GetItem<int>(key);
                return await Task.FromResult(isCheck == 1);
            }
            var assistantEntity = await _assistantRepostory.GetAsync(x => x.AssistantGUID == assistanGUID).ConfigureAwait(false);
            var openCheck = assistantEntity?.OpenCheck ?? 0;
            _httpContextAccessor.AddItem(key, openCheck);
            return await Task.FromResult(openCheck > 0);
        }
        
        public async Task<List<ModelInstanceDto>> GetContentReviewModelInstancesAsync()
        {
            var _sqlSugarProvider = (await _sqlSugarProviderFactory.GetSqlSugarProvider()).CopyNew();

            return await Task.FromResult(_sqlSugarProvider.Queryable<ModelInstanceEntity>()
                .InnerJoin<ModelEntity>((instance, model) => instance.ModelGUID == model.ModelGUID)
                .Where((instance, model) => !string.IsNullOrEmpty(instance.ApiKey) && model.ServiceTypeEnum == ServiceTypeEnum.ContentReview && instance.IsDefault && instance.IsAvailable)  // 添加 ServiceType 等于 5 的条件
                .Select((instance, model) => new ModelInstanceDto
                {
                    ModelGUID = model.ModelGUID,
                    ModelCode = model.ModelCode,
                    InstanceGUID = instance.InstanceGUID,
                    InstanceCode = instance.InstanceCode,
                    InstanceName = instance.InstanceName,
                    ModelType = model.ModelType,
                    Endpoint = instance.Endpoint,
                    ApiKey = instance.ApiKey,
                    ClientID = instance.ClientID,
                    DeploymentName = instance.DeploymentName,
                    IsDefault = instance.IsDefault,
                    ServiceTypeEnum = model.ServiceTypeEnum,
                    Vendor = instance.Vendor,
                    StrategyId = instance.StrategyId
                })
                .ToList());
        }

    }

    public static class ContentReviewInvokeFactory
    {
        private static readonly Dictionary<string, IContentReview> ContentReviewMap = new Dictionary<string, IContentReview>();
        
        public static List<string> AllConfigKeyList = new List<string>();
        static  ContentReviewInvokeFactory()
        {
            InitAllInstance();
        }
        
        private static void InitAllInstance()
        {
            // 获取当前程序集
            var assembly = Assembly.GetExecutingAssembly();

            // 获取所有实现了IContentReview的类型
            var types = assembly.GetTypes()
                .Where(t => !t.IsInterface && t.GetInterfaces().Any(i => i == typeof(IContentReview)));

            foreach (var type in types)
            {
                // 实例化对象
                var instance = (IContentReview)Activator.CreateInstance(type);

                // 获取Source方法的返回值，作为字典的键
                var key = instance.Source();

                // 将实例添加到字典中
                ContentReviewMap.Add(key, instance);
                InitAllConfigKey(instance);
            }

           
        }

        public static IContentReview GetInterfacesInstance(String key)
        {
            return ContentReviewMap[key];
        }
        
        private static void InitAllConfigKey(IContentReview instance)
        {
            List<string> current = instance.GetAllConfigKey();
            foreach (var item in current)
            {
                if (!AllConfigKeyList.Contains(item))
                {
                    AllConfigKeyList.Add(item);
                }
            }
        }
    }
}
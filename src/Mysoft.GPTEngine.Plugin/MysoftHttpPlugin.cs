using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.Plugin.Http;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin
{
    public sealed class MysoftHttpPlugin : HttpPluginBase
    {
        private IHttpContextAccessor _httpContextAccessor;
        private HttpClient _httpClient;
        private const string _mysoftCookies = "Mysoft-Cookies";
        [ActivatorUtilitiesConstructor]
        public MysoftHttpPlugin(IHttpClientFactory clientFactory, IHttpContextAccessor httpContextAccessor) : base(clientFactory)
        {
            _httpContextAccessor = httpContextAccessor;
            _httpClient = clientFactory.CreateClient("Mysoft");
        }

        /// <summary>
        /// Sends an HTTP POST request to the specified URI and returns the response body as a string.
        /// </summary>
        /// <param name="uri">URI of the request</param>
        /// <param name="body">The body of the request</param>
        /// <param name="cancellationToken">The token to use to request cancellation.</param>
        /// <returns>The response body as a string.</returns>
        [KernelFunction, Description("Makes a POST request to a uri")]
        public Task<string> PostAsync(
            [Description("The URI of the request")] string uri,
            [Description("The body of the request")] string body,
            CancellationToken cancellationToken = default)
        {
            var httpContent = new StringContent(body);
            var httpCookies = _httpContextAccessor.HttpContext.Request.Headers[_mysoftCookies];
            if (string.IsNullOrWhiteSpace(httpCookies) == false)
            {
                var cookies = JsonConvert.DeserializeObject<List<Cookie>>(httpCookies);
                if (cookies?.Count == 0)
                {
                    return this.SendRequestAsync(uri, HttpMethod.Post, httpContent, _httpClient, cancellationToken);
                }
                var myApiAuthorizationCookie = cookies?.FirstOrDefault(x => x != null && x.Name != null && x.Name.Equals("my-api-Authorization"));
                if (myApiAuthorizationCookie != null)
                {
                    httpContent.Headers.Add("my-api-Authorization", myApiAuthorizationCookie.Value);
                }
                else
                {
                    httpContent.Headers.Add("Cookie", httpCookies.ToString());
                }

                return this.SendRequestAsync(uri, HttpMethod.Post, httpContent, _httpClient, cancellationToken);
            }

            return this.SendRequestAsync(uri, HttpMethod.Post, httpContent, _httpClient, cancellationToken);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Domain.McpServerEnhancement.Services;

namespace Mysoft.GPTEngine.WebApplication.Middleware
{
    /// <summary>
    /// MCP Headers 中间件 - 捕获 MCP 请求的 headers 并传递给上下文服务
    /// </summary>
    public class McpHeadersMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<McpHeadersMiddleware> _logger;

        public McpHeadersMiddleware(RequestDelegate next, ILogger<McpHeadersMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IMcpContextService mcpContextService)
        {
            // 检查是否是 MCP 端点请求
            if (context.Request.Path.StartsWithSegments("/mcp_server/mcp"))
            {
                _logger.LogInformation("检测到 MCP 请求: {Method} {Path}", context.Request.Method, context.Request.Path);

                // 提取相关的 headers
                var headers = ExtractRelevantHeaders(context.Request.Headers);
                
                if (headers.Count > 0)
                {
                    _logger.LogInformation("从 MCP 请求中提取到 {Count} 个相关 headers", headers.Count);
                    mcpContextService.SetCurrentRequestHeaders(headers);
                }
                else
                {
                    _logger.LogWarning("MCP 请求中未找到相关的认证 headers");
                }

                try
                {
                    // 继续处理请求
                    await _next(context);
                }
                finally
                {
                    // 请求完成后清除上下文
                    mcpContextService.ClearCurrentRequestHeaders();
                    _logger.LogDebug("MCP 请求处理完成，已清除上下文");
                }
            }
            else
            {
                // 非 MCP 请求，直接传递
                await _next(context);
            }
        }

        /// <summary>
        /// 提取所有 headers（确保完整转发）
        /// </summary>
        private Dictionary<string, string> ExtractRelevantHeaders(IHeaderDictionary requestHeaders)
        {
            var headers = new Dictionary<string, string>();

            // 提取所有 headers，确保完整转发
            foreach (var header in requestHeaders)
            {
                var value = header.Value.ToString();
                if (!string.IsNullOrEmpty(value))
                {
                    headers[header.Key] = value;
                    _logger.LogDebug("提取到 header: {HeaderName}={HeaderValue}", header.Key,
                        header.Key.Contains("authorization", StringComparison.OrdinalIgnoreCase) ||
                        header.Key.Contains("key", StringComparison.OrdinalIgnoreCase) ||
                        header.Key.Contains("token", StringComparison.OrdinalIgnoreCase) ? "***" : value);
                }
            }

            _logger.LogInformation("MCP 请求提取到 {Count} 个 headers，确保完整转发", headers.Count);

            return headers;
        }
    }
}

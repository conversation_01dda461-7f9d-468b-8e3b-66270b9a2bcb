using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Milvus.Client;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Baidu;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Extensions;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen;
using Mysoft.GPTEngine.SemanticKernel.Core.Xunfei;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.WebApplication
{
    public class SemanticKernelFilter : IAsyncActionFilter
    {
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly IKernelBuilder _kernelBuilder;
        private readonly MemoryBuilder _memoryBuilder;
        private readonly ModelInstanceAppService _modelInstanceAppService;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        public SemanticKernelFilter(IKernelBuilder kernelBuilder, MemoryBuilder memoryBuilder, IMysoftContextFactory mysoftContextFactory
            , ModelInstanceAppService modelInstanceAppService, MysoftMemoryCache mysoftMemoryCache)
        {
            _kernelBuilder = kernelBuilder;
            _modelInstanceAppService = modelInstanceAppService;
            _mysoftContextFactory = mysoftContextFactory;
            _memoryBuilder = memoryBuilder;
            _mysoftMemoryCache = mysoftMemoryCache;
        }
        
        private static SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        private async Task<List<ModelInstanceDto>> GetModelInstancesAll()
        {
            var modelInstances = _mysoftMemoryCache.GetModelInstanceCache();
            if (modelInstances != null)
            {
                return await Task.FromResult(modelInstances);
            }
            await _semaphore.WaitAsync(); // 获取许可证
            try
            {
                modelInstances = _mysoftMemoryCache.GetModelInstanceCache();
                if (modelInstances == null)
                {
                    modelInstances = await _modelInstanceAppService.GetModelInstancesAsync();
                    foreach (var modelInstance in modelInstances)
                    {
                        modelInstance.ApiKey = AesHelper.Decrypt(modelInstance.ApiKey);
                    }
                    _mysoftMemoryCache.SetModelInstanceCache(modelInstances);
                }
                return await Task.FromResult(modelInstances);
            }
            finally
            {
                _semaphore.Release(); // 释放许可证
            }
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var modelInstances = await GetModelInstancesAll();
            List<ServiceTypeEnum> textServiceTypeEnumList = new List<ServiceTypeEnum>()
            {
                ServiceTypeEnum.TextGeneration,
                ServiceTypeEnum.FileComprehend,
                ServiceTypeEnum.ImageComprehend
            };
            var textModelInstances = modelInstances.Where(x => textServiceTypeEnumList.Contains(x.ServiceTypeEnum)).ToList();
            var embeddingInstances = modelInstances.Where(x => x.ServiceTypeEnum == ServiceTypeEnum.Embedding).ToList();

            await _kernelBuilder.AddChatCompletionServices(textModelInstances);

            await AddEmbeddingServices(embeddingInstances);

#pragma warning disable IDE0058 // 永远不会使用表达式值
            await next();
#pragma warning restore IDE0058 // 永远不会使用表达式值
        }
        private async Task AddEmbeddingServices(List<ModelInstanceDto> modelInstances)
        {
            try
            {
                //设置支持非HTTPS
                AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2UnencryptedSupport", true);
                //设置System.Drawing跨平台
                AppContext.SetSwitch("System.Drawing.EnableUnixSupport", true);
                if (modelInstances?.Count == 0) return;
                var myContext = _mysoftContextFactory.GetMysoftContext();

                //租户code 作为数据库名，需要先判断一下数据库是否存在，不存在则创建
                //创建一个mv MemoryBuilder
                MilvusClient milvusClient = myContext.MemoryStore.CreateMilvusClient(isThrowException:false);
                IReadOnlyList<string> dataBases = await milvusClient.ListDatabasesAsync();
                if (!dataBases.Contains(myContext.TenantCode))
                {
                    await milvusClient.CreateDatabaseAsync(myContext.TenantCode);
                }
#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0020 
                _memoryBuilder.WithMemoryStore(myContext.MemoryStore.CreateMilvusMemoryStore(myContext.TenantCode));
                modelInstances.ForEach(modelInstance =>
                {
                    string chatCompletionType = null;
                    switch (modelInstance.ServiceTypeEnum)
                    {
                        case ServiceTypeEnum.TextGeneration:
                            chatCompletionType = ChatCompletionTypeDto.TextGeneration;
                            break;
                        case ServiceTypeEnum.FileComprehend:
                            chatCompletionType = ChatCompletionTypeDto.FileComprehend;
                            break;
                        case ServiceTypeEnum.ImageComprehend:
                            chatCompletionType = ChatCompletionTypeDto.ImageComprehend;
                            break;
                    }
                    var serviceId = modelInstance.IsDefault ? chatCompletionType : modelInstance.InstanceCode;

                    switch (modelInstance.ModelType)
                    {
                        case ModelTypeEnum.AzureOpenAI:

                            _kernelBuilder.AddAzureOpenAIEmbedding(deploymentName: modelInstance.DeploymentName, modelId: modelInstance.ModelCode, endpoint: modelInstance.Endpoint, apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.OpenAI:
                            _kernelBuilder.AddOpenAIEmbedding(modelId: modelInstance.ModelCode, apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.Ali:
                            _kernelBuilder.AddQwenEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.Xunfei:
                            _kernelBuilder.AddXunfeiEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, serviceId: serviceId);
                            break;
                        case ModelTypeEnum.Baidu:
                            _kernelBuilder.AddBaiduEmbedding(modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, clientId: modelInstance.ClientID, serviceId: serviceId);
                            break;
                        default:
                            string modelInstanceString = JsonConvert.SerializeObject(modelInstance);
                            CustomRedirectHttpDto customRedirectHttpDto = JsonConvert.DeserializeObject<CustomRedirectHttpDto>(modelInstanceString);
                            MysoftKernelBuilderExtensions.AddMysoftEmbedding(_kernelBuilder, deploymentName: modelInstance.DeploymentName, modelId: modelInstance.ModelCode, endpoint: new Uri(modelInstance.Endpoint), apiKey: modelInstance.ApiKey, clientId: modelInstance.ClientID, vendor: modelInstance.Vendor, serviceId: serviceId, httpClient: new HttpClient(new CustomRedirectingHandler(customRedirectHttpDto)));
                            break;
                    }
                });
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }
    }
}

using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;

namespace Mysoft.GPTEngine.WebApplication
{
    public static class SwaggerSetUp
    {
        public const String ApiName = "天际GPT引擎";
        public const String Version = "v1";
        public static IServiceCollection AddSwaggerSetup(this IServiceCollection services)
        {
            if (services == null) throw new ArgumentNullException(nameof(services));

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc(Version, new OpenApiInfo
                {
                    // {ApiName} 定义成全局变量，方便修改
                    Version = Version,
                    Title = $"{ApiName} 接口文档",
                    Description = $"{ApiName} HTTP API {Version}",
                });
                c.OrderActionsBy(o => o.RelativePath);
            });
            return services;

        }
    }
}

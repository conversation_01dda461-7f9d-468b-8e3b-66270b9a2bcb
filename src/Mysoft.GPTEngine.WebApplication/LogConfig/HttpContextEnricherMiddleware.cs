using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Serilog.Context;

namespace Mysoft.GPTEngine.WebApplication.LogConfig
{
    public class HttpContextEnricherMiddleware
    {
        private readonly RequestDelegate _next;  
        private readonly IMysoftContextFactory _mysoftContextFactory;  
  
        public HttpContextEnricherMiddleware(RequestDelegate next, IMysoftContextFactory mysoftContextFactory)  
        {  
            _next = next;  
            _mysoftContextFactory = mysoftContextFactory;  
        }  
  
        public async Task InvokeAsync(HttpContext context)
        {
            MysoftContext mysoftContext = _mysoftContextFactory.GetMysoftContext(); 
  
            // 使用LogContext将属性推送到当前日志上下文中  
            using (LogContext.PushProperty("tenantCode", mysoftContext.TenantCode))  
            using (LogContext.PushProperty("userCode", mysoftContext.UserContext.UserCode))  
            {  
                // 继续处理请求  
                await _next(context);  
            }  
        }  
    }
}
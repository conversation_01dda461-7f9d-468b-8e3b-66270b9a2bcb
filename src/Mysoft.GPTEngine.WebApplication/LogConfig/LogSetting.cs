using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Mysoft.GPTEngine.Common.Constants;
using Mysoft.GPTEngine.Common.fast;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Templates;

namespace Mysoft.GPTEngine.WebApplication.LogConfig
{
    public class LogSetting
    {
        public static void Init()
        {
            var loggerConfiguration = new LoggerConfiguration()
                // .MinimumLevel.Information()// 设置日志记录的最小级别  
                .Enrich.FromLogContext()
                .Enrich.WithThreadId()
                .Enrich.With<InvocationContextEnricher>()
                .Enrich.WithThreadName()
                .Enrich.WithExceptionDetails()
                .WriteTo.Console(
                    outputTemplate:
                    "{Timestamp:yyyy-MM-dd HH:mm:ss:fff} [{Level:u3}] [{tenantCode}] [{userCode}] [{ThreadId}] [{Endpoint}] {Message:lj}{NewLine}{Exception}")
                .WriteTo.File(
                    formatter: new ExpressionTemplate(
                        "{ {Time:ToString(@t,'yyyy-MM-dd HH:mm:ss:fff'),LogId: LogId,TenantCode: tenantCode,UserCode: userCode,Message:@m,Level:@l,SourceContext: SourceContext, Exception:@x,Properties:@p} }\n"),
                    path: Path.Combine("logs", "Run", ".log"), // 日志文件名模板，注意这里不再包含日期部分
                    rollingInterval: RollingInterval.Day, // 按天滚动  
                    rollOnFileSizeLimit: true, // 当文件大小达到限制时滚动  
                    fileSizeLimitBytes: 10485760, // 文件大小限制，例如10MB
                    retainedFileCountLimit: 30, // 保留最近30个滚动文件
                    restrictedToMinimumLevel: LogEventLevel.Error
                );
            
            SetLogLevel(loggerConfiguration);
            
            Log.Logger = loggerConfiguration.CreateLogger();

            FastLogger.Init();
        }

        private static void SetLogLevel(LoggerConfiguration configuration)
        {
            var logLevel = Environment.GetEnvironmentVariable(SystemConfigConstants.LogLevel);
            if (logLevel == null)
            {
                configuration.MinimumLevel.Information();
                return;
            }
            
            switch (logLevel.Trim())
            {
                case nameof(LogEventLevel.Verbose):
                    configuration.MinimumLevel.Verbose();
                    break;
                case nameof(LogEventLevel.Debug):
                    configuration.MinimumLevel.Debug();
                    break;
                case nameof(LogEventLevel.Warning):
                    configuration.MinimumLevel.Warning();
                    break;
                case nameof(LogEventLevel.Error):
                    configuration.MinimumLevel.Error();
                    break;
                case nameof(LogEventLevel.Fatal):
                    configuration.MinimumLevel.Fatal();
                    break;
                default:
                    configuration.MinimumLevel.Information();
                    break;
            }
        }
    }
    
    public class InvocationContextEnricher : ILogEventEnricher
    {   
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            string actionName = null;
            string sourceContext = null;
            StackFrame callerFrame = null;
            if (logEvent.Properties.ContainsKey("SourceContext"))
            {
                sourceContext = ((ScalarValue)logEvent.Properties["SourceContext"]).Value?.ToString();
                callerFrame = GetCallerStackFrame(sourceContext);
            }

            // if (logEvent.Properties.ContainsKey("ActionName"))
            // {
            //     actionName = ((ScalarValue)logEvent.Properties["ActionName"]).Value?.ToString();
            //     actionName = actionName?.Split(" ")[0];
            //     callerFrame = GetCallerStackFrame(actionName);
            // }
            
            string endpoint = string.IsNullOrEmpty(actionName) ? sourceContext : actionName;
            
            if (callerFrame != null)
            {
                var methodName = callerFrame.GetMethod()?.Name;
                var lineNumber = callerFrame.GetFileLineNumber();
                if (!string.IsNullOrEmpty(methodName))
                {
                    endpoint = $"{endpoint}.{methodName}:{lineNumber}";
                }
                
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("MethodName", methodName));
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("LineNumber", lineNumber));
            }

            if (endpoint != null)
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("Endpoint", ReplaceWithShortNamespace(endpoint)));                
            }
        }

        private static string ReplaceWithShortNamespace(string input)
        {
            var pattern = @"\b((?:[A-Z][a-zA-Z0-9]*\.)+)([A-Z][a-zA-Z0-9]*)\b";

            return Regex.Replace(input, pattern, match =>
            {
                var namespacePart = match.Groups[1].Value.TrimEnd('.');
                var methodPart = match.Groups[2].Value;

                var shortNamespace = string.Join(".", namespacePart
                    .Split('.')
                    .Select(part => part.Length > 0 ? char.ToLower(part[0]).ToString() : ""));

                return $"{shortNamespace}.{methodPart}";
            });
        }

        private StackFrame GetCallerStackFrame(string className)
        {
            var trace = new StackTrace(true);
            var frames = trace.GetFrames();
            
            var callerFrame = frames.FirstOrDefault(f => f.GetMethod().DeclaringType?.FullName == className);

            return callerFrame;
        }
    }
}
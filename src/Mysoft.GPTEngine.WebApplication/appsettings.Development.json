{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "RabbitMq": {"HostName": "127.0.0.1", "Port": 5672, "UserName": "guest", "Password": "1234567", "VirtualHost": "/"}, "ReverseProxy": {"Routes": {"static_source_route": {"ClusterId": "gpt_builder", "Match": {"Path": "/gptbuilder/{*all}"}}, "api_source_route": {"ClusterId": "gpt_builder", "Match": {"Path": "/api/42000101/{*all}"}, "AccessTokenApi": ["/api/42000101/assistant/chatList", "/api/42000101/assistant/chatEdit"]}, "api_plan_source_route": {"ClusterId": "gpt_builder", "Match": {"Path": "/api/42001401/{*all}"}}, "api_privacyAgreement_source_route": {"ClusterId": "gpt_builder", "Match": {"Path": "/api/42001301/privacyAgreement/{*all}"}}}, "Clusters": {"gpt_builder": {"Destinations": {"destination": {"Address": "http://modelingplatform:9300"}}}}}}
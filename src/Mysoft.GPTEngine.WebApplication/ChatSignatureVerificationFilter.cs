using System;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;

/**
 * 会话签名校验
 */
namespace Mysoft.GPTEngine.WebApplication
{
    public class ChatSignatureVerificationFilter : IAsyncActionFilter
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<ChatSignatureVerificationFilter> _logger;

        public ChatSignatureVerificationFilter(IHttpContextAccessor httpContextAccessor, ILogger<ChatSignatureVerificationFilter> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            try
            {
                // 获取原始请求
                var request = context.HttpContext.Request;
                string requestBody = "";

                // 确保请求体可以多次读取
                request.EnableBuffering();
                request.Body.Seek(0, SeekOrigin.Begin);

                // 使用异步方式读取请求体，并添加超时保护
                using (var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                using (var ms = new MemoryStream())
                {
                    try
                    {
                        await request.Body.CopyToAsync(ms, cancellationTokenSource.Token);
                        requestBody = Encoding.UTF8.GetString(ms.ToArray());
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogWarning("读取请求体超时，跳过签名验证");
                        // 重置请求体位置以供后续处理
                        request.Body.Seek(0, SeekOrigin.Begin);
                        await next();
                        return;
                    }
                    catch (IOException ioEx)
                    {
                        _logger.LogWarning(ioEx, "读取请求体时发生IO异常，可能是网络连接问题，跳过签名验证");
                        // 重置请求体位置以供后续处理
                        request.Body.Seek(0, SeekOrigin.Begin);
                        await next();
                        return;
                    }
                }

                // 重置请求体位置以供后续处理
                request.Body.Seek(0, SeekOrigin.Begin);

                // 如果需要，可以在这里计算签名
                if (!string.IsNullOrEmpty(requestBody))
                {
                    CalculateSignature(requestBody);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取请求体时发生错误");
                // 对于其他异常，也跳过签名验证以避免阻断正常流程
                try
                {
                    var request = context.HttpContext.Request;
                    request.Body.Seek(0, SeekOrigin.Begin);
                }
                catch
                {
                    // 如果重置失败，记录日志但不抛出异常
                    _logger.LogWarning("无法重置请求体位置");
                }
            }

            // 继续执行下一个过滤器或操作
            await next();
        }
        
        public void CalculateSignature(String bodyParams)
        {
            try
            {
                // 获取头里的时间戳和签名
                var timestamp = _httpContextAccessor.HttpContext.Request.Headers["x-ai-ts"].FirstOrDefault();
                var signature = _httpContextAccessor.HttpContext.Request.Headers["x-ai-sign"].FirstOrDefault();

                // 获取常量里的key
                var key = "ZD3MZOlQkLbaG0Ecs1S171mQQDEvmsKExJXfkxoYPgQ=";
                if (string.IsNullOrEmpty(timestamp) || string.IsNullOrEmpty(bodyParams) || string.IsNullOrEmpty(key))
                {
                    _logger.LogWarning("签名验证参数不完整: timestamp={0}, bodyParams长度={1}, key存在={2}",
                        !string.IsNullOrEmpty(timestamp), bodyParams?.Length ?? 0, !string.IsNullOrEmpty(key));
                    throw new ArgumentException("timestamp, bodyParams, 和 key 不能为空");
                }

                //计算签名的方式化成 md5
                string newSignature = GetMd5Hash(key + timestamp + bodyParams);
                if (signature != newSignature)
                {
                    _logger.LogWarning("数据签名验证失败: 期望签名={0}, 实际签名={1}, 时间戳={2}, 请求体长度={3}",
                        signature, newSignature, timestamp, bodyParams.Length);
                    throw new Exception("数据签名验证失败");
                }

                _logger.LogDebug("数据签名验证成功");
            }
            catch (Exception ex) when (!(ex is ArgumentException))
            {
                _logger.LogError(ex, "计算签名时发生异常");
                throw new Exception("数据签名验证失败", ex);
            }
        }
        
        public static string GetMd5Hash(string input)
        {
            // 创建MD5实例
            using (MD5 md5 = MD5.Create())
            {
                // 将输入字符串转换为字节数组并计算哈希值
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);

                // 将字节数组转换为十六进制字符串
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("x2"));
                }
                return sb.ToString();
            }
        }
    }
} 
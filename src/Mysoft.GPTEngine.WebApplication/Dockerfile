#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

#Depending on the operating system of the host machines(s) that will build or run the containers, the image specified in the FROM statement may need to be changed.
#For more information, please see https://aka.ms/containercompat

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

COPY src/Mysoft.GPTEngine.WebApplication/Fonts/simsun.ttc /usr/share/fonts/truetype/

# 更换APT源为华为云镜像
RUN sed -i 's|deb.debian.org|mirrors.huaweicloud.com|g' /etc/apt/sources.list.d/debian.sources \
    && sed -i 's|security.debian.org|mirrors.huaweicloud.com|g' /etc/apt/sources.list.d/debian.sources

RUN apt-get update \
    && apt-get install -y --no-install-recommends fontconfig libgdiplus libc6-dev libfontconfig1 libwebp-dev webp \
    && rm -rf /var/lib/apt/lists/*

ENV ASPNETCORE_URLS=http://0.0.0.0:80

ENV NUGET_CONFIG=/NuGet.Config

EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

COPY ["src/NuGet.Config", "/NuGet.Config"]
COPY ["src/Mysoft.GPTEngine.WebApplication/Mysoft.GPTEngine.WebApplication.csproj", "Mysoft.GPTEngine.WebApplication/"]
COPY ["src/Mysoft.GPTEngine.Application/Mysoft.GPTEngine.Application.csproj", "Mysoft.GPTEngine.Application/"]
COPY ["src/Mysoft.GPTEngine.Application.Contracts/Mysoft.GPTEngine.Application.Contracts.csproj", "Mysoft.GPTEngine.Application.Contracts/"]
COPY ["src/Mysoft.GPTEngine.Domain.Shared/Mysoft.GPTEngine.Domain.Shared.csproj", "Mysoft.GPTEngine.Domain.Shared/"]
COPY ["src/Mysoft.GPTEngine.Domain/Mysoft.GPTEngine.Domain.csproj", "Mysoft.GPTEngine.Domain/"]
COPY ["src/Mysoft.GPTEngine.Plugin/Mysoft.GPTEngine.Plugin.csproj", "Mysoft.GPTEngine.Plugin/"]
COPY ["src/Mysoft.GPTEngine.SemanticKernel.Core/Mysoft.GPTEngine.SemanticKernel.Core.csproj", "Mysoft.GPTEngine.SemanticKernel.Core/"]
COPY ["src/Mysoft.GPTEngine.Common/Mysoft.GPTEngine.Common.csproj", "Mysoft.GPTEngine.Common/"]
COPY ["src/Mysoft.GPTEngine.Diagnostics.Extend/Mysoft.GPTEngine.Diagnostics.Extend.csproj", "Mysoft.GPTEngine.Diagnostics.Extend/"]
RUN dotnet restore "./Mysoft.GPTEngine.WebApplication/Mysoft.GPTEngine.WebApplication.csproj"
COPY . .
WORKDIR "src/Mysoft.GPTEngine.WebApplication"
RUN dotnet build "./Mysoft.GPTEngine.WebApplication.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Mysoft.GPTEngine.WebApplication.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

ENTRYPOINT ["dotnet", "Mysoft.GPTEngine.WebApplication.dll"]
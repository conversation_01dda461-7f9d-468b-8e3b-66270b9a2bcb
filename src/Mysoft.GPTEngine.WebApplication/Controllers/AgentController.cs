using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// 模型实例控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class AgentController : ControllerBase
    {
        private AgentAppService _service;
        private ILogger<AgentController> _logger;

        public AgentController(AgentAppService service, ILogger<AgentController> logger)
        {
            _service = service;
            _logger = logger;
        }


        /// <summary>
        /// 对话完成
        /// </summary>
        /// <param name="actionDto">包含历史会话的DTO对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        [ServiceFilter(typeof(ChatSignatureVerificationFilter))]
        public async Task StreamingChatCompletionAsync(ChatInputDto chatInput, CancellationToken cancellationToken)
        {
            await _service.StreamingChatCompletionAsync(chatInput, cancellationToken);
        }
        /// <summary>
        /// 对话完成
        /// </summary>
        /// <param name="actionDto">包含历史会话的DTO对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> GetChatMessageLog(Guid messageGuid)
        {
            return await _service.GetChatMessageLog(messageGuid);
        }
        
        /// <summary>
        /// 对话完成
        /// </summary>
        /// <param name="chatGuid">包含历史会话的DTO对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> GetChatMessageList(Guid chatGuid)
        {
            return await _service.GetChatMessageList(chatGuid);
        }
        
        /// <summary>
        /// 创建一个技能会话
        /// </summary>
        /// <param name="createChatInputDto">包含技能GUID等信息的输入DTO</param>
        /// <param name="cancellationToken">用于取消任务的取消令牌</param>
        /// <returns>会话GUID</returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> CreateChat(CreateChatInputDto createChatInputDto, CancellationToken cancellationToken)
        {
            return await _service.CreateChat(createChatInputDto, cancellationToken);
        }
        
        /// <summary>
        /// 调用记录对话完成
        /// </summary>
        /// <param name="actionDto">包含历史会话的DTO对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ChatCompletion(ChatInputDto chatInput, CancellationToken cancellationToken)
        {
            return await _service.ChatCompletionAsync(chatInput, false, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// 彻底清除指定用户的会话记忆（包括缓存和数据库）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ClearUserConversationMemoryCompletely([FromBody] string userId)
        {
            return await _service.ClearUserConversationMemoryCompletely(userId);
        }

        /// <summary>
        /// 彻底清除指定用户指定会话的记忆（包括缓存和数据库）
        /// </summary>
        /// <param name="request">包含用户ID和会话ID的请求对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ClearUserChatConversationMemoryCompletely([FromBody] ClearChatMemoryRequest request)
        {
            return await _service.ClearUserChatConversationMemoryCompletely(request.UserId, request.ChatGuid);
        }

    }
}

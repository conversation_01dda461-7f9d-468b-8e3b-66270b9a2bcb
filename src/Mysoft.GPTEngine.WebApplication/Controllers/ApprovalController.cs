using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// 智能审批控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class ApprovalController : ControllerBase
    {
        private ApprovalAppService _approvalAppService;
        public ApprovalController(ApprovalAppService approvalAppService)
        {
            _approvalAppService = approvalAppService;
        }
        
        /// <summary>
        /// 调用记录对话完成
        /// </summary>
        /// <param name="actionDto">包含历史会话的DTO对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ExecutePlan(ExecutePlanJobDto executePlanJobDto, CancellationToken cancellationToken)
        {
            return await _approvalAppService.ExecutePlan(executePlanJobDto, cancellationToken);
        }
    }
}

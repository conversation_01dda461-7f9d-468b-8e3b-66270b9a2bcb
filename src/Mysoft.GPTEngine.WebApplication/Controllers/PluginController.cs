using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// 提示词控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class PluginController : ControllerBase
    {
        private PluginAppService _service;
        public PluginController(PluginAppService service) { _service = service; }


        /// <summary>
        /// 测试提示词场景
        /// </summary>
        /// <param name="promptTestSceneGUID">测试场景GUID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> MysoftIPassApi(ApiRequestDto requestDto)
        {
            return await _service.MysoftIPassApi(requestDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> YkfInit(ThirdApiRequestDto thirdApiRequestDto)
        {
            return await _service.YkfInit(thirdApiRequestDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> GetFunctionsMetadata(string downloadUrl)
        {
            return await _service.GetFunctionsMetadata(downloadUrl);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> GetApiInfoByMetadata(string pluginGUID)
        {
            return await _service.GetApiInfoByMetadata(pluginGUID);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
#pragma warning disable SKEXP0010
    /// <summary>
    /// 知识库控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class KnowledgeController : ControllerBase
    {
        private KnowledgeAppService _service;
        public KnowledgeController(KnowledgeAppService service) { _service = service; }

        /// <summary>
        /// 根据表单json解析Word字段
        /// </summary>
        /// <param name="analysisWordDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> AnalysisWordByForm(AnalysisWordDto analysisWordDto)
        {
            return await _service.AnalysisWordByForm(analysisWordDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> DeleteEmbeddingInfoByFileGUID(DocumentFileDto documentFileDto)
        {
            return await _service.DeleteEmbeddingInfoByFileGUID(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ConvertDocumentToImg(DocumentDto documentDto)
        {
            return await _service.ConvertDocumentToImg(documentDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> RefreshKnowledgeFileSection(DocumentFileDto documentFileDto)
        {
            return await _service.RefreshKnowledgeFileSection(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> RefreshKnowledgeTotal(DocumentFileDto documentFileDto)
        {
            return await _service.RefreshKnowledgeTotal(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> SaveKnowledgeFileSectionAndQuestion(KnowledgeFileSectionDto knowledgeFileSectionDto)
        {
            return await _service.SaveKnowledgeFileSectionAndQuestion(knowledgeFileSectionDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> DelKnowledgeFileSection(DocumentFileDto documentFileDto)
        {
            return await _service.DelKnowledgeFileSection(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        [RequestSizeLimit(500_000_000)]
        public async Task<ActionResultDto> SaveEmbeddingFile(DocumentFileDto documentFileDto)
        {
            return await _service.SaveEmbeddingFile(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<List<KnowledgeFileSectionDto>> GetKnowledgeFileSections(List<Guid> sectionGuids)
        {
            return await _service.GetKnowledgeFileSections(sectionGuids);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> SearchKnowledge(KnowledgeSearchDto knowledgeSearchDto)
        {
            return await _service.SearchKnowledge(knowledgeSearchDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> QuestionGenerate(KnowledgeFileGUIDsDto knowledgeFileDtos)
        {
            return await _service.QuestionGenerate(knowledgeFileDtos);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> DeleteKnowledge(DocumentFileDto documentFileDto)
        {
            return await _service.DeleteKnowledge(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> KnowledgeExcuteTest(KnowledgeExcuteTestDto knowledgeExcuteTestDto)
        {
            return await _service.KnowledgeExcuteTest(knowledgeExcuteTestDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> CreateCollection(DocumentFileDto documentFileDto)
        {
            return await _service.CreateCollection(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> KnowledgeFileSectionDisable(DocumentFileDto documentFileDto)
        {
            return await _service.KnowledgeFileSectionDisable(documentFileDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> KnowledgeFileSectionDisableALL(DocumentFileDto documentFileDto)
        {
            return await _service.KnowledgeFileSectionDisableALL(documentFileDto);

        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> SaveKnowledgeFileSectionQuestion(KnowledgeQuestionDto knowledgeQuestionDto)
        {
            return await _service.SaveKnowledgeFileSectionQuestion(knowledgeQuestionDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> DeleteKnowledgeFileSectionQuestion(KnowledgeQuestionDto knowledgeQuestionDto)
        {
            return await _service.DeleteKnowledgeFileSectionQuestion(knowledgeQuestionDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> KnowledgeEvaluatingTaskExcute(KnowledgeEvaluatingTaskDto knowledgeEvaluatingTaskDto)
        {
            return await _service.KnowledgeEvaluatingTaskExcute(knowledgeEvaluatingTaskDto);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> SaveEmbeddingContent(EmbeddingContentDto embeddingContent)
        {
            return await _service.SaveEmbeddingContent(embeddingContent);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> DeleteEmbeddingContent(EmbeddingContentDto embeddingContent)
        {
            return await _service.DeleteEmbeddingContent(embeddingContent);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> RefreshQuestionDisableStatus(QuestionEmbeddingDto questionEmbeddingDto)
        {
            return await _service.RefreshQuestionDisableStatus(questionEmbeddingDto);
        }
        
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> TestEmbeddingDatabase()
        {
            return await _service.TestEmbeddingDatabase();
        }
        
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> TextEmbeddings(KnowledgeEmbeddingsParamDto knowledgeEmbeddingsParamDto)
        {
            return await _service.TextEmbeddings(knowledgeEmbeddingsParamDto);
        }

    }
}

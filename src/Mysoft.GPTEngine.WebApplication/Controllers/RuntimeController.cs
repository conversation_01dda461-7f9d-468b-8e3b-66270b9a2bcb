using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application.Contracts;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// 运行时
    /// </summary>
    [ApiController]
    [Route("/runtime")]
    public class RuntimeController : ControllerBase
    {
        /// <summary>
        /// 测试提示词场景
        /// </summary>
        /// <param name="promptTestSceneGUID">测试场景GUID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("health/liveness")]
        public async Task<ActionResultDto> HealthLiveness()
        {
            return await Task.FromResult(new ActionResultDto(){Success = true});
        }
        
    }
}
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Dtos.approval;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// 智能审批控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class EvalController : ControllerBase
    {
        private EvalAppService _evalAppService;
        public EvalController(EvalAppService evalAppService)
        {
            _evalAppService = evalAppService;
        }
        
        /// <summary>
        /// 调用记录对话完成
        /// </summary>
        /// <param name="actionDto">包含历史会话的DTO对象</param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ExecutePlan(EvalTaskRecordDto<Dictionary<string,RuleDto>> evalTaskRecordDto, CancellationToken cancellationToken)
        {
            return await _evalAppService.ExecutePlan(evalTaskRecordDto, cancellationToken);
        }
    }
}

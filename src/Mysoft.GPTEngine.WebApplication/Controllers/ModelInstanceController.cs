using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    /// <summary>
    /// 模型实例控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    [ServiceFilter(typeof(UserAuthenticationFilter))]
    public class ModelInstanceController : ControllerBase
    {
        private ModelInstanceAppService _modelInstanceAppService;
        public ModelInstanceController(ModelInstanceAppService modelInstanceAppService) { _modelInstanceAppService = modelInstanceAppService; }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="modelInstance"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> TestConnection(MysoftActionDto<string> actionDto, CancellationToken cancellationToken)
        {
            return await _modelInstanceAppService.TestConnection(actionDto, cancellationToken);
        }
        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="modelInstance"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<List<ModelInstanceDto>> GetModelInstancesAsync()
        {
            return await _modelInstanceAppService.GetModelInstancesAsync();
        }
        /// <summary>
        /// 更新模型可信敏感词缓存
        /// </summary>
        /// <param name="spaceGUID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> UpdateSensitiveWordsCache(string spaceGUID)
        {
            return await _modelInstanceAppService.UpdateSensitiveWordsCache(spaceGUID);
        }
    }
}

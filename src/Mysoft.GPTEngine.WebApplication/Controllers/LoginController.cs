using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.Contracts;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.WebApplication.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class LoginController : ControllerBase
    {
        private ILogger<LoginController> _logger;

        private readonly CheckPlatformTokenService _checkPlatformTokenService;

        private readonly AccessTokenService _accessTokenService;
        
        public LoginController(CheckPlatformTokenService checkPlatformTokenService,AccessTokenService accessTokenService, ILogger<LoginController> logger)
        {
            _logger = logger;
            _checkPlatformTokenService = checkPlatformTokenService;
            _accessTokenService = accessTokenService;
        }
        
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> GetToken(AccessTokenParams accessTokenParams, CancellationToken cancellationToken)
        {
            return await _accessTokenService.GetAccessToken(accessTokenParams, cancellationToken);
        }
        
        [HttpPost]
        [Route("[action]")]
        public async Task<ActionResultDto> ShowToken(AccessTokenContent accessTokenParams, CancellationToken cancellationToken)
        {
            JsonConvert.SerializeObject(accessTokenParams);
            return await Task.FromResult(new ActionResultDto() {Success = true, Message = "暂未实现"});
        }
        
    }
}
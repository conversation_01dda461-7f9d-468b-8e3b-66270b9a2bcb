using System.Linq;
using Microsoft.Extensions.Configuration;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.WebApplication
{
    public class ConfigurationService : IConfigurationService
    {
        private readonly IConfiguration _configuration;

        public ConfigurationService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string GetBuilderUrl()
        {
            return _configuration[EMCConfigConst.ApassUrl];
        }

        public string GetConfigurationItemByKey(string key)
        {
            return _configuration[key];
        }

        public DbInstanceItem GetDbInstanceItem(string tenantCode)
        {
            DbUsed dbUsed = JsonConvert.DeserializeObject<DbUsed>(_configuration[nameof(DbUsed)]);
            if (dbUsed == null)
            {
                return null;
            }
            DbUsedItem dbUsedItem = dbUsed.items.FirstOrDefault(f => f.TenantCode == tenantCode && f.Code == EMCConfigConst.DbCode && f.Category == EMCConfigConst.CategoryType);
            if (dbUsedItem == null)
            {
                return null;
            }
            DbInstances dbInstances = JsonConvert.DeserializeObject<DbInstances>(_configuration[nameof(DbInstances)]);
            DbInstanceItem dbInstanceItem = dbInstances.items.FirstOrDefault(f => f.Id == dbUsedItem.DbMasterInstanceId);
            if (dbInstanceItem == null)
            {
                return null;
            }
            dbInstanceItem.Name = dbUsedItem.DbName;
            return dbInstanceItem;
        }

        public IConfiguration GetIConfiguration()
        {
            return _configuration;
        }
    }
}

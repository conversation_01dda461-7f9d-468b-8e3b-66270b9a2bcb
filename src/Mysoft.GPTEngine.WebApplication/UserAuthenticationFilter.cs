using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.WebApplication
{
    public class UserAuthenticationFilter : IAsyncActionFilter
    {
        private IHttpContextAccessor _httpContextAccessor;
        private ILogger<UserAuthenticationFilter> _logger;
        private CheckPlatformTokenService _checkPlatformTokenService;
        private readonly ApplicationSecurityRepostory _applicationSecurityRepostory;
        private readonly ApplicationRepostory _applicationRepostory;

        public UserAuthenticationFilter(IHttpContextAccessor httpContextAccessor, ApplicationSecurityRepostory applicationSecurityRepostory, 
            ApplicationRepostory applicationRepostory, 
            CheckPlatformTokenService checkPlatformTokenService, ILogger<UserAuthenticationFilter> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _applicationSecurityRepostory = applicationSecurityRepostory;
            _applicationRepostory = applicationRepostory;
            _checkPlatformTokenService = checkPlatformTokenService;
            _logger = logger;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext actionExecutingContext, ActionExecutionDelegate next)
        {

            try
            {
                await CheckUserAuthorization();
            }
            catch (Exception e)
            {
                throw e;
            }
            await next();
            
        }

        private async Task<bool> CheckUserAuthorization()
        {
            if (_httpContextAccessor.HttpContext == null 
                || !_httpContextAccessor.HttpContext.Request.Headers.ContainsKey(MysoftConstant.MysoftContext))
            {
                throw new BusinessException("认证信息不存在");
            }
            var context = _httpContextAccessor.HttpContext.Request.Headers[MysoftConstant.MysoftContext];

            context = AesHelper.Decrypt(context);

            var mysoftContext = JsonConvert.DeserializeObject<MysoftContext>(context);
               
            _httpContextAccessor.AddItem(nameof(MysoftContext), mysoftContext);

            var appSecret = JwtHelper<string>.DefaultSecret;
            PlatformTokenBase64 platformTokenBase64 = _checkPlatformTokenService.GetPlatformToken(_httpContextAccessor.HttpContext);
            if (platformTokenBase64 != null)
            {
                mysoftContext.AuthType = 0;
                var application = await _applicationRepostory.GetSingleAsync((x => x.ApplicationCode == platformTokenBase64.ApplicationCode))
                    .ConfigureAwait(false);
                if(application == null)
                {
                    return await Task.FromResult(false);
                }
                mysoftContext.EnableUserAuthorization = application.EnableUserAuthorization;
                mysoftContext.AuthorizationType = application.AuthorizationType;
                mysoftContext.ApplicationPublisherUserCode = platformTokenBase64.PlatformToken == null || string.IsNullOrEmpty(platformTokenBase64.PlatformToken.UserCode) ? "admin" : platformTokenBase64.PlatformToken.UserCode;
                if (application.EnableUserAuthorization != 1)
                {
                    return await Task.FromResult(true);
                }
                var applicationSecurity = await _applicationSecurityRepostory.
                    GetSingleAsync(x => x.ApplicationGUID == application.ApplicationGUID)
                    .ConfigureAwait(false);
                appSecret = applicationSecurity.AppSecret;
                
                // 添加助手是否开启用户鉴权标识
                mysoftContext.UserContext.EnableApplicationUserAuthorization = true;
                _httpContextAccessor.AddItem(nameof(MysoftContext), mysoftContext);
            }
                
            string accessToken = _httpContextAccessor.HttpContext.Request.Headers["access-token"];
            if (accessToken == null)
            {
                throw new BusinessException("认证信息不存在");
            }
            var accessTokenContent = JwtHelper<AccessTokenContent>.ValidateJwtToken(accessToken, appSecret);

            _httpContextAccessor.AddItem(nameof(AccessTokenContent), accessTokenContent);
            return await Task.FromResult(true);
            
        }
    }
}
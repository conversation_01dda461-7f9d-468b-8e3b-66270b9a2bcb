using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Http;
using Mysoft.GPTEngine.Application;
using Mysoft.GPTEngine.Application.ApprovalDatasource;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.ApiAuthorization;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Repositories.Approval;
using Mysoft.GPTEngine.Domain.Repositories.eval;
using Mysoft.GPTEngine.Domain.Repositories.KnowledgeQuestion;
using Mysoft.GPTEngine.Domain.Services;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.TextExtractDecode;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.Plugin.Activities;
using Mysoft.GPTEngine.Plugin.ContentReview;
using Mysoft.GPTEngine.SemanticKernel.Core.Parsers;
using Mysoft.GPTEngine.WebApplication.LogConfig;
using SqlSugar;
using System;
using Mysoft.GPTEngine.Application.ApprovalDatasource;
using Mysoft.GPTEngine.Domain.Repositories.eval;
using Mysoft.GPTEngine.Plugin.Activities;
using Mysoft.GPTEngine.Plugin.Activities.DataQuery;
using Mysoft.GPTEngine.Domain.ApiAuthorization;
using Mysoft.GPTEngine.Domain.Services;
using Mysoft.GPTEngine.Domain.AgentSkillEnhancement;

namespace Mysoft.GPTEngine.WebApplication
{
    internal static class ServiceExtensions
    {
        internal static IServiceCollection AddService(this IServiceCollection services)
        {
            services.AddMemoryCache();
            services.AddTransient<SqlSugarClient>(s =>
             {
                 SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig() { ConfigId = ".", DbType = DbType.MySql, ConnectionString = "host=.", IsAutoCloseConnection = true });
                 return Db;
             });
            services.AddSingleton<IConfigurationService, ConfigurationService>();
            
            services.AddScoped<MysoftMemoryCache>();

            services.AddScoped<IHttpContextAccessor, HttpContextAccessor>();
            services.AddScoped<IMysoftContextFactory, MysoftContextFactory>();

            services.AddScoped<IMysoftApiService, MysoftApiService>();
            services.AddScoped<IMysoftIPassApiService, MysoftIPassApiService>();
            services.AddScoped<IThirdPartyApiService, ThirdPartyApiService>();
            services.AddScoped<IMysoftCustomerServiceApiService, MysoftCustomerServiceApiService>();


            services.AddScoped<IJsonOutputParsers, JsonOutputParsers>();

            services.AddScoped<ISqlSugarProviderFactory, SqlSugarProviderFactory>();
            
            services.AddScoped<ContentReviewFactory>();
            
            


            //services.AddScoped<SemanticKernelFilter>();

            services.AddHttpClient();
            
            services.AddScoped<IHttpMessageHandlerBuilderFilter, LoggingHttpMessageHandlerBuilderFilter>();

            return services.AddRepostory()
                .AddDomainService()
                .AddAppService();
        }

        internal static IServiceCollection AddRepostory(this IServiceCollection services)
        {
            services.AddScoped<AssistantRepostory>();
            services.AddScoped<MyParamValueRepostory>();
            services.AddScoped<ModelRepostory>();
            services.AddScoped<ModelInstanceRepostory>();

            services.AddScoped<ChatRepostory>();
            services.AddScoped<ChatArgumentsRepostory>();
            services.AddScoped<ChatMessageRepostory>();
            services.AddScoped<ChatMessageFileRepostory>();
            services.AddScoped<ChatMessageCheckRepostory>();
            services.AddScoped<ChatMessageNodeLogEntityRepostory>();
            services.AddScoped<ChatMessageKnowledgeNodeLogEntityRepostory>();
            services.AddScoped<PublishedSkillRepostory>();
            services.AddScoped<SensitiveWordsRepostory>();

            services.AddScoped<SkillRepostory>();

            services.AddScoped<PromptRepostory>();
            services.AddScoped<PromptTemplateRepostory>();
            services.AddScoped<PromptParamRepostory>();
            services.AddScoped<PromptTestSceneRepostory>();

            services.AddScoped<KnowledgeRepository>();
            services.AddScoped<KnowledgeFileSectionRepository>();
            services.AddScoped<KnowledgeQuestionRepostory>();
            services.AddScoped<KnowledgeFileRepository>();
            services.AddScoped<KnowledgeHyperLinkRepostory>();
            
            services.AddScoped<KnowledgeHyperLinkRepostory>();

            services.AddScoped<KnowledgeTaskRepostory>();
            services.AddScoped<KnowledgeTaskDetailRepostory>();
            services.AddScoped<KnowledgeTaskDataDetailRepostory>();
            services.AddScoped<KnowledgeTaskKeyWordRepostory>();
            services.AddScoped<KnowledgeTaskRecordRepostory>();

            services.AddScoped<QuestionRepository>();
            services.AddScoped<QuestionRelationRepository>();
            
            services.AddScoped<ApplicationRepostory>();
            services.AddScoped<ApplicationReleaseRepostory>();
            services.AddScoped<ApplicationSiteRepostory>();
            services.AddScoped<ApplicationSecurityRepostory>();
            services.AddScoped<PlanInstanceRepostory>();
            services.AddScoped<PlanRepostory>();
            services.AddScoped<PlanRuleInstanceRepostory>();
            services.AddScoped<PluginRepostory>();
            services.AddScoped<PluginMetadataRepostory>();
            services.AddScoped<PlanRuleDataInstanceRepostory>();
            services.AddScoped<PlanDatasourceInstanceRepostory>();
            services.AddScoped<EvalTaskRecordRepostory>();
            services.AddScoped<PlanEvalResultRepostory>();
            services.AddScoped<EvalTaskRepostory>();
            services.AddScoped<McpServiceRepository>();
            services.AddScoped<McpServiceToolRepository>();
            return services;
        }
        internal static IServiceCollection AddDomainService(this IServiceCollection services)
        {
            services.AddScoped<SemanticKernelActivity>();
            services.AddScoped<DataQueryActivity>();
            services.AddScoped<MysoftApiService>();
            services.AddScoped<MysoftConfigurationDomain>();
            services.AddScoped<AgentDomainService>();
            services.AddScoped<DocumentDomainService>();
            services.AddScoped<PromptDomainService>();
            services.AddScoped<BaseDecode>();
            services.AddScoped<IDocumentDecoder, MsWordDecoder>();
            services.AddScoped<IDocumentDecoder, PdfDecoder>();
            services.AddScoped<IDocumentDecoder, ImageDecoder>();
            services.AddScoped<IKnowledgeDomainService, KnowledgeDomainService>();
            services.AddScoped<IMilvusMemoryDomainService, MilvusMemoryDomainService>();
            services.AddScoped<McpCustomService>();
            services.AddScoped<IAuthorization, OAuth>();
            services.AddScoped<ConversationMemoryService>();
            services.AddScoped<IAuthorization, NoAuth>();
            services.AddScoped<FlowDatasourceService>();
            services.AddScoped<PlatformDatasourceService>();
            services.AddScoped<GptPluginDatasrouceService>();
            services.AddScoped<GptSkillDatasourceService>();
            services.AddScoped<ApprovalDatasourceService>();
            services.AddScoped<ApprovalDomainService>();
            services.AddScoped<MysoftFlowApiService>();
            services.AddScoped<BaseDataFixParamHelper>();
            return services;
        }
        internal static IServiceCollection AddAppService(this IServiceCollection services)
        {
            services.AddScoped<ModelInstanceAppService>();
            services.AddScoped<AgentAppService>();
            services.AddScoped<KnowledgeAppService>();
            services.AddScoped<ParameterAppService>();
            services.AddScoped<PromptAppService>();
            services.AddScoped<PluginAppService>();
            services.AddScoped<CheckPlatformTokenService>();
            services.AddScoped<AccessTokenService>();
            services.AddScoped<ApprovalAppService>();
            services.AddScoped<EvalAppService>();
            services.AddScoped<McpAppService>();
            services.AddScoped<UserAuthenticationFilter>();
            services.AddScoped<ChatSignatureVerificationFilter>();
            return services;
        }
    }
}

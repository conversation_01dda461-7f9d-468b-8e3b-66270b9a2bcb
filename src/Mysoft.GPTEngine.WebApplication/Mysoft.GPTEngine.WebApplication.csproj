<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>d9026d5b-d619-400e-a16c-f85e37779c0d</UserSecretsId>
    <DockerDefaultTargetOS>Windows</DockerDefaultTargetOS>
    <NoWarn>$(NoWarn);SKEXP0001</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Fonts\simsun.ttc" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="FastTracker.Abstractions" Version="1.4.2" />
    <PackageReference Include="FastTracker.Agent.AspNetCore" Version="1.4.2" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Abstractions" Version="1.60.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
    <PackageReference Include="ModelContextProtocol.AspNetCore" Version="0.3.0-preview.2" />
    <PackageReference Include="nacos-sdk-csharp" Version="1.3.10" />
    <PackageReference Include="nacos-sdk-csharp.Extensions.Configuration" Version="1.3.10" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.154" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
    <PackageReference Include="YamlDotNet" Version="16.2.0" />
    <PackageReference Include="Yarp.ReverseProxy" Version="1.1.2" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Fonts\simsun.ttc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mysoft.GPTEngine.Application\Mysoft.GPTEngine.Application.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Diagnostics.Extend\Mysoft.GPTEngine.Diagnostics.Extend.csproj" />
    <ProjectReference Include="..\Mysoft.GPTEngine.Plugin\Mysoft.GPTEngine.Plugin.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Service\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Controllers\McpServerController.cs" />
  </ItemGroup>

</Project>

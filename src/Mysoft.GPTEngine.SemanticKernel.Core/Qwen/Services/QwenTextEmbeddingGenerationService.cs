using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Services;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Services
{
    public class QwenTextEmbeddingGenerationService : ITextEmbeddingGenerationService
    {
        private Dictionary<string, object?> AttributesInternal { get; } = new Dictionary<string, object?>();
        private QwenClient Client { get; }

        /// <inheritdoc />
        public IReadOnlyDictionary<string, object?> Attributes => this.AttributesInternal;

        public QwenTextEmbeddingGenerationService(
            string model,
            Uri? endpoint = null,
            string? apiKey = null,
            HttpClient? httpClient = null,
            ILoggerFactory? loggerFactory = null)
        {
            this.Client = new QwenClient(modelId: model, endpoint: endpoint ?? httpClient?.BaseAddress, apiKey: apiKey, httpClient: HttpClientProvider.GetHttpClient(httpClient), logger: loggerFactory?.CreateLogger(this.GetType())
                );
            this.AttributesInternal.Add(AIServiceExtensions.ModelIdKey, model);
        }

        public Task<IList<ReadOnlyMemory<float>>> GenerateEmbeddingsAsync(IList<string> data, Kernel? kernel = null, CancellationToken cancellationToken = default)
            => this.Client.GenerateEmbeddingsAsync(data, kernel, cancellationToken);
    }
}

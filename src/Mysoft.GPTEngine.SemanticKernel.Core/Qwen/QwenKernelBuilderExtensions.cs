// Copyright (c) Microsoft. All rights reserved.

using System;
using System.Net.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Embeddings;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Services;
using Serilog;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Qwen
{
    /// <summary>
    /// Provides extension methods for the <see cref="IKernelBuilder"/> class to configure Hugging Face connectors.
    /// </summary>
    public static class QwenKernelBuilderExtensions
    {

        /// <summary>
        /// Adds an Hugging Face chat completion service with the specified configuration.
        /// </summary>
        /// <param name="builder">The <see cref="IKernelBuilder"/> instance to augment.</param>
        /// <param name="modelId">The name of the Hugging Face model.</param>
        /// <param name="endpoint">The endpoint URL for the chat completion service.</param>
        /// <param name="apiKey">The API key required for accessing the Hugging Face service.</param>
        /// <param name="serviceId">A local identifier for the given AI service.</param>
        /// <param name="httpClient">The HttpClient to use with this service.</param>
        /// <returns>The same instance as <paramref name="builder"/>.</returns>
        public static IKernelBuilder AddQwenChatCompletion(
            this IKernelBuilder builder,
            string modelId,
            Uri? endpoint = null,
            string? apiKey = null,
            string? serviceId = null,
            HttpClient? httpClient = null)
        {
            Verify.NotNull(builder);
            Verify.NotNull(modelId);
            
            var factory = new LoggerFactory();
            factory.AddSerilog();

            builder.Services.AddKeyedSingleton<IChatCompletionService>(serviceId, (serviceProvider, _) =>
                new QwenChatCompletionService(
                    modelId,
                    endpoint,
                    apiKey,
                    HttpClientProvider.GetHttpClient(httpClient, serviceProvider),
                    factory
                ));

            return builder;
        }
        public static IKernelBuilder AddQwenEmbedding(
           this IKernelBuilder builder,
           string modelId,
           Uri? endpoint = null,
           string? apiKey = null,
           string? serviceId = null,
           HttpClient? httpClient = null)
        {
            Verify.NotNull(builder);
            Verify.NotNull(modelId);

            builder.Services.AddKeyedSingleton<ITextEmbeddingGenerationService>(serviceId, (serviceProvider, _) =>
                new QwenTextEmbeddingGenerationService(modelId, endpoint, apiKey, HttpClientProvider.GetHttpClient(httpClient, serviceProvider), serviceProvider.GetService<ILoggerFactory>()
                ));

            return builder;
        }


    }
}

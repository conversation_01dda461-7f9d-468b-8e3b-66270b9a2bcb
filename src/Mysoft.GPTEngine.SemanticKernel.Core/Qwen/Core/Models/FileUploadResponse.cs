using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models
{
    internal sealed class FileUploadResponse
    {
        [JsonPropertyName("id")]
        [JsonRequired]
        public string Id { get; set; } = null!;

        [JsonPropertyName("bytes")]
        [JsonRequired]
        public int Bytes { get; set; } = 0!;

        [JsonPropertyName("created_at")]
        [JsonRequired]
        public long CreatedAt { get; set; } = 0!;

        [JsonPropertyName("filename")]
        [JsonRequired]
        public string Filename { get; set; } = null!;

        [JsonPropertyName("object")]
        [JsonRequired]
        public string Object { get; set; } = null!;

        [JsonPropertyName("purpose")]
        [JsonRequired]
        public string Purpose { get; set; } = null!;

        [JsonPropertyName("status")]
        [JsonRequired]
        public string Status { get; set; } = null!;
    }
}

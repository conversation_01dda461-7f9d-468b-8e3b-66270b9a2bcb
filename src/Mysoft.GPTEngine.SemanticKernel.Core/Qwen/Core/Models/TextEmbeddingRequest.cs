using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models
{
    /// <summary>
    /// HTTP schema to perform embedding request.
    /// </summary>
    internal sealed class TextEmbeddingRequest
    {
        /// <summary>
        /// model
        /// </summary>
        [JsonPropertyName("model")]
        public string Model { get; set; }
        /// <summary>
        /// parameters
        /// </summary>
        [JsonPropertyName("input")]
        public EmbeddingInput Input { get; set; }
        /// <summary>
        /// parameters
        /// </summary>
        [JsonPropertyName("parameters")]
        public EmbeddingParameter Parameters { get; set; } = new EmbeddingParameter();
    }
    internal sealed class EmbeddingInput
    {
        /// <summary>
        /// Data to embed.
        /// </summary>
        [JsonPropertyName("texts")]
        public IList<string> Texts { get; set; } = new List<string>();
    }
    internal sealed class EmbeddingParameter
    {
        /// <summary>
        /// 取值：query或document，默认值为document。
        /// 说明：文本转换为向量后可以应用于检索、聚类、分类等下游任务，对于检索这类非对称任务，为了达到更好的检索效果，建议区分查询文本（query）和底库文本（document）类型，聚类、分类等对称任务可以不用特殊指定，
        /// </summary>
        [JsonPropertyName("text_type")]
        public string TextType { get; set; } = "document";
    }
}

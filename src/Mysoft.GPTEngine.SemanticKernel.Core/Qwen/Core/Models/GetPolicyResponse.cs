using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models
{
    /// <summary>
    /// Represents the response from the Qwen text embedding API.
    /// </summary>
    internal sealed class GetPolicyResponse
    {
        [JsonPropertyName("request_id")]
        [JsonRequired]
        public string RequestId { get; set; } = null!;

        [JsonPropertyName("data")]
        [JsonRequired]
        public UploadsData Data { get; set; } = null!;
    }
    internal sealed class UploadsData
    {
        [JsonPropertyName("policy")]
        [JsonRequired]
        public string Policy { get; set; } = null!;

        [JsonPropertyName("signature")]
        [JsonRequired]
        public string Signature { get; set; } = null!;

        [JsonPropertyName("upload_dir")]
        [JsonRequired]
        public string UploadDir { get; set; } = null!;

        [JsonPropertyName("upload_host")]
        [JsonRequired]
        public string UploadHost { get; set; } = null!;

        [JsonPropertyName("expire_in_seconds")]
        [JsonRequired]
        public int ExpireInSeconds { get; set; } = 0!;

        [JsonPropertyName("max_file_size_mb")]
        [JsonRequired]
        public int MaxFileSizeMb { get; set; } = 100!;

        [JsonPropertyName("capacity_limit_mb")]
        [JsonRequired]
        public int CapacityLimitMb { get; set; } = 100!;

        [JsonPropertyName("oss_access_key_id")]
        [JsonRequired]
        public string OssAccessKeyId { get; set; } = null!;

        [JsonPropertyName("x_oss_object_acl")]
        [JsonRequired]
        public string XOssObjectAcl { get; set; } = null!;

        [JsonPropertyName("x_oss_forbid_overwrite")]
        [JsonRequired]
        public string XOssForbidOverwrite { get; set; } = null!;
    }
}

// Copyright (c) Microsoft. All rights reserved.

using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core.Models;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Models;
using Mysoft.GPTEngine.SemanticKernel.Core.Text;
using static Mysoft.GPTEngine.SemanticKernel.Core.Dtos.LLMResponseDto;
using BinaryContent = Mysoft.GPTEngine.SemanticKernel.Core.Contents.BinaryContent;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Qwen.Core
{
    /// <summary>
    /// This class is responsible for making HTTP requests to the HuggingFace Inference API - Chat Completion Message API
    /// <see href="https://huggingface.co/docs/text-generation-inference/main/en/messages_api" />
    /// </summary>
    internal sealed class QwenMessageApiClient
    {
        public readonly QwenClient _clientCore;

        private static readonly string s_namespace = typeof(QwenMessageApiClient).Namespace!;

        /// <summary>
        /// Instance of <see cref="Meter"/> for metrics.
        /// </summary>
        private static readonly Meter s_meter = new Meter(s_namespace);

        /// <summary>
        /// Instance of <see cref="Counter{T}"/> to keep track of the number of prompt tokens used.
        /// </summary>
        private static readonly Counter<int> s_promptTokensCounter =
            s_meter.CreateCounter<int>(
                name: $"{s_namespace}.tokens.prompt",
                unit: "{token}",
                description: "Number of prompt tokens used");

        /// <summary>
        /// Instance of <see cref="Counter{T}"/> to keep track of the number of completion tokens used.
        /// </summary>
        private static readonly Counter<int> s_completionTokensCounter =
            s_meter.CreateCounter<int>(
                name: $"{s_namespace}.tokens.completion",
                unit: "{token}",
                description: "Number of completion tokens used");

        /// <summary>
        /// Instance of <see cref="Counter{T}"/> to keep track of the total number of tokens used.
        /// </summary>
        private static readonly Counter<int> s_totalTokensCounter =
            s_meter.CreateCounter<int>(
                name: $"{s_namespace}.tokens.total",
                unit: "{token}",
                description: "Number of total tokens used");

        internal QwenMessageApiClient(
            string modelId,
            HttpClient httpClient,
            Uri? endpoint = null,
            string? apiKey = null,
            ILogger? logger = null)
        {
            _clientCore = new QwenClient(
                modelId,
                httpClient,
                endpoint,
                apiKey,
                logger);
        }

        internal async IAsyncEnumerable<StreamingChatMessageContent> StreamCompleteChatMessageAsync(
          ChatHistory chatHistory,
          PromptExecutionSettings? executionSettings,
          [EnumeratorCancellation] CancellationToken cancellationToken)
        {
            await _clientCore.UploadsAsync(chatHistory, cancellationToken);

            string modelId = executionSettings?.ModelId ?? _clientCore.ModelId;
            var endpoint = chatHistory.Any(x => x.Items.Any(s=>s is ImageContent)) ? GetMultimodalChatGenerationEndpoint() : chatHistory.Any(x => x.Items.Any(s => s is BinaryContent)) ? GetFileChatGenerationEndpoint() :GetChatGenerationEndpoint();
            var request = CreateChatRequest(chatHistory, executionSettings);
            request.Parameters.IncrementalOutput = true;
            request.Model = modelId;

            using var httpRequestMessage = _clientCore.CreatePost(request, endpoint, _clientCore.ApiKey);

            httpRequestMessage.Headers.Add("X-DashScope-SSE", "enable");


            using var response = await _clientCore.SendRequestAndGetResponseImmediatelyAfterHeadersReadAsync(httpRequestMessage, modelId, cancellationToken)
                .ConfigureAwait(false);

            using var responseStream = await response.Content.ReadAsStreamAndTranslateExceptionAsync()
                .ConfigureAwait(false);

            if (chatHistory.Any(i=>i.Items.Any(s=>s is ImageContent)))
            {
                await foreach (var streamingChatContent in this.ProcessMultimodaChatResponseStreamAsync(responseStream, modelId, cancellationToken).ConfigureAwait(false))
                {
                    yield return streamingChatContent;
                }
            }
            else if (chatHistory.Any(i => i.Items.Any(s => s is BinaryContent)))
            {
                await foreach (var streamingChatContent in this.ProcessFilemodaChatResponseStreamAsync(responseStream, modelId, cancellationToken).ConfigureAwait(false))
                {
                    yield return streamingChatContent;
                }
            }
            else
            {
                await foreach (var streamingChatContent in this.ProcessChatResponseStreamAsync(responseStream, modelId, cancellationToken).ConfigureAwait(false))
                {
                    yield return streamingChatContent;
                }
            }
        }

        internal async Task<IReadOnlyList<ChatMessageContent>> CompleteChatMessageAsync(
            ChatHistory chatHistory,
            PromptExecutionSettings? executionSettings,
            CancellationToken cancellationToken)
        {
            await _clientCore.UploadsAsync(chatHistory, cancellationToken);

            string modelId = executionSettings?.ModelId ?? _clientCore.ModelId;
            var endpoint = GetChatGenerationEndpoint();
            var request = CreateChatRequest(chatHistory, executionSettings);

            using var httpRequestMessage = _clientCore.CreatePost(request, endpoint, _clientCore.ApiKey);

            string body = await _clientCore.SendRequestAndGetStringBodyAsync(httpRequestMessage, cancellationToken)
                .ConfigureAwait(false);

            var response = QwenClient.DeserializeResponse<ChatCompletionResponse>(body);
            var chatContents = GetChatMessageContentsFromResponse(response, modelId);

            LogChatCompletionUsage(executionSettings, response);

            return chatContents;
        }

        private void LogChatCompletionUsage(PromptExecutionSettings? executionSettings, ChatCompletionResponse chatCompletionResponse)
        {
            if (_clientCore.Logger.IsEnabled(LogLevel.Debug))
            {
                _clientCore.Logger.Log(
                LogLevel.Debug,
                "Qwen chat completion usage -  Prompt tokens: {PromptTokens}, Completion tokens: {CompletionTokens}, Total tokens: {TotalTokens}",
                chatCompletionResponse.Usage!.InputTokens,
                chatCompletionResponse.Usage!.OutputTokens,
                chatCompletionResponse.Usage!.TotalTokens);
            }

            s_promptTokensCounter.Add(chatCompletionResponse.Usage!.InputTokens);
            s_completionTokensCounter.Add(chatCompletionResponse.Usage!.OutputTokens);
            s_totalTokensCounter.Add(chatCompletionResponse.Usage!.TotalTokens);
        }

        public static List<ChatMessageContent> GetChatMessageContentsFromResponse(ChatCompletionResponse response, string modelId)
        {
            var chatMessageContents = new List<ChatMessageContent>();
            foreach (var choice in response.Output.Choices!)
            {
                var metadata = new QwenChatCompletionMetadata
                {
                    Created = response.Output.Created,
                    FinishReason = choice.FinishReason,
                    UsageCompletionTokens = response.Usage?.OutputTokens,
                    UsagePromptTokens = response.Usage?.InputTokens,
                    UsageTotalTokens = response.Usage?.TotalTokens,
                };

                chatMessageContents.Add(new ChatMessageContent(
                    role: new AuthorRole(choice.Message?.Role ?? AuthorRole.Assistant.ToString()),
                    content: choice.Message?.Content,
                    modelId: modelId,
                    innerContent: response,
                    encoding: Encoding.UTF8,
                    metadata: metadata));
            }

            return chatMessageContents;
        }

        public static StreamingChatMessageContent GetStreamingChatMessageContentFromStreamResponse(ChatCompletionResponse response, string modelId)
        {
            var choice = response?.Output?.Choices?.FirstOrDefault();
            if (response != null && choice != null && choice.Message != null)
            {
                var metadata = new QwenChatCompletionMetadata
                {
                    Created = response.Output.Created,
                    FinishReason = choice.FinishReason,
                    UsageCompletionTokens = response.Usage?.OutputTokens,
                    UsagePromptTokens = response.Usage?.InputTokens,
                    UsageTotalTokens = response.Usage?.TotalTokens,
                };

                var streamChat = new StreamingChatMessageContent(
                    new AuthorRole(choice.Message.Role),
                    choice.Message.Content,
                    response.Output,
                    0,
                    modelId,
                    Encoding.UTF8,
                    metadata);

                return streamChat;
            }

            throw new KernelException("Unexpected response from model")
            {
                Data = { { "ResponseData", response } },
            };
        }
        public static StreamingChatMessageContent GetStreamingChatMessageContentFromStreamResponse(MultimodalChatCompletionResponse response, string modelId)
        {
            var choice = response?.Output?.Choices?.FirstOrDefault();
            if (response != null && choice != null && choice.Message != null)
            {
                var metadata = new QwenChatCompletionMetadata
                {
                    Created = response.Output.Created,
                    FinishReason = choice.FinishReason,
                    UsageCompletionTokens = response.Usage?.OutputTokens,
                    UsagePromptTokens = response.Usage?.InputTokens,
                    UsageTotalTokens = response.Usage?.TotalTokens,
                };

                var streamChat = new StreamingChatMessageContent(
                    new AuthorRole(choice.Message.Role),
                    choice.Message.Contents.FirstOrDefault()?.Text,
                    response.Output,
                    0,
                    modelId,
                    Encoding.UTF8,
                    metadata);

                return streamChat;
            }

            throw new KernelException("Unexpected response from model")
            {
                Data = { { "ResponseData", response } },
            };
        }

        public static StreamingChatMessageContent GetStreamingChatMessageContentFromStreamResponse(FileChatCompletionChunkResponse response, string modelId)
        {
            var choice = response?.Choices?.FirstOrDefault();
            if (response != null)
            {
                var metadata = new QwenChatCompletionMetadata
                {
                    Created = response.Created,
                    FinishReason = choice?.FinishReason,
                    UsageCompletionTokens = response.Usage?.CompletionTokens,
                    UsagePromptTokens = response.Usage?.PromptTokens,
                    UsageTotalTokens = response.Usage?.TotalTokens,
                };

                var streamChat = new StreamingChatMessageContent(
                    new AuthorRole(choice?.Delta?.Role ?? AuthorRole.Assistant.ToString()),
                    choice?.Delta?.Content,
                    response.Choices,
                    0,
                    modelId,
                    Encoding.UTF8,
                    metadata);

                return streamChat;
            }

            throw new KernelException("Unexpected response from model")
            {
                Data = { { "ResponseData", response } },
            };
        }
        private async IAsyncEnumerable<StreamingChatMessageContent> ProcessChatResponseStreamAsync(Stream stream, string modelId, [EnumeratorCancellation] CancellationToken cancellationToken)
        {
            await foreach (var content in ParseChatResponseStreamAsync(stream, cancellationToken).ConfigureAwait(false))
            {
                yield return GetStreamingChatMessageContentFromStreamResponse(content, modelId);
            }
        }
        private async IAsyncEnumerable<StreamingChatMessageContent> ProcessMultimodaChatResponseStreamAsync(Stream stream, string modelId, [EnumeratorCancellation] CancellationToken cancellationToken)
        {
            await foreach (var content in ParseMultimodalChatResponseStreamAsync(stream, cancellationToken).ConfigureAwait(false))
            {
                yield return GetStreamingChatMessageContentFromStreamResponse(content, modelId);
            }
        }

        private async IAsyncEnumerable<StreamingChatMessageContent> ProcessFilemodaChatResponseStreamAsync(Stream stream, string modelId, [EnumeratorCancellation] CancellationToken cancellationToken)
        {
            await foreach (var content in ParseFilemodalChatResponseStreamAsync(stream, cancellationToken).ConfigureAwait(false))
            {
                yield return GetStreamingChatMessageContentFromStreamResponse(content, modelId);
            }
        }
        public ChatCompletionRequest CreateChatRequest(
            ChatHistory chatHistory,
            PromptExecutionSettings? promptExecutionSettings)
        {
            var executionSettings = QwenPromptExecutionSettings.FromExecutionSettings(promptExecutionSettings);
            executionSettings.ModelId ??= _clientCore.ModelId;

            QwenClient.ValidateMaxTokens(executionSettings.MaxTokens);
            var request = ChatCompletionRequest.FromChatHistoryAndExecutionSettings(chatHistory, executionSettings, null);
            request.Parameters.IncrementalOutput = false;
            return request;
        }

        static SseData? DeserializeTargetType<T>(SseLine sseLine)
        {
            if (sseLine.EventName == "error")
            {
                var errorStatus = ErrorCodeInitHelper.ErrorStatusInit(sseLine.FieldValue.ToString());
                if (!string.IsNullOrEmpty(errorStatus))
                {
                    throw new LLmCustomException(errorStatus, sseLine.FieldValue.ToString());
                }
                else
                {
                    JsonUtility.TryDeserializeObject(sseLine.FieldValue.ToString(), out QwenDto qwenDto);
                    string errMsg = string.Empty;
                    if (qwenDto != null)
                    {
                        errMsg = string.Format(ErrMsgConst.LLM_ErrMsgQwenSse, qwenDto.code, qwenDto.message);
                    }
                    else
                    {
                        errMsg = string.Format(ErrMsgConst.LLM_ErrMsgOthersSse, sseLine.FieldValue.ToString());
                    }
                    throw new LLmCustomException("LLM", errMsg, (int)ModelInstanceTypeEnum.AliTextGeneration);
                }
            }
            var obj = JsonSerializer.Deserialize<T>(sseLine.FieldValue.Span, JsonOptionsCache.ReadPermissive);
            return new SseData(sseLine.EventName, obj!);
        }

        public static IAsyncEnumerable<ChatCompletionResponse> ParseChatResponseStreamAsync(Stream responseStream, CancellationToken cancellationToken)
            => SseJsonParser.ParseAsync<ChatCompletionResponse>(responseStream, cancellationToken, DeserializeTargetType<ChatCompletionResponse>);

        public static IAsyncEnumerable<MultimodalChatCompletionResponse> ParseMultimodalChatResponseStreamAsync(Stream responseStream, CancellationToken cancellationToken)
    => SseJsonParser.ParseAsync<MultimodalChatCompletionResponse>(responseStream, cancellationToken, DeserializeTargetType<MultimodalChatCompletionResponse>);

        public static IAsyncEnumerable<FileChatCompletionChunkResponse> ParseFilemodalChatResponseStreamAsync(Stream responseStream, CancellationToken cancellationToken)
            => SseJsonParser.ParseAsync<FileChatCompletionChunkResponse>(responseStream, cancellationToken, DeserializeTargetType<FileChatCompletionChunkResponse>); 

        private Uri GetChatGenerationEndpoint()
            => new Uri($"{_clientCore.Endpoint}{_clientCore.Separator}api/v1/services/aigc/text-generation/generation");
        private Uri GetMultimodalChatGenerationEndpoint()
    => new Uri($"{_clientCore.Endpoint}{_clientCore.Separator}api/v1/services/aigc/multimodal-generation/generation");
        private Uri GetFileChatGenerationEndpoint()=> new Uri($"{_clientCore.Endpoint}{_clientCore.Separator}compatible-mode/v1/chat/completions");
    }
}

// Copyright (c) Microsoft. All rights reserved.

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using Microsoft.SemanticKernel;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics
{
    [ExcludeFromCodeCoverage]
    public static class Verify
    {
        private static readonly Regex s_asciiLettersDigitsUnderscoresRegex = new Regex("^[0-9A-Za-z_]*$");
        private static readonly Regex s_filenameRegex = new Regex("^[^.]+\\.[^.]+$");

        /// <summary>
        /// Equivalent of ArgumentNullException.ThrowIfNull
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void NotNull([NotNull] object? obj, [CallerArgumentExpression("obj")] string? paramName = null)
        {
            if (obj is null)
            {
                ThrowArgumentNullException(paramName);
            }
        }
        /// <summary>
        /// Equivalent of ArgumentNullException.ThrowIfNull
        /// </summary>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void NotNull([NotNull] Guid? obj, [CallerArgumentExpression("obj")] string? paramName = null)
        {
            if (obj is null || obj == Guid.Empty)
            {
                ThrowArgumentNullException(paramName);
            }
        }
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void NotNullOrWhiteSpace([NotNull] string? str, [CallerArgumentExpression("str")] string? paramName = null)
        {
            NotNull(str, paramName);
            if (string.IsNullOrWhiteSpace(str))
            {
                ThrowArgumentWhiteSpaceException(paramName);
            }
        }

        public static void NotNullOrEmpty<T>(IList<T> list, [CallerArgumentExpression("list")] string? paramName = null)
        {
            NotNull(list, paramName);
            if (list.Count == 0)
            {
                throw new ArgumentException("The value cannot be empty.", paramName);
            }
        }

        public static void True(bool condition, string message, [CallerArgumentExpression("condition")] string? paramName = null)
        {
            if (!condition)
            {
                throw new ArgumentException(message, paramName);
            }
        }

        public static void ValidPluginName([NotNull] string? pluginName, IReadOnlyKernelPluginCollection? plugins = null, [CallerArgumentExpression("pluginName")] string? paramName = null)
        {
            NotNullOrWhiteSpace(pluginName);
            if (!s_asciiLettersDigitsUnderscoresRegex.IsMatch(pluginName))
            {
                ThrowArgumentInvalidName("plugin name", pluginName, paramName);
            }

            if (plugins != null && plugins.Contains(pluginName))
            {
                throw new ArgumentException($"A plugin with the name '{pluginName}' already exists.");
            }
        }

        public static void ValidFunctionName([NotNull] string? functionName, [CallerArgumentExpression("functionName")] string? paramName = null)
        {
            NotNullOrWhiteSpace(functionName);
            if (!s_asciiLettersDigitsUnderscoresRegex.IsMatch(functionName))
            {
                ThrowArgumentInvalidName("function name", functionName, paramName);
            }
        }

        public static void ValidFilename([NotNull] string? filename, [CallerArgumentExpression("filename")] string? paramName = null)
        {
            NotNullOrWhiteSpace(filename);
            if (!s_filenameRegex.IsMatch(filename))
            {
                throw new ArgumentException($"Invalid filename format: '{filename}'. Filename should consist of an actual name and a file extension.", paramName);
            }
        }

        public static void ValidateUrl(string url, bool allowQuery = false, [CallerArgumentExpression("url")] string? paramName = null)
        {
            NotNullOrWhiteSpace(url, paramName);

            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri) || string.IsNullOrEmpty(uri.Host))
            {
                throw new ArgumentException($"The `{url}` is not valid.", paramName);
            }

            if (!allowQuery && !string.IsNullOrEmpty(uri.Query))
            {
                throw new ArgumentException($"The `{url}` is not valid: it cannot contain query parameters.", paramName);
            }

            if (!string.IsNullOrEmpty(uri.Fragment))
            {
                throw new ArgumentException($"The `{url}` is not valid: it cannot contain URL fragments.", paramName);
            }
        }

        public static void StartsWith([NotNull] string? text, string prefix, string message, [CallerArgumentExpression("text")] string? textParamName = null)
        {
            Debug.Assert(prefix != null);

            NotNullOrWhiteSpace(text, textParamName);
            if (!text.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            {
                throw new ArgumentException(textParamName, message);
            }
        }

        public static void DirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                throw new DirectoryNotFoundException($"Directory '{path}' could not be found.");
            }
        }

        /// <summary>
        /// Make sure every function parameter name is unique
        /// </summary>
        /// <param name="parameters">List of parameters</param>
        public static void ParametersUniqueness(IReadOnlyList<KernelParameterMetadata> parameters)
        {
            int count = parameters.Count;
            if (count > 0)
            {
                var seen = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                for (int i = 0; i < count; i++)
                {
                    KernelParameterMetadata p = parameters[i];
                    if (string.IsNullOrWhiteSpace(p.Name))
                    {
                        string paramName = $"{nameof(parameters)}[{i}].{p.Name}";
                        if (p.Name is null)
                        {
                            ThrowArgumentNullException(paramName);
                        }
                        else
                        {
                            ThrowArgumentWhiteSpaceException(paramName);
                        }
                    }

                    if (!seen.Add(p.Name))
                    {
                        throw new ArgumentException($"The function has two or more parameters with the same name '{p.Name}'");
                    }
                }
            }
        }

        [DoesNotReturn]
        private static void ThrowArgumentInvalidName(string kind, string name, string? paramName) =>
            throw new ArgumentException($"A {kind} can contain only ASCII letters, digits, and underscores: '{name}' is not a valid name.", paramName);

        [DoesNotReturn]
        internal static void ThrowArgumentNullException(string? paramName) =>
            throw new ArgumentNullException(paramName);

        [DoesNotReturn]
        internal static void ThrowArgumentWhiteSpaceException(string? paramName) =>
            throw new ArgumentException("The value cannot be an empty string or composed entirely of whitespace.", paramName);

        [DoesNotReturn]
        internal static void ThrowArgumentOutOfRangeException<T>(string? paramName, T actualValue, string message) =>
            throw new ArgumentOutOfRangeException(paramName, actualValue, message);
    }
}

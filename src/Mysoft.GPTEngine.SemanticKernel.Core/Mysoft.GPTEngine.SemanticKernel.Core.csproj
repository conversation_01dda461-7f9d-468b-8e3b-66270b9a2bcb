<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <NoWarn>$(NoWarn);SKEXP0001,SKEXP0060,CS0103</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AlibabaCloud.SDK.Green20220302" Version="2.19.2" />
    <PackageReference Include="AlibabaCloud.SDK.Ocr-api20210707" Version="3.1.1" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Baidu.AI" Version="4.15.15" />
    <PackageReference Include="JsonSchema.Net" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.OpenApi" Version="1.6.22" />
    <PackageReference Include="Microsoft.OpenApi.ApiManifest" Version="0.5.6-preview" />
    <PackageReference Include="Microsoft.OpenApi.Readers" Version="1.6.22" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.60.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.Milvus" Version="1.60.0-alpha" />
    <PackageReference Include="RestSharp" Version="106.15.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Ocr\Aliyun\Models\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Mysoft.GPTEngine.Common\Mysoft.GPTEngine.Common.csproj" />
  </ItemGroup>

</Project>

using System.IO;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr
{
    /// <summary>
    /// HTTP schema to perform request.
    /// </summary>
    public sealed class OcrRequest
    {
        public OcrRequest() { 
            this.memoryStream = new MemoryStream();
        }
        /// <summary>
        /// 图片二进制文件
        /// </summary>
        [JsonPropertyName("body")]
        public byte[] Body { get; set; } = new byte[0];

        public MemoryStream memoryStream { get; set; }

        public string ocrCode { get; set; }
    }
}

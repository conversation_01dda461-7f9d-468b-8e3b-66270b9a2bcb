using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr
{
    /// <summary>
    /// Represents the response from the Qwen text embedding API.
    /// </summary>
    public sealed class OcrResponse
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("content")]
        public string Content { get; set; } = null!;

        [JsonPropertyName("words_result_num")]
        public int WordsResultNum { get; set; } = 0;

        [JsonPropertyName("words_result")]
        public List<WordsResult> WordsResult { get; set; } = null!;
    }
    public sealed class WordsResult
    {
        [JsonPropertyName("prob")]
        public double Prob { get; set; } = 0;

        [JsonPropertyName("location")]
        public Location Location { get; set; } = null!;

        [JsonPropertyName("words")]
        public string Words { get; set; } = null!;
    }
    public sealed class Location
    {
        [JsonPropertyName("top")]
        public double Top { get; set; } = 0;

        [JsonPropertyName("left")]
        public double Left { get; set; } = 0;

        [JsonPropertyName("width")]
        public double Width { get; set; } = 0;

        [JsonPropertyName("height")]
        public double Height { get; set; } = 0;
    }
}

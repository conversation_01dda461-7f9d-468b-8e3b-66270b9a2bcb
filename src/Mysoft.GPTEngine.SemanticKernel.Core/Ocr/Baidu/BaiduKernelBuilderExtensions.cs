// Copyright (c) Microsoft. All rights reserved.

using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu.Services;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu
{
    /// <summary>
    /// Provides extension methods for the <see cref="IKernelBuilder"/> class to configure Hugging Face connectors.
    /// </summary>
    public static class BaiduKernelBuilderExtensions
    {
        public static IKernelBuilder AddBaiduOcrRecognize(
            this IKernelBuilder builder,
            string model,
            string endpoint,
            string accessKeyId,
            string accessSecret,
            string serviceId)
        {
            Verify.NotNull(builder);
            Verify.NotNull(endpoint);

            builder.Services.AddKeyedSingleton<IOcrRecognizeService>(serviceId, (serviceProvider, _) =>
            {
                switch (model)
                {
                    case OcrRecognizeConst.Baidu_RecognizeHandwriting:
                        return new RecognizeHandwritingService(endpoint, accessKeyId, accessSecret);
                    case OcrRecognizeConst.Baidu_RecognizeInvoice:
                        return new RecognizeInvoiceService(endpoint, accessKeyId, accessSecret);
                    default:
                        return new RecognizeAccurateService(endpoint, accessKeyId, accessSecret);
                }
            });
            return builder;
        }
    }
}

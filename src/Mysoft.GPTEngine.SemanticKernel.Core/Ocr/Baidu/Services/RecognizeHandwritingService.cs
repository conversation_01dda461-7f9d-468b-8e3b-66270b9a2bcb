using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Newtonsoft.Json.Linq;
using Tea;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu.Services
{
    /// <summary>
    /// 百度-通用手写体识别
    /// </summary>
    public class RecognizeHandwritingService : RecognizeBaseService, IOcrRecognizeService
    {
        public RecognizeHandwritingService(string endpoint, string accessKeyId, string accessSecret) : base(endpoint, accessKeyId, accessSecret) { }

        public async Task<OcrResponse> Execute(OcrRequest body)
        {
            OcrResponse ocrResponse = new OcrResponse();
            try
            {
                // 如果有可选参数
                var options = new Dictionary<string, object>{
                    {"probability", "true"},         //每行置信度
                    {"detect_direction", "true"}         //是否检测图像朝向,会自动转换为正
                };
                byte[] bytes = body.memoryStream != null && body.memoryStream.Length > 0 ? body.memoryStream.ToArray() : body.Body;
                var response = _client.Handwriting(bytes, options);

                ProcessOcrRequest(response, ocrResponse, body.ocrCode);
            }
            catch (TeaException error)
            {
                ocrResponse.Content = error.Message;
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
            }
            return await Task.FromResult(ocrResponse);
        }

        public void ProcessOcrRequest(JObject response, OcrResponse ocrResponse,string ocrCode)
        {
            BaiduOcrResult handwritingResult = response.ToObject<BaiduOcrResult>();
            ocrResponse.Success = string.IsNullOrEmpty(handwritingResult?.Error_Msg) == true;
            if (!ocrResponse.Success)
            {
                ThrowOcrException((int)handwritingResult.Error_Code, handwritingResult.Error_Msg, ocrCode, ModelInstanceTypeEnum.BaiduOCR);
            }

            if (ocrResponse.Success && handwritingResult?.Words_Result?.Count() > 0)
            {
                ocrResponse.Content = string.Join(" ", handwritingResult.Words_Result.Select(x => x.Words).ToList());
                ocrResponse.WordsResult = handwritingResult.Words_Result.Select(x => new WordsResult
                {
                    Prob = x.Probability.Average * 100,
                    Words = x.Words,
                    Location = new Ocr.Location
                    {
                        Left = x.Location.Left,
                        Top = x.Location.Top,
                        Height = x.Location.Height,
                        Width = x.Location.Width
                    }
                }).ToList();
                ocrResponse.WordsResultNum = handwritingResult.Words_Result_Num;
            }
        }
    }
}

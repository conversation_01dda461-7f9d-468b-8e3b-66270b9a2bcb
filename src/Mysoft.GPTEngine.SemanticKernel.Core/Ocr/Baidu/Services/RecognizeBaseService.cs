using System.Collections.Generic;
using System.Text.Json.Serialization;
using BaiduOcr = Baidu.Aip.Ocr;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu.Services
{
    /// <summary>
    /// OCR识别基类
    /// </summary>
    public class RecognizeBaseService: OcrBaseService
    {
        public readonly BaiduOcr.Ocr _client;
        public RecognizeBaseService(string endpoint, string accessKeyId, string accessSecret)
        {
            _client = new BaiduOcr.Ocr(accessKeyId, accessSecret)
            {
                Timeout = 60000
            };
        }
    }
    public class BaiduOcrResult
    {
        [JsonPropertyName("words_result")]
        public List<Words_Result> Words_Result { get; set; }

        [JsonPropertyName("words_result_num")]
        public int Words_Result_Num { get; set; } = 0;

        [JsonPropertyName("log_id")]
        public string Log_Id { get; set; }

        [JsonPropertyName("error_msg")]
        public string Error_Msg { get; set; }

        [JsonPropertyName("error_code")]
        public long Error_Code { get; set; }
    }

    public class Words_Result
    {
        [JsonPropertyName("words")]
        public string Words { get; set; }

        [JsonPropertyName("probability")]
        public Probability Probability { get; set; }

        [JsonPropertyName("location")]
        public Location Location { get; set; }
    }


    public class Probability
    {
        /// <summary>
        /// 行置信度平均值
        /// </summary>
        [JsonPropertyName("average")]
        public double Average { get; set; }

        /// <summary>
        /// 行置信度最小值
        /// </summary>
        [JsonPropertyName("min")]
        public double Min { get; set; }

        /// <summary>
        /// 行置信度方差
        /// </summary>
        [JsonPropertyName("variance")]
        public double Variance { get; set; }
    }

    public class Location
    {
        [JsonPropertyName("top")]
        public int Top { get; set; } = 0;

        [JsonPropertyName("left")]
        public int Left { get; set; } = 0;

        [JsonPropertyName("width")]
        public int Width { get; set; } = 0;

        [JsonPropertyName("height")]
        public int Height { get; set; } = 0;
    }
}

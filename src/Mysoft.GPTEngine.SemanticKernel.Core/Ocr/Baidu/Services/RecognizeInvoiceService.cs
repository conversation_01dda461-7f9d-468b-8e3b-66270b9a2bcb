using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Tea;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Baidu.Services
{
    /// <summary>
    /// 百度-增值税发票识别
    /// </summary>
    public class RecognizeInvoiceService : RecognizeBaseService, IOcrRecognizeService
    {
        public RecognizeInvoiceService(string endpoint, string accessKeyId, string accessSecret) 
            : base(endpoint, accessKeyId, accessSecret) { }

        public async Task<OcrResponse> Execute(OcrRequest body)
        {
            OcrResponse ocrResponse = new OcrResponse();
            try
            {
                // 调用参数
                var options = new Dictionary<string, object>{
                    {"type", "normal"}, // 发票类型，默认normal
                };
                
                byte[] bytes = body.memoryStream != null && body.memoryStream.Length > 0 
                    ? body.memoryStream.ToArray() 
                    : body.Body;
                    
                var response = _client.VatInvoice(bytes, options);

                ProcessOcrRequest(response, ocrResponse, body.ocrCode);
            }
            catch (TeaException error)
            {
                ocrResponse.Content = error.Message;
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
                ThrowOcrException(error.StatusCode, error.Message,body.ocrCode, ModelInstanceTypeEnum.BaiduOCR);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                ocrResponse.Content = _error.Message;
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
                ThrowOcrException(error.StatusCode, error.Message,body.ocrCode, ModelInstanceTypeEnum.BaiduOCR);
            }
            return await Task.FromResult(ocrResponse);
        }

        private void ProcessOcrRequest(JObject response, OcrResponse ocrResponse, string ocrCode)
        {
            var invoiceResult = response.ToObject<BaiduInvoiceResult>();
            ocrResponse.Success = string.IsNullOrEmpty(invoiceResult?.Error_Msg);
            
            if (!ocrResponse.Success)
            {
                ThrowOcrException((int)invoiceResult.Error_Code, invoiceResult.Error_Msg, ocrCode, ModelInstanceTypeEnum.BaiduOCR);
            }

            if (ocrResponse.Success && invoiceResult?.Words_Result != null)
            {
                // 将识别结果转换为统一格式
                ocrResponse.Content = JsonConvert.SerializeObject(invoiceResult.Words_Result);
                ocrResponse.WordsResult = new List<WordsResult>();
                
                // 将发票字段转换为WordsResult格式
                foreach (var field in GetInvoiceFields(invoiceResult.Words_Result))
                {
                    ocrResponse.WordsResult.Add(new WordsResult 
                    {
                        Words = field,
                        Prob = 100, // 百度发票识别API没有返回置信度
                        Location = null
                    });
                }
                
                ocrResponse.WordsResultNum = ocrResponse.WordsResult.Count;
            }
        }

        private IEnumerable<string> GetInvoiceFields(InvoiceWordsResult result)
        {
            if (result == null) yield break;

            // 基础信息
            if (!string.IsNullOrEmpty(result.InvoiceType))
                yield return result.InvoiceType;
            if (!string.IsNullOrEmpty(result.InvoiceCode))
                yield return result.InvoiceCode; 
            if (!string.IsNullOrEmpty(result.InvoiceNum))
                yield return result.InvoiceNum;
            if (!string.IsNullOrEmpty(result.InvoiceDate))
                yield return result.InvoiceDate;

            // 购买方信息
            if (!string.IsNullOrEmpty(result.PurchaserName))
                yield return result.PurchaserName;
            if (!string.IsNullOrEmpty(result.PurchaserRegisterNum))
                yield return result.PurchaserRegisterNum;
            if (!string.IsNullOrEmpty(result.PurchaserAddress))
                yield return result.PurchaserAddress;
            if (!string.IsNullOrEmpty(result.PurchaserBank))
                yield return result.PurchaserBank;

            // 销售方信息
            if (!string.IsNullOrEmpty(result.SellerName))
                yield return result.SellerName;
            if (!string.IsNullOrEmpty(result.SellerRegisterNum))
                yield return result.SellerRegisterNum;
            if (!string.IsNullOrEmpty(result.SellerAddress))
                yield return result.SellerAddress;
            if (!string.IsNullOrEmpty(result.SellerBank))
                yield return result.SellerBank;

            // 金额信息
            if (!string.IsNullOrEmpty(result.TotalAmount))
                yield return result.TotalAmount;
            if (!string.IsNullOrEmpty(result.TotalTax))
                yield return result.TotalTax;
            if (!string.IsNullOrEmpty(result.AmountInWords))
                yield return result.AmountInWords;
            if (!string.IsNullOrEmpty(result.AmountInFiguers))
                yield return result.AmountInFiguers;

            // 商品信息
            if (result.CommodityName?.Any() == true)
            {
                for (int i = 0; i < result.CommodityName.Count; i++)
                {
                    var commodity = new List<string>();
                    if (result.CommodityName?.Count > i)
                        commodity.Add(result.CommodityName[i].Word);
                    if (result.CommodityType?.Count > i)
                        commodity.Add(result.CommodityType[i].Word);
                    if (result.CommodityUnit?.Count > i)
                        commodity.Add(result.CommodityUnit[i].Word);
                    if (result.CommodityNum?.Count > i)
                        commodity.Add(result.CommodityNum[i].Word);
                    if (result.CommodityPrice?.Count > i)
                        commodity.Add(result.CommodityPrice[i].Word);
                    if (result.CommodityAmount?.Count > i)
                        commodity.Add(result.CommodityAmount[i].Word);
                    if (result.CommodityTaxRate?.Count > i)
                        commodity.Add(result.CommodityTaxRate[i].Word);
                    if (result.CommodityTax?.Count > i)
                        commodity.Add(result.CommodityTax[i].Word);
                    yield return string.Join(", ", commodity);
                }
            }
        }
    }

    public class BaiduInvoiceResult : BaiduOcrResult
    {
        [JsonPropertyName("words_result")]
        public new InvoiceWordsResult Words_Result { get; set; }
    }

    public class InvoiceWordsResult
    {
        [JsonPropertyName("InvoiceType")]
        public string InvoiceType { get; set; }

        [JsonPropertyName("InvoiceCode")]
        public string InvoiceCode { get; set; }

        [JsonPropertyName("InvoiceNum")]
        public string InvoiceNum { get; set; }

        [JsonPropertyName("InvoiceDate")]
        public string InvoiceDate { get; set; }

        [JsonPropertyName("PurchaserName")]
        public string PurchaserName { get; set; }

        [JsonPropertyName("PurchaserRegisterNum")]
        public string PurchaserRegisterNum { get; set; }

        [JsonPropertyName("PurchaserAddress")]
        public string PurchaserAddress { get; set; }

        [JsonPropertyName("PurchaserBank")]
        public string PurchaserBank { get; set; }

        [JsonPropertyName("SellerName")]
        public string SellerName { get; set; }

        [JsonPropertyName("SellerRegisterNum")]
        public string SellerRegisterNum { get; set; }

        [JsonPropertyName("SellerAddress")]
        public string SellerAddress { get; set; }

        [JsonPropertyName("SellerBank")]
        public string SellerBank { get; set; }

        [JsonPropertyName("TotalAmount")]
        public string TotalAmount { get; set; }

        [JsonPropertyName("TotalTax")]
        public string TotalTax { get; set; }

        [JsonPropertyName("AmountInWords")]
        public string AmountInWords { get; set; }

        [JsonPropertyName("AmountInFiguers")]
        public string AmountInFiguers { get; set; }

        [JsonPropertyName("CommodityName")]
        public List<CommodityInfo> CommodityName { get; set; }

        [JsonPropertyName("CommodityType")]
        public List<CommodityInfo> CommodityType { get; set; }

        [JsonPropertyName("CommodityUnit")]
        public List<CommodityInfo> CommodityUnit { get; set; }

        [JsonPropertyName("CommodityNum")]
        public List<CommodityInfo> CommodityNum { get; set; }

        [JsonPropertyName("CommodityPrice")]
        public List<CommodityInfo> CommodityPrice { get; set; }

        [JsonPropertyName("CommodityAmount")]
        public List<CommodityInfo> CommodityAmount { get; set; }

        [JsonPropertyName("CommodityTaxRate")]
        public List<CommodityInfo> CommodityTaxRate { get; set; }

        [JsonPropertyName("CommodityTax")]
        public List<CommodityInfo> CommodityTax { get; set; }
    }

    public class CommodityInfo
    {
        [JsonPropertyName("row")]
        public string Row { get; set; }

        [JsonPropertyName("word")]
        public string Word { get; set; }
    }
} 
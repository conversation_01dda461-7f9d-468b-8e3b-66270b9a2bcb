using System;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Aliyun.Services
{
    /// <summary>
    /// 多模态vl-ocr
    /// </summary>
    public class RecognizeVlService :IOcrRecognizeService
    {
        public RecognizeVlService(string endpoint, string modelCode, string accessSecret)
        {
            _apiKey = accessSecret;
            _apiUrl = endpoint + "/compatible-mode/v1/chat/completions";
            _modelCode = modelCode;
        }

        private static readonly HttpClient HttpClient = new HttpClient();
        private string _modelCode;
        private string _apiKey;
        private string _apiUrl;
        public async Task<OcrResponse> Execute(OcrRequest body)
        {
            using MemoryStream stream = body.memoryStream != null && body.memoryStream.Length > 0 ? body.memoryStream : new MemoryStream(body.Body);
            Console.WriteLine($"vl-ocr开始时间：{DateTime.Now}, 模型模型编码：{_modelCode}");
            var requestBody = new
            {
                model = _modelCode,
                messages = new[]
                {
                    new
                    {
                        role = "user",
                        content = new object[]
                        {
                            new { type = "image_url", image_url = new { url = $"data:image/webp;base64,{Convert.ToBase64String(stream.ToArray())}"} }
                        }
                    }
                }
            };
        
            var jsonRequest = JsonConvert.SerializeObject(requestBody);
            var requestContent = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

            OcrResponse ocrResponse = new OcrResponse();
            try
            {
                HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _apiKey);
                Console.WriteLine($"vl-ocr开始时间：{DateTime.Now}");
                var response = await HttpClient.PostAsync(_apiUrl, requestContent);
                Console.WriteLine($"vl-ocr结束时间：{DateTime.Now}");
                response.EnsureSuccessStatusCode();
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<dynamic>(jsonResponse);
                ocrResponse.Success = true;
                string content = result.choices[0].message.content;
                ocrResponse.Content = content.Replace("\n", " ");
            }
            catch (Exception e)
            {
                ocrResponse.Content = e.Message;
                Console.WriteLine(e);
            }
            return await Task.FromResult(ocrResponse); 
        }
    }
}

using System.Collections.Generic;
using System.Text.Json.Serialization;
using AlibabaCloud.OpenApiClient.Models;
using AlibabaCloud.SDK.Ocr_api20210707;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Aliyun.Services
{
    /// <summary>
    /// OCR识别基类
    /// </summary>
    public class RecognizeBaseService:OcrBaseService
    {
        public readonly Client _client;
        public RecognizeBaseService(string endpoint, string accessKeyId, string accessSecret)
        {
            Config config = new Config
            {
                AccessKeyId = accessKeyId,
                AccessKeySecret = accessSecret,
                Endpoint = endpoint
            };
            _client = new Client(config);
        }
    }
    public class RecognizeData
    {
        [JsonPropertyName("prism_wnum")]
        public int PrismWnum { get; set; }

        [JsonPropertyName("content")]
        public string Content { get; set; } = null!;

        [JsonPropertyName("prism_wordsInfo")]
        public List<RecognizeWordInfo> Words { get; set; } = new List<RecognizeWordInfo>();
    }
    public class RecognizeWordInfo
    {
        [JsonPropertyName("prob")]
        public double Prob { get; set; }

        [JsonPropertyName("x")]
        public int X { get; set; } = 0;

        [JsonPropertyName("y")]
        public int Y { get; set; } = 0;

        [JsonPropertyName("width")]
        public int Width { get; set; } = 0;

        [JsonPropertyName("height")]
        public int Height { get; set; } = 0;

        [JsonPropertyName("word")]
        public string Word { get; set; } = null!;

    }
}

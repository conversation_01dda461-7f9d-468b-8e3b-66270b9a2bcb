using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using AlibabaCloud.SDK.Ocr_api20210707.Models;
using AlibabaCloud.TeaUtil.Models;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Tea;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Aliyun.Services
{
    /// <summary>
    /// 全文识别高精版
    /// </summary>
    public class RecognizeAdvancedService : RecognizeBaseService, IOcrRecognizeService
    {
        public RecognizeAdvancedService(string endpoint, string accessKeyId, string accessSecret) : base(endpoint, accessKeyId, accessSecret) { }

        public async Task<OcrResponse> Execute(OcrRequest body)
        {
            OcrResponse ocrResponse = new OcrResponse();
            using MemoryStream stream = body.memoryStream != null && body.memoryStream.Length > 0 ? body.memoryStream : new MemoryStream(body.Body);
            stream.Position = 0;
            if (stream.Length == 0)
            {
                Console.WriteLine("Either Body must be provided.");
                return await Task.FromResult(ocrResponse);
            }
            RecognizeAdvancedRequest recognizeAdvancedRequest = new RecognizeAdvancedRequest
            {
                Body = stream,
                NeedRotate = true
            };
            RuntimeOptions runtime = new RuntimeOptions();
            try
            {
                var response = _client.RecognizeAdvancedWithOptions(recognizeAdvancedRequest, runtime);
                ProcessOcrRequest(response, ocrResponse);
            }
            catch (TeaException error)
            {
                ocrResponse.Content = error.Message;
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
                ThrowOcrException(error.StatusCode, error.Message, body.ocrCode, ModelInstanceTypeEnum.AliOCR);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                ocrResponse.Content = _error.Message;
                Console.WriteLine(error.Message);
                Console.WriteLine(error.Data["Recommend"]);
                Console.WriteLine(_error.InnerException?.StackTrace);
                Console.WriteLine(_error.StackTrace);
            }
            return await Task.FromResult(ocrResponse);
        }
        public void ProcessOcrRequest(RecognizeAdvancedResponse response, OcrResponse ocrResponse)
        {
            ocrResponse.Success = response.StatusCode == 200;
            if (ocrResponse.Success && response?.Body?.Data != null)
            {
                var data = JsonSerializer.Deserialize<RecognizeData>(response.Body.Data);
                ocrResponse.Content = data.Content;
                ocrResponse.WordsResult = data.Words.Select(x => new WordsResult
                {
                    Prob = x.Prob,
                    Words = x.Word,
                    Location = new Location
                    {
                        Left = x.X,
                        Top = x.Y,
                        Height = x.Height,
                        Width = x.Width
                    }
                }).ToList();
                ocrResponse.WordsResultNum = ocrResponse.WordsResult.Count;
            }
        }
    }
}

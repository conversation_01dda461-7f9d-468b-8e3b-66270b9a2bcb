// Copyright (c) Microsoft. All rights reserved.

using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Mysoft.GPTEngine.SemanticKernel.Core.Diagnostics;
using Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Aliyun.Services;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Ocr.Aliyun
{
    /// <summary>
    /// Provides extension methods for the <see cref="IKernelBuilder"/> class to configure Hugging Face connectors.
    /// </summary>
    public static class AliyunKernelBuilderExtensions
    {
        public static IKernelBuilder AddAliyunOcrRecognize(
            this IKernelBuilder builder,
            string model,
            string endpoint,
            string accessKeyId,
            string accessSecret,
            string serviceId)
        {
            Verify.NotNull(builder);
            Verify.NotNull(endpoint);

            builder.Services.AddKeyedSingleton<IOcrRecognizeService>(serviceId, (serviceProvider, _) =>
            {
                switch (model.Replace("-latest", ""))
                {
                    case OcrRecognizeConst.Ali_RecognizeHandwriting:
                        return new RecognizeHandwritingService(endpoint, accessKeyId, accessSecret);
                    case OcrRecognizeConst.Ali_RecognizeInvoice:
                        return new RecognizeInvoiceService(endpoint, accessKeyId, accessSecret);
                    case OcrRecognizeConst.Ali_RecognizeVl:
                        return new RecognizeVlService(endpoint, model, accessSecret);
                    default:
                        return new RecognizeAdvancedService(endpoint, accessKeyId, accessSecret);
                }
            });
            return builder;
        }
    }
}

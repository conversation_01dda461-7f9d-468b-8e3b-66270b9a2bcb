using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Services;
using Mysoft.GPTEngine.SemanticKernel.Core.Http;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Services
{
    public class MysoftTextEmbeddingGenerationService : ITextEmbeddingGenerationService
    {

        private Dictionary<string, object?> AttributesInternal { get; } = new Dictionary<string, object?>();
        private MysoftClient Client { get; }

        /// <inheritdoc />
        public IReadOnlyDictionary<string, object?> Attributes => this.AttributesInternal;

        public MysoftTextEmbeddingGenerationService(
            string model,
            string? deploymentName,
            Uri? endpoint = null,
            string? apiKey = null,
            string? clientId = null,
            string? vendor = null,
            HttpClient? httpClient = null,
            ILoggerFactory? loggerFactory = null)
        {
            this.Client = new MysoftClient(modelId: model, deploymentName: deploymentName, endpoint: endpoint ?? httpClient?.BaseAddress, apiKey: apiKey, clientId: clientId, vendor:vendor, httpClient: HttpClientProvider.GetHttpClient(httpClient), logger: loggerFactory?.CreateLogger(this.GetType())
            );
            this.AttributesInternal.Add(AIServiceExtensions.ModelIdKey, model);
        }

        public Task<IList<ReadOnlyMemory<float>>> GenerateEmbeddingsAsync(IList<string> data, Kernel? kernel = null, CancellationToken cancellationToken = default)
            => this.Client.GenerateEmbeddingsAsync(data, kernel, cancellationToken);
    }
}

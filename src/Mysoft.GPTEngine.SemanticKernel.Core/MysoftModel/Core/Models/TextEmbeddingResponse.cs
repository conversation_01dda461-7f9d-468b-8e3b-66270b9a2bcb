using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Core.Models
{
    internal sealed class TextEmbeddingResponse
    {
        public string model { get; set; }

        public int created { get; set; }
        
        [JsonPropertyName("usage")]
        [JsonRequired]
        public EmbeddingUsage Usage { get; set; } = null!;

        public List<EmbeddingOutputs> data { get; set; }
    }

    internal sealed class EmbeddingOutputs
    {
        [JsonPropertyName("embedding")]
        [JsonRequired]
        public List<float> embedding { get; set; }

        public int index { get; set; }
    }
    
    internal sealed class EmbeddingUsage
    {
        [JsonPropertyName("total_tokens")]
        public int TotalTokens { get; set; }
    }

 
}

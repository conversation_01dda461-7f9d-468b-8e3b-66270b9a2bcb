using System;
using System.Net.Http;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy.impl
{
    public class SHENDUStrategy : IRedirectStrategy
    {
        public void RedirectUri(HttpRequestMessage request, CustomRedirectHttpDto customRedirectHttpDto)
        {
            Uri uri = new Uri(customRedirectHttpDto.Endpoint);
            Uri originalUri = request.RequestUri;
            request.RequestUri = new UriBuilder(originalUri)
            {
                Host = uri.Host,
                Scheme = originalUri.Scheme,
                Path = uri.AbsolutePath
            }.Uri;
        }

        public void RedirectHeaders(HttpRequestMessage request, CustomRedirectHttpDto customRedirectHttpDto)
        {
            // 这里可以根据需要添加Zhipu相关的请求头
        }
    }
}
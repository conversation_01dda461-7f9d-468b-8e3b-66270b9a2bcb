using System;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy.impl
{
    public class MysoftAIStrategy : IRedirectStrategy
    {
        public void RedirectUri(HttpRequestMessage request, CustomRedirectHttpDto customRedirectHttpDto)
        {
            //+ "?model=" + customRedirectHttpDto.DeploymentName
            Uri uri = new Uri(customRedirectHttpDto.Endpoint);
            Uri originalUri = request.RequestUri;

            string newQueryParam = "?model=" + customRedirectHttpDto.DeploymentName;
            // 检查原始查询字符串是否为空，以决定是否需要添加"&"
            string query = string.IsNullOrEmpty(uri.Query) ? newQueryParam : uri.Query.TrimStart('?') + "&" + newQueryParam;

            request.RequestUri = new UriBuilder(originalUri)
            {
                Host = uri.Host,
                Scheme = originalUri.Scheme,
                Path = uri.AbsolutePath,
                Query = query
            }.Uri;
        }

        public void RedirectHeaders(HttpRequestMessage request, CustomRedirectHttpDto customRedirectHttpDto)
        {
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            request.Headers.Add("x-vision-api-key", customRedirectHttpDto.ClientID);
            request.Headers.Add("x-vision-timestamp", currentTime.ToString());
            var mysoftSign = CreateSign(request.Content.ReadAsStringAsync().Result, currentTime, customRedirectHttpDto.ClientID, customRedirectHttpDto.ApiKey);
            request.Headers.Add("x-vision-sign", mysoftSign);
            request.Headers.Add("x-vision-vendor", customRedirectHttpDto.Vendor);
        }

        private string CreateSign(string content, long currentTime, string accessKey, string accessSecret)
        {
            var data = $"{accessKey}{accessSecret}{currentTime}{CalculateMD5(content)}";
            return CalculateMD5(data);
        }

        private static string CalculateMD5(string input)
        {
            using (var md5Hash = MD5.Create())
            {
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(input));
                StringBuilder sBuilder = new StringBuilder();
                foreach (var t in data)
                {
                    sBuilder.Append(t.ToString("x2"));
                }
                return sBuilder.ToString();
            }
        }
    }
}
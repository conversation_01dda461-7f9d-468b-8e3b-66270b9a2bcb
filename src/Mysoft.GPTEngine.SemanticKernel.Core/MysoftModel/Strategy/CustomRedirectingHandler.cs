using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy.impl;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy
{
    public class CustomRedirectingHandler : DelegatingHandler
    {
        private readonly IRedirectStrategy _strategy;

        private readonly CustomRedirectHttpDto _customRedirectHttpDto;

        public CustomRedirectingHandler(CustomRedirectHttpDto customRedirectHttpDto) : base(new HttpClientHandler()) // 正确调用基类构造函数
        {
            if (customRedirectHttpDto.ModelType == ModelTypeEnum.MYSOFTAI)
            {
                _strategy = new MysoftAIStrategy();
            }
            else if (customRedirectHttpDto.ModelType == ModelTypeEnum.ZHIPU)
            {
                _strategy = new ZhipuStrategy();
            }
            else if (customRedirectHttpDto.ModelType == ModelTypeEnum.SHENDU)
            {
                _strategy = new SHENDUStrategy();
            }
            _customRedirectHttpDto = customRedirectHttpDto;
        }

        protected override async Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request, CancellationToken cancellationToken)
        {
            if (request.RequestUri.ToString().IndexOf(".aliyuncs.com") == -1)
            {
                _strategy.RedirectUri(request, _customRedirectHttpDto);
                _strategy.RedirectHeaders(request, _customRedirectHttpDto);
            }
            return await base.SendAsync(request, cancellationToken);
        }

    }
}
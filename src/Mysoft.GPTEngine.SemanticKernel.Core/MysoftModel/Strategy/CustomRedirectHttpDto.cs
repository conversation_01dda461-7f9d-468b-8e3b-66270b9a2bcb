using System;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;

namespace Mysoft.GPTEngine.SemanticKernel.Core.MysoftModel.Strategy
{
    public class CustomRedirectHttpDto
    {
        /// <summary>
        /// 模型实例编码
        /// </summary>
        public string InstanceCode { get; set; }
        /// <summary>
        /// 模型实例名称
        /// </summary>
        public string InstanceName { get; set; }
        /// <summary>
        /// 模型GUID
        /// </summary>
        public Guid ModelGUID { get; set; }
        /// <summary>
        /// 模型类型
        /// </summary>
        public ModelTypeEnum ModelType { get; set; }
        /// <summary>
        /// 终结点
        /// </summary>
        public string Endpoint { get; set; }
        /// <summary>
        /// 部署名称
        /// </summary>
        public string DeploymentName { get; set; }
        /// <summary>
        /// 密钥
        /// </summary>
        public string ApiKey { get; set; }

        /// <summary>
        /// clientID 百度模型使用
        /// </summary>
        public string ClientID { get; set; }

        public string Vendor { get; set; }
    }
}
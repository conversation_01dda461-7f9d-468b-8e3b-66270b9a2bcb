namespace Mysoft.GPTEngine.Plugin.ContentReview
{
    public class ContentReviewResult
    {
        public bool IsSuccess { get; set; }

        public string Message { get; set; } = "内容审查不合规";

        public static ContentReviewResult Success()
        {
            var contentReviewResult = new ContentReviewResult();
            contentReviewResult.IsSuccess = true;
            return contentReviewResult;
        }
        
        public static ContentReviewResult Failed(string msg)
        {
            var contentReviewResult = new ContentReviewResult();
            contentReviewResult.IsSuccess = false;
            contentReviewResult.Message = msg;
            return contentReviewResult;
        }
    }
}
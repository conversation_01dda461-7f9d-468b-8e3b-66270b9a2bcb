using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using Mysoft.GPTEngine.SemanticKernel.Core.ContentReview;
using Newtonsoft.Json;
using RestSharp;

namespace Mysoft.GPTEngine.Plugin.ContentReview
{
    public class BaiduContentReview : IContentReview
    {
        public string Source() => CheckSourceEnum.BaiDu.ToString();
        
        const string ConfigEndpoint = "gptbuilder_check_baidu_endpoint";
        const string ConfigApiKey = "gptbuilder_check_baidu_api_key";
        const string ConfigSecretKey = "gptbuilder_check_baidu_secret_key";
        const string ConfigStrategyIdKey = "gptbuilder_check_baidu_strategy_id";
        
        public string Endpoint { get; set; }
        public string ApiKey { get; set; }

        public string SecretKey { get; set; }
        
        public string StrategyId { get; set; }
        
        public BaiduContentReview(string endpoint, string apiKey, string secretKey, string strategyId)
        {
            this.Endpoint = endpoint;
            this.ApiKey = apiKey;
            this.SecretKey = secretKey;
            this.StrategyId = strategyId;
        }
        
        public ContentReviewResult Check(string content)
        {
            //创建configMap
            var configMap = new Dictionary<string, string>()
            {
                {ConfigEndpoint, Endpoint},
                {ConfigApiKey, ApiKey},
                {ConfigSecretKey, SecretKey},
                {ConfigStrategyIdKey, StrategyId}
            };
            return Check(content, configMap);
        }

        public ContentReviewResult Check(string content, Dictionary<string, string> configMap)
        {
            string endpoint = configMap[ConfigEndpoint] == null ? "https://aip.baidubce.com" : configMap[ConfigEndpoint];
            var client = new RestClient($"{endpoint}/rest/2.0/solution/v1/text_censor/v2/user_defined?access_token={GetAccessToken(configMap)}");

            client.Timeout = 5000;

            var request = new RestRequest(Method.POST);

            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");

            request.AddHeader("Accept", "application/json");

            request.AddParameter("text", content);
            
            request.AddParameter("strategyId", configMap[ConfigStrategyIdKey]);
            
            var response = client.Execute(request);

            if (response.StatusCode != HttpStatusCode.OK) return ContentReviewResult.Failed(response.Content);
           
            var responseResult = JsonConvert.DeserializeObject<ResponseResult>(response.Content);
            
            if (responseResult == null) return ContentReviewResult.Failed("内容审查服务结果异常：" + response.Content);

            if (responseResult.ConclusionType == 1) return ContentReviewResult.Success();

            if (responseResult.Data != null && responseResult.Data.Count > 0) return ContentReviewResult.Failed(FormatConclusionData(responseResult.Data));

            return ContentReviewResult.Failed(responseResult.ErrorMsg);
            
        }

        public List<string> GetAllConfigKey()
        {
            return new List<string>() { ConfigApiKey, ConfigSecretKey, ConfigStrategyIdKey };
        }

        private string FormatConclusionData(List<ResponseResultData> list)
        {
            // 使用 StringBuilder 拼接字符串
            var sb = new StringBuilder();
            for (var i = 0; i < list.Count; i++)
            {
                sb.Append(list[i].Msg);
                if (i < list.Count - 1)
                {
                    sb.Append(";");
                }
            }

            return sb.ToString();
        }
        
        
        static string GetAccessToken(Dictionary<string, string> configMap) {
            string endpoint = configMap[ConfigEndpoint] == null ? "https://aip.baidubce.com" : configMap[ConfigEndpoint];
            var client = new RestClient($"{endpoint}/oauth/2.0/token");

            client.Timeout = -1;

            var request = new RestRequest(Method.POST);

            request.AddParameter("grant_type", "client_credentials");

            request.AddParameter("client_id", configMap[ConfigApiKey]);

            request.AddParameter("client_secret", configMap[ConfigSecretKey]);

            IRestResponse response = client.Execute(request);

            Console.WriteLine(response.Content);

            var result = JsonConvert.DeserializeObject<dynamic>(response.Content);

            return result.access_token.ToString();
            
        }
    }

    class ResponseResult
    {
        [JsonProperty("conclusionType")]
        public int? ConclusionType { get; set; }

        [JsonProperty("error_code")]
        public int? ErrorCode { get; set; }
        
        [JsonProperty("error_msg")]
        public string ErrorMsg { get; set; } = "";
        
        [JsonProperty("data")]
        public List<ResponseResultData>? Data { get; set; }

    }

    class ResponseResultData
    {
        [JsonProperty("msg")]
        public string? Msg { get; set; }
    }
}
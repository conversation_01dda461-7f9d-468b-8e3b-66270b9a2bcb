using System.Collections.Generic;

namespace Mysoft.GPTEngine.Plugin.ContentReview
{
    public interface IContentReview
    {
        /// <summary>
        /// 内容审查工具来源名称：
        /// </summary>
        /// <returns></returns>
        public string Source();
        
        /// <summary>
        /// 内容审查
        /// </summary>
        /// <param name="content"></param>
        /// <param name="configMap"></param>
        /// <returns></returns>
        public ContentReviewResult Check(string content);
        
        /// <summary>
        /// 内容审查
        /// </summary>
        /// <param name="content"></param>
        /// <param name="configMap"></param>
        /// <returns></returns>
        public ContentReviewResult Check(string content, Dictionary<string, string> configMap);
        
        /// <summary>
        /// 获取所有需要获取的业务参数的key
        /// </summary>
        /// <returns></returns>
        public List<string> GetAllConfigKey();

        
        
        

    }
}
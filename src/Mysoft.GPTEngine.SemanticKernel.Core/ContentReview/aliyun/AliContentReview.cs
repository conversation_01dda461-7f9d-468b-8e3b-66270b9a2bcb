using System;
using System.Collections.Generic;
using AlibabaCloud.OpenApiClient.Models;
using AlibabaCloud.SDK.Green20220302;
using AlibabaCloud.SDK.Green20220302.Models;
using AlibabaCloud.TeaUtil.Models;
using Mysoft.GPTEngine.SemanticKernel.Core.ContentReview;
using Newtonsoft.Json;

namespace Mysoft.GPTEngine.Plugin.ContentReview
{
    public class AliContentReview : IContentReview
    {
        public string Source() => CheckSourceEnum.Ali.ToString();

        const string ConfigEndpoint = "gptbuilder_check_ali_endpoint";
        const string ConfigAccessKeyId = "gptbuilder_check_ali_access_key_id";
        const string ConfigAccessKeySecret = "gptbuilder_check_ali_access_key_secret";
        const string ConfigServiceCode = "gptbuilder_check_ali_service_code";

        public string Endpoint { get; set; }
        public string AccessKeyId { get; set; }
        public string AccessKeySecret { get; set; }
        public string ServiceCode { get; set; }

        public AliContentReview(string endpoint, string accessKeyId, string accessKeySecret, string serviceCode)
        {
            this.Endpoint = endpoint;
            this.AccessKeyId = accessKeyId;
            this.AccessKeySecret = accessKeySecret;
            this.ServiceCode = serviceCode;
        }

        public ContentReviewResult Check(string content)
        {
            var configMap = new Dictionary<string, string>()
            {
                { ConfigEndpoint, Endpoint },
                { ConfigAccessKeyId, AccessKeyId },
                { ConfigAccessKeySecret, AccessKeySecret },
                { ConfigServiceCode, ServiceCode }
            };
            return Check(content, configMap);
        }

        public ContentReviewResult Check(string content, Dictionary<string, string> configMap)
        {
            TextModerationRequest textModerationRequest =
                new TextModerationRequest();
            textModerationRequest.Service = ServiceCode;
            Dictionary<string, object> task = new Dictionary<string, object>();
            // 待检测文本内容。
            task.Add("content", content);
            if (!task.ContainsKey("content") || Convert.ToString(task["content"]).Trim() == string.Empty)
            {
                return ContentReviewResult.Failed($"审核服务异常: ");
            }
            textModerationRequest.ServiceParameters = JsonConvert.SerializeObject(task);
            // 工程代码泄露可能会导致AccessKey泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的STS方式。
            Config config = new Config
            {
                AccessKeyId = configMap[ConfigAccessKeyId],
                AccessKeySecret = configMap[ConfigAccessKeySecret],
                Endpoint = configMap[ConfigEndpoint],
            };
            // 注意，此处实例化的client请尽可能重复使用，避免重复建立连接，提升检测性能。
            Client client = new Client(config);

            // 创建RuntimeObject实例并设置运行参数。
            RuntimeOptions runtime = new RuntimeOptions();
            runtime.ReadTimeout = 10000;
            runtime.ConnectTimeout = 10000;
            try
            {
                TextModerationResponse response =
                    client.TextModerationWithOptions(textModerationRequest, runtime);
                // 自动路由，服务端错误，区域切换至cn-beijing。
                if (response is null || response.Body is null ||
                    AlibabaCloud.TeaUtil.Common.EqualNumber(500,
                        AlibabaCloud.TeaUtil.Common.AssertAsNumber(response.StatusCode)) ||
                    AlibabaCloud.TeaUtil.Common.EqualString("500", Convert.ToString(response.Body.Code)))
                {
                    config.Endpoint = "green-cip.cn-beijing.aliyuncs.com";
                    client = new Client(config);
                    response = client.TextModerationWithOptions(textModerationRequest, runtime);
                }

                Console.WriteLine(JsonConvert.SerializeObject(response.Body));
                if (response?.Body.Code.ToString() != "200")
                {
                    return ContentReviewResult.Failed($"审核失败: {response?.Body.Message ?? "未知错误"}");
                }

                if (response?.Body.Data == null)
                {
                    return ContentReviewResult.Success();
                }
                var scanResult = response?.Body.Data;
                // 如果没有命中任何违规,则表示内容正常
                if (scanResult.Labels == null || scanResult.Labels == "")
                {
                    return ContentReviewResult.Success();
                }
                // 拼接所有违规原因
                var reasons = scanResult.Reason;
                return ContentReviewResult.Failed(reasons.ToString().TrimEnd(';'));
            }
            catch (Exception _err)
            {
                Console.WriteLine(_err);
                return ContentReviewResult.Failed($"审核服务异常: {_err.Message}");
            }
        }

        public List<string> GetAllConfigKey()
        {
            return new List<string>() { ConfigAccessKeyId, ConfigAccessKeySecret, ConfigServiceCode, ConfigEndpoint };
        }
    }
} 
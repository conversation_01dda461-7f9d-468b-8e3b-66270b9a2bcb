using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Parsers
{
    public partial class JsonOutputParsers : IJsonOutputParsers
    {
        public string Format(string output)
        {
            var jsonRegex = new Regex(@"\[([\s\S]*?)\]");
            MatchCollection matches = jsonRegex.Matches(output);
            return matches.FirstOrDefault() == null ? string.Empty : matches.First().Value;
        }

        /// <summary>
        /// 转换
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public List<string> StringArrayParse(string input)
        {
            try
            {
                var list = JsonSerializer.Deserialize<List<string>>(input);
                if(list != null) return list;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message + ":" + input);
            }
            var stringArrayRegex = new Regex(@"\[([\w\s\""\?？,]*)\]");

            List<string> resultList = new List<string>();

            MatchCollection matches = stringArrayRegex.Matches(input);

            if (matches.Count > 0)
            {
                string arrayString = matches.First().Value;
                if (string.IsNullOrWhiteSpace(arrayString))
                {
                    return resultList;
                }
                resultList = JsonSerializer.Deserialize<List<string>>(arrayString);
            }
            return resultList;

        }

    }
}

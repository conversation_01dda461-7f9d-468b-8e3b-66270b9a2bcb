using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Mysoft.GPTEngine.SemanticKernel.Core.Authentication
{
    /// <summary>
    /// Represents a delegate that defines the method signature for asynchronously authenticating an HTTP request.
    /// </summary>
    /// <param name="request">The <see cref="HttpRequestMessage"/> to authenticate.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    public delegate Task AuthenticateRequestAsyncCallback(HttpRequestMessage request, CancellationToken cancellationToken = default);
}

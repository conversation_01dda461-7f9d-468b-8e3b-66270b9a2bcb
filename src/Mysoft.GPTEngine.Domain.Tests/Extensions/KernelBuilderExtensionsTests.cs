using System;
using System.Reflection;
using Xunit;
using Mysoft.GPTEngine.Domain.Extensions;

namespace Mysoft.GPTEngine.Domain.Tests.Extensions
{
    /// <summary>
    /// Tests for KernelBuilderExtensions URL handling functionality
    /// </summary>
    public class KernelBuilderExtensionsTests
    {
        /// <summary>
        /// Tests the CombineUrlPath method using reflection since it's private
        /// </summary>
        /// <param name="baseUrl">Base URL to test</param>
        /// <param name="path">Path to combine</param>
        /// <param name="expected">Expected result</param>
        [Theory]
        [InlineData("https://dashscope.aliyuncs.com", "/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1")]
        [InlineData("https://dashscope.aliyuncs.com/", "/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1")]
        [InlineData("https://dashscope.aliyuncs.com//", "/compatible-mode/v1", "https://dashscope.aliyuncs.com/compatible-mode/v1")]
        [InlineData("https://api.openai.com", "v1/chat/completions", "https://api.openai.com/v1/chat/completions")]
        [InlineData("https://api.openai.com/", "v1/chat/completions", "https://api.openai.com/v1/chat/completions")]
        [InlineData("https://api.openai.com", "/v1/chat/completions", "https://api.openai.com/v1/chat/completions")]
        [InlineData("https://api.openai.com/", "/v1/chat/completions", "https://api.openai.com/v1/chat/completions")]
        [InlineData("", "/compatible-mode/v1", "/compatible-mode/v1")]
        [InlineData("https://dashscope.aliyuncs.com", "", "https://dashscope.aliyuncs.com")]
        [InlineData("", "", "")]
        [InlineData(null, "/compatible-mode/v1", "/compatible-mode/v1")]
        [InlineData("https://dashscope.aliyuncs.com", null, "https://dashscope.aliyuncs.com")]
        public void CombineUrlPath_ShouldCombineUrlsCorrectly(string baseUrl, string path, string expected)
        {
            // Arrange
            var type = typeof(KernelBuilderExtensions);
            var method = type.GetMethod("CombineUrlPath", BindingFlags.NonPublic | BindingFlags.Static);
            
            // Act
            var result = (string)method.Invoke(null, new object[] { baseUrl, path });
            
            // Assert
            Assert.Equal(expected, result);
        }

        /// <summary>
        /// Test specific case that was causing the 404 error
        /// </summary>
        [Fact]
        public void CombineUrlPath_ShouldFixDoubleSlashIssue()
        {
            // Arrange
            var type = typeof(KernelBuilderExtensions);
            var method = type.GetMethod("CombineUrlPath", BindingFlags.NonPublic | BindingFlags.Static);
            string baseUrl = "https://dashscope.aliyuncs.com/";
            string path = "/compatible-mode/v1";
            string expected = "https://dashscope.aliyuncs.com/compatible-mode/v1";
            
            // Act
            var result = (string)method.Invoke(null, new object[] { baseUrl, path });
            
            // Assert
            Assert.Equal(expected, result);
            Assert.DoesNotContain("//compatible-mode", result); // Ensure no double slash
        }

        /// <summary>
        /// Test edge cases for URL combination
        /// </summary>
        [Theory]
        [InlineData("https://example.com///", "///path///", "https://example.com/path///")]
        [InlineData("https://example.com", "path/to/resource", "https://example.com/path/to/resource")]
        [InlineData("https://example.com/", "path/to/resource", "https://example.com/path/to/resource")]
        public void CombineUrlPath_ShouldHandleEdgeCases(string baseUrl, string path, string expected)
        {
            // Arrange
            var type = typeof(KernelBuilderExtensions);
            var method = type.GetMethod("CombineUrlPath", BindingFlags.NonPublic | BindingFlags.Static);
            
            // Act
            var result = (string)method.Invoke(null, new object[] { baseUrl, path });
            
            // Assert
            Assert.Equal(expected, result);
        }
    }
}

using System;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using Mysoft.GPTEngine.Domain.Extensions;
using Xunit;

namespace Mysoft.GPTEngine.Domain.Tests.Extensions
{
    /// <summary>
    /// Tests for MultimodalHttpHandler functionality
    /// </summary>
    public class MultimodalHttpHandlerTests
    {
        private readonly Mock<ILogger<MultimodalHttpHandler>> _mockLogger;
        private readonly Mock<HttpMessageHandler> _mockInnerHandler;

        public MultimodalHttpHandlerTests()
        {
            _mockLogger = new Mock<ILogger<MultimodalHttpHandler>>();
            _mockInnerHandler = new Mock<HttpMessageHandler>();
        }

        [Fact]
        public async Task SendAsync_WithQwenVlOcrLatestModel_ShouldKeepStringFormat()
        {
            // Arrange
            var requestJson = @"{
                ""model"": ""qwen-vl-ocr-latest"",
                ""messages"": [
                    {
                        ""role"": ""system"",
                        ""content"": ""你是一个智能助手""
                    },
                    {
                        ""role"": ""user"",
                        ""content"": ""你好""
                    }
                ]
            }";

            var handler = new TestableMultimodalHttpHandler(_mockInnerHandler.Object, _mockLogger.Object);

            // Act
            var convertedContent = await handler.TestConvertToOpenAIMultimodalFormat(requestJson);

            // Assert - qwen-vl-ocr-latest should keep string format, not convert to array
            Assert.Equal(requestJson, convertedContent);
            Assert.DoesNotContain(@"""type"":""text""", convertedContent);
        }

        [Fact]
        public async Task SendAsync_WithQwenVlPlusModel_ShouldConvertTextToArray()
        {
            // Arrange
            var requestJson = @"{
                ""model"": ""qwen-vl-plus"",
                ""messages"": [
                    {
                        ""role"": ""user"",
                        ""content"": ""这张图片里有什么？""
                    }
                ]
            }";

            var handler = new TestableMultimodalHttpHandler(_mockInnerHandler.Object, _mockLogger.Object);

            // Act
            var convertedContent = await handler.TestConvertToOpenAIMultimodalFormat(requestJson);

            // Assert
            Assert.NotEqual(requestJson, convertedContent);
            Assert.Contains(@"""type"":""text""", convertedContent);
            Assert.Contains(@"""text"":""这张图片里有什么？""", convertedContent);
        }

        [Fact]
        public async Task SendAsync_WithNonMultimodalModel_ShouldNotConvert()
        {
            // Arrange
            var requestJson = @"{
                ""model"": ""gpt-3.5-turbo"",
                ""messages"": [
                    {
                        ""role"": ""user"",
                        ""content"": ""你好""
                    }
                ]
            }";

            var handler = new TestableMultimodalHttpHandler(_mockInnerHandler.Object, _mockLogger.Object);

            // Act
            var convertedContent = await handler.TestConvertToOpenAIMultimodalFormat(requestJson);

            // Assert
            Assert.Equal(requestJson, convertedContent);
        }

        [Fact]
        public async Task SendAsync_WithQwen2VlModel_ShouldConvertTextToArray()
        {
            // Arrange
            var requestJson = @"{
                ""model"": ""qwen2-vl-7b-instruct"",
                ""messages"": [
                    {
                        ""role"": ""system"",
                        ""content"": ""你是一个智能助手""
                    },
                    {
                        ""role"": ""user"",
                        ""content"": ""你好""
                    }
                ]
            }";

            var handler = new TestableMultimodalHttpHandler(_mockInnerHandler.Object, _mockLogger.Object);

            // Act
            var convertedContent = await handler.TestConvertToOpenAIMultimodalFormat(requestJson);

            // Assert - Other multimodal models should convert to array format
            Assert.NotEqual(requestJson, convertedContent);
            Assert.Contains(@"""type"":""text""", convertedContent);
            Assert.Contains(@"""text"":""你是一个智能助手""", convertedContent);
            Assert.Contains(@"""text"":""你好""", convertedContent);
        }

        [Fact]
        public async Task SendAsync_WithExistingArrayContent_ShouldNotDoubleConvert()
        {
            // Arrange
            var requestJson = @"{
                ""model"": ""qwen-vl-plus"",
                ""messages"": [
                    {
                        ""role"": ""user"",
                        ""content"": [
                            {
                                ""type"": ""text"",
                                ""text"": ""这张图片里有什么？""
                            },
                            {
                                ""type"": ""image_url"",
                                ""image_url"": {
                                    ""url"": ""https://example.com/image.jpg""
                                }
                            }
                        ]
                    }
                ]
            }";

            var handler = new TestableMultimodalHttpHandler(_mockInnerHandler.Object, _mockLogger.Object);

            // Act
            var convertedContent = await handler.TestConvertToOpenAIMultimodalFormat(requestJson);

            // Assert - Should remain the same since it's already in correct format
            Assert.Equal(requestJson, convertedContent);
        }

        [Theory]
        [InlineData("qwen-vl-ocr-latest", true)]
        [InlineData("qwen-vl-plus", true)]
        [InlineData("qwen-vl-max", true)]
        [InlineData("qwen-vl-ocr", true)]
        [InlineData("qwen2-vl-7b-instruct", true)]
        [InlineData("qwen2-vl-72b-instruct", true)]
        [InlineData("gpt-3.5-turbo", false)]
        [InlineData("gpt-4", false)]
        [InlineData("claude-3", false)]
        [InlineData("", false)]
        [InlineData(null, false)]
        public void IsMultimodalModel_ShouldDetectCorrectly(string modelName, bool expected)
        {
            // Arrange
            var handler = new TestableMultimodalHttpHandler(_mockInnerHandler.Object, _mockLogger.Object);

            // Act
            var result = handler.TestIsMultimodalModel(modelName);

            // Assert
            Assert.Equal(expected, result);
        }
    }

    /// <summary>
    /// Testable version of MultimodalHttpHandler that exposes private methods for testing
    /// </summary>
    public class TestableMultimodalHttpHandler : MultimodalHttpHandler
    {
        public TestableMultimodalHttpHandler(HttpMessageHandler innerHandler, ILogger<MultimodalHttpHandler> logger)
            : base(innerHandler, logger)
        {
        }

        public async Task<string> TestConvertToOpenAIMultimodalFormat(string jsonContent)
        {
            // Use reflection to call the private method
            var method = typeof(MultimodalHttpHandler).GetMethod("ConvertToOpenAIMultimodalFormat", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (string)method.Invoke(this, new object[] { jsonContent });
        }

        public bool TestIsMultimodalModel(string modelName)
        {
            // Use reflection to call the private method
            var method = typeof(MultimodalHttpHandler).GetMethod("IsMultimodalModel", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (bool)method.Invoke(this, new object[] { modelName });
        }
    }
}
